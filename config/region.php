<?php

return [
    'master' => env('APP_REGION_MASTER', 'sg'),
    'region_order_shard_id' => env('REGION_ORDER_SHARD_ID', 40),
    'regions' => [
        /**
         * Lưu ý quan trọng:
         *
         * <PERSON>hông được phép thay đổi lại ID của region sau khi đã đưa vào production.
         * V<PERSON> các đơn hàng đã được tạo dựa trên ID của các region này, đồng thời mã đơn hàng sẽ unique theo region
         */

        'sg' => [
            'id' => 01,
            'name' => 'sg',
            'database_connection' => env('DB_REGION_SG_CONNECTION', 'mysql_sg'),
            'url' => env('APP_REGION_SG_URL', ''),
            'auth_key' => env('APP_REGION_SG_AUTH_KEY', ''),
        ],
        'us' => [
            'id' => 02,
            'name' => 'us',
            'database_connection' => env('DB_REGION_US_CONNECTION', 'mysql_us'),
            'url' => env('APP_REGION_US_URL', ''),
            'auth_key' => env('APP_REGION_US_AUTH_KEY', ''),
        ],
         'eu' => [
             'id' => 03,
             'name' => 'eu',
             'database_connection' => env('DB_REGION_EU_CONNECTION', 'mysql_eu'),
             'url' => env('APP_REGION_EU_URL', ''),
             'auth_key' => env('APP_REGION_EU_AUTH_KEY', ''),
         ],
    ]
];
