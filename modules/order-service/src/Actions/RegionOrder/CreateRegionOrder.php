<?php

namespace Modules\OrderService\Actions\RegionOrder;

use App\Actions\Storefront\Order\VerifyFraud;
use App\Enums\CacheKeys;
use App\Enums\CampaignPublicStatusEnum;
use App\Enums\DesignByEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\FulfillMappingEnum;
use App\Enums\MarketLocationEnum;
use App\Enums\OrderFraudStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderProductLogEnum;
use App\Enums\OrderSenFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PricingModeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ShippingRuleType;
use App\Http\Controllers\SystemConfigController;
use App\Models\FulfillMapping;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\Template;
use App\Models\User;
use App\Services\CampaignService;
use App\Services\OrderService;
use App\Services\Recaptcha;
use App\Services\StoreService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use LogicException;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\OrderService\Exceptions\BadDataException;
use Modules\OrderService\Helpers\OrderHelper;
use Modules\OrderService\Helpers\OrderNumberManager;
use Modules\OrderService\Jobs\DatabaseSync\SyncOrderJob;
use Modules\OrderService\Models\RegionOrderHistory;
use Modules\OrderService\Models\RegionOrderProducts;
use Modules\OrderService\Models\RegionOrders;
use Modules\OrderService\Services\RegionOrderService;
use Modules\OrderService\Traits\UpdatingRegionOrderAttributesTrait;

class CreateRegionOrder
{
    use UpdatingRegionOrderAttributesTrait;

    private $regionName;
    private $storeModel;
    private VerifyFraud $verifyFraud;

    public function __construct(VerifyFraud $verifyFraud)
    {
        $this->verifyFraud = $verifyFraud;
    }

    /**
     * @throws \Throwable
     */
    public function handle(Request $request): string
    {
        $this->regionName = config('app.region');
        $storeInfo = StoreService::getCurrentStoreInfo();
        if (!$storeInfo) {
            throw new LogicException('Store is not found');
        }
        $ipAddress = getIp($request);
        $visitInfo = $request->post('visit_info');
        $visitInfo['version'] = $request->currentVersion();
        $sellerId = $visitInfo && isset($visitInfo['seller_id']) ? $visitInfo['seller_id'] : $storeInfo->seller_id;
        $seller = User::query()->find($sellerId);
        $deviceId = $visitInfo['device_id'] ?? null;
        $deviceDetail = $visitInfo['device_detail'] ?? null;
        $accessToken = $request->cookie('order_token');
        $ipLocation = strtoupper($request->cfIpCountry());
        $fraudStatus = OrderFraudStatus::TRUSTED;
        $flagLog = null;
        $captchaVerified = true;

        $result = $this->verifyFraud->handle(
            $sellerId,
            $ipAddress,
            $deviceId,
            $deviceDetail,
            true
        );

        switch (true) {
            case $result === VerifyFraud::IP_FRAUD:
            case $result === VerifyFraud::DEVICE_FRAUD:
                $fraudStatus = OrderFraudStatus::FRAUD;
                break;

            case $result === VerifyFraud::FLAG_CHECKOUT:
                $fraudStatus = OrderFraudStatus::FLAGGED;
                $flagLog = 'Checkout per device > 5, device id: ' . $deviceId . ', ip: ' . $ipAddress;
                break;

            case $result === VerifyFraud::FLAG_PAYMENT_RATE:
            case $result === VerifyFraud::CAPTCHA:
                $fraudStatus = OrderFraudStatus::FLAGGED;
                $flagLog = 'Too many checkout, device id: ' . $deviceId . ', ip: ' . $ipAddress;
                $captchaVerified = $result !== VerifyFraud::CAPTCHA;
                break;
        }

        if ($visitInfo && isset($visitInfo['ad_campaign']) && $visitInfo['ad_campaign'] === 'test_captcha') {
            $captchaVerified = false;
        }

        $captchaVerifiedKey = 'recaptcha_' . $accessToken;
        try {
            if (cache()->get($captchaVerifiedKey)) {
                $captchaVerified = true;
            }
        } catch (\Throwable $e) {
        }

        if (!$captchaVerified && $request->has('recaptcha_token')) {
            if (!Recaptcha::verify('storefront', $request->post('recaptcha_token'), $ipAddress)) {
                throw new LogicException('Captcha is invalid');
            }
            $captchaVerified = true;
            cache()->put($captchaVerifiedKey, true, CacheKeys::CACHE_1H);
        }

        if (!$captchaVerified && $request->header('user-agent') !== 'SenprintsTestBot') {
            throw new LogicException('need_verify');
        }
        $newOrder = null;
        $regionModel = RegionOrderService::regionOrderModelInstance(null, $this->regionName);
        /** @var Order|RegionOrders|Builder $orderQuery */
        $orderQuery = data_get($regionModel, 'order');
        /** @var OrderProduct|RegionOrderProducts|Builder $orderProductQuery */
        $orderProductQuery = data_get($regionModel, 'order_products');
        if (!empty($accessToken)) {
            $newOrder = $orderQuery
                ->selectForHistory([
                    'promotion_rule_id', //select for calculate order
                    'tip_amount',
                    'shipping_method',
                    'insurance_fee',
                    'country',
                    'seller_id',
                    'store_id',
                    'address', // select for check exclude shipping
                    'address_2',
                    'house_number',
                    'city',
                    'state',
                    'postcode',
                    'type'
                ])
                ->whereIn('status', [
                    OrderStatus::PENDING,
                    OrderStatus::DRAFT
                ])
                ->firstWhere([
                    'store_id' => $storeInfo->id,
                    'access_token' => $accessToken
                ]);
        }
        // check if we need to create new order
        $wasRecentlyCreated = false;
        if (is_null($newOrder)) {
            if (app()->environment('testing')) {
                $accessToken = 'sen_test_create_order';
            } else {
                $accessToken = OrderHelper::generateAccessToken($ipAddress);
            }

            $country = $request->post('country');
            if (empty($country) && !empty($visitInfo['country'])) {
                // check valid country
                $visitCountry = strtoupper($visitInfo['country']);
                $location = getLocationByCode($visitCountry);
                if ($location) {
                    $country = $visitCountry;
                }
            }
            if (empty($country)) {
                $location = getLocationByCode($ipLocation);
                if ($location) {
                    $country = $ipLocation;
                }
                $country ??= MarketLocationEnum::US;
            }

            // check if country is disabled
            $disableCountry = SystemConfig::getConfig('disable_country', '');
            if (!empty($disableCountry)) {
                $disableCountry = explode(',', $disableCountry);
                if (in_array($country, $disableCountry, true)) {
                    $country = MarketLocationEnum::US;
                }
            }
            DB::beginTransaction();
            try {
                $data = [
                    'access_token' => $accessToken,
                    'ip_address' => $ipAddress,
                    'ip_location' => $ipLocation,
                    'country' => $country,
                    'store_id' => $storeInfo->id,
                    'store_name' => $storeInfo->name,
                    'store_domain' => $storeInfo->currentDomain ?? StoreService::currentDomain($request),
                    'seller_id' => $storeInfo->seller_id,
                    'company_id' => $storeInfo->company_id ?? 1,
                    'type' => OrderTypeEnum::REGULAR,
                    'region' => $this->regionName,
                ];
                if (data_get($storeInfo, 'custom_payment', false)) {
                    $data['type'] = OrderTypeEnum::CUSTOM;
                }
                if ($visitInfo) {
                    try {
                        if ($storeInfo->id === Store::SENPRINTS_STORE_ID && isset($visitInfo['seller_id'])) {
                            $data['seller_id'] = $visitInfo['seller_id'];
                        }
                        if (isset($visitInfo['device'])) {
                            $data['device'] = strtolower($visitInfo['device']);
                        }
                        if (isset($visitInfo['device_detail'])) {
                            $data['device_detail'] = strtolower($visitInfo['device_detail']);
                        }
                        if (isset($visitInfo['device_id'])) {
                            $data['device_id'] = $visitInfo['device_id'];
                        }
                        $data['visit_info'] = json_encode($visitInfo, JSON_THROW_ON_ERROR);
                    } catch (\Throwable $e) {
                    }
                }
                $newOrder = $orderQuery->create($data);
                DB::commit();

            } catch (\Throwable $e) {
                logToDiscord(
                    'Exception: ' . $e->getMessage()
                    . "\nData: " . json_encode($data),
                    'error',
                    true
                );
                DB::rollBack();
                throw new LogicException('[1] Cannot create new order.');
            }

            if (!$newOrder->wasRecentlyCreated) {
                throw new LogicException('[2] Cannot create new order.');
            }

            // fresh to get data after create
            $newOrder = $newOrder->fresh();

            $wasRecentlyCreated = true;
            RegionOrderHistory::insertLog(
                $newOrder,
                OrderHistoryActionEnum::CREATED,
                '',
                OrderHistoryDisplayLevelEnum::CUSTOMER,
                $this->regionName
            );
            // log to graylog when created a new order
            try {
                graylogInfo('Created new order with ID: ' . $newOrder->id, [
                    'category' => 'created_order',
                    'order_id' => $newOrder->id,
                    'seller_id' => $newOrder->seller_id,
                    'seller_email' => $seller->email,
                    'access_token' => $newOrder->access_token,
                    'store_domain' => $newOrder->store_domain,
                    'store_type' => $newOrder->isCustomOrder() ? OrderTypeEnum::CUSTOM : OrderTypeEnum::REGULAR,
                    'order_region' => $newOrder->region,
                    'order_country' => $newOrder->country,
                    'store_version' => $request->currentVersion(),
                    'visit_info' => $newOrder->visit_info,
                ]);
            } catch (\Throwable $e) {}
        } else {
            // reset this filed, used for case abandoned sms & email
            $newOrder->created_at = now();
        }
        if (!$newOrder instanceof RegionOrders &&
            !$newOrder instanceof Order) {
            throw new LogicException('[3] Cannot create new order.');
        }
        if ($visitInfo) {
            if (isset($visitInfo['ad_source'])) {
                $newOrder->ad_source = $visitInfo['ad_source'];
            }

            if (isset($visitInfo['ad_campaign'])) {
                $newOrder->ad_campaign = $visitInfo['ad_campaign'];
            }

            if (isset($visitInfo['ad_medium'])) {
                $newOrder->ad_medium = $visitInfo['ad_medium'];
            }

            if (isset($visitInfo['ad_id'])) {
                $newOrder->ad_id = $visitInfo['ad_id'];
            }

            if (isset($visitInfo['session_id'])) {
                $newOrder->session_id = $visitInfo['session_id'];
            }
            $newOrder->visit_info = json_encode($visitInfo, JSON_THROW_ON_ERROR);
        }

        if (empty($newOrder->shipping_method)) {
            $newOrder->shipping_method = ShippingRuleType::STANDARD;
        }

        if ($visitInfo && isset($visitInfo['currency_code'])) {
            $currency = SystemConfigController::findOrDefaultCurrency($visitInfo['currency_code']);
            if ($currency) {
                $newOrder->currency_code = $currency->code;
                $newOrder->currency_rate = $currency->rate;
            }
        }


        $defaultTip = 0;
        if ($wasRecentlyCreated) {
            // set default insurance fee
            $newOrder->insurance_fee = 0;
            if ($storeInfo->enable_insurance_fee) {
                $newOrder->insurance_fee = Order::ORDER_INSURANCE_FEE;
            }
            // set default tip
            if ($storeInfo->show_tipping > 0) {
                $defaultTip = $storeInfo->default_tipping;
            }
        }

        // link products with order ID
        $orderId = $newOrder->id;

        // create collection from products
        $requestProducts = $request->post('products');
        $productIdsRequest = [];
        $listConnection = array_keys(config('database.connections'));
        foreach ($requestProducts as &$product) {
            if (empty($product['seller_id'])) {
                $product['seller_id'] = $storeInfo->seller_id;
            }
            if (isset($product['via']) && in_array($connection = str_replace('m', 'mysql_', $product['via']), $listConnection, true)) {
                $productIdsRequest[$connection][] = (int)$product['product_id'];
            } else {
                $productIdsRequest['mysql'][] = (int)$product['product_id'];
            }
        }
        unset($product);
        $productsDB = collect();
        // get products from DB to get price
        foreach ($productIdsRequest as $connection => $productIds) {
            $productsDB = $productsDB->merge(Product::query()
                ->filterActiveProduct()
                ->on($connection)
                ->whereKey($productIds)
                ->with([
                    'template:id,options,status',
                    'campaign:id,options,market_location',
                ])
                ->get());
        }
        $orderProducts = [];
        $errorProducts = [];
        $fulfillStatus = OrderFulfillStatus::UNFULFILLED;
        $newOrder->personalized = PersonalizedType::NONE;
        $noShipLogs = collect();
        $opIdx = 0;
        OrderService::handleOrderProductPromotionBundle($requestProducts);
        $isLawyerAddress = false;
        if ($newOrder->isCustomOrder()) {
            $isLawyerAddress = OrderService::isLawyerAddress($newOrder);
            $newOrder->sen_fulfill_status = OrderSenFulfillStatus::PENDING;
        }
        $sellerIds = collect($requestProducts)->pluck('seller_id')->filter()->unique()->toArray();
        $sellers = User::query()->select([
            'id',
            'custom_payment',
            'sharding_status',
            'db_connection'
        ])->whereIn('id', $sellerIds)->get();
        foreach ($requestProducts as $product) {
            /** @var Product $p */
            $p = $productsDB->first(
                function ($productDB) use (&$product) {
                    if ($productDB->id === (int) $product['product_id']) {
                        if (isset($product['seller_id']) && (int) $product['seller_id'] !== $productDB->seller_id) {
                            return null;
                        }
                        // match options
                        $logs = '';
                        $correctOptions = OrderService::correctOptions($product['options'], $productDB, $logs);
                        // ex: option = [ 'color' => 'red', 'size' => 's' ], optionDB = [ 'color' => ['red', 'blue'], 'size' => ['s', 'm'] ]
                        $product['options_db'] = json_decode($productDB->options, true, 512, JSON_THROW_ON_ERROR);
                        $product['default_option'] = $productDB->default_option;
                        unset($product);
                        if ($correctOptions !== false) {
                            $product['options'] = $correctOptions;
                            return $productDB;
                        }
                    }
                    return null;
                }
            );
            $sellerId = data_get($product, 'seller_id', null);
            if (!empty($sellerId)) {
                $seller = $sellers->find($sellerId);
            }
            if (!$p) {
                $errorProducts[] = $product;
                continue;
            }
            $templateProduct = Template::findAndCacheByKey((int)$p->template_id, false);
            if (!$templateProduct) {
                $errorProducts[] = $product;
                continue;
            }

            $productPrice = $p->price;
            $location = getLocationByCode($newOrder->country);
            $productBaseCost = getBaseCostsByLocation($templateProduct, $location);
            $productSku = $templateProduct->sku;
            $productFulfillStatus = OrderProductFulfillStatus::UNFULFILLED;
            $currency = SystemConfigController::findOrDefaultCurrency($p->currency_code);
            $currencyRate = $currency->rate;

            $dynamicBaseCostIndex = 0;
            // check variant price
            if (!empty($product['options'])) {
                // create variant key from options
                $variantKey = getVariantKey($product['options']);
                $variantKeyBaseSize = getBaseVariantFromOptions($product['options_db'], $product['options']['color'] ?? $product['default_option'] ?? 'white');
                $campLocation = getLocationByCode($p->market_location);
                $campRegionCodes = $campLocation ? $campLocation->getRegionCodes() : ['*'];
                $orderRegionCodes = $location ? $location->getRegionCodes() : ['*'];
                $existVariantKeyTM = false;
                $templateVariantOnOrderLocation = null;
                if ($seller && $seller->custom_payment) {
                    $variantKeyTM = $variantKey . '-tm';
                    $existVariantKeyTM = ProductVariant::findAndCacheByTemplate($templateProduct->id)->contains('variant_key', $variantKeyTM);
                }
                if (!$existVariantKeyTM) {
                    $templateVariantOnOrderLocation = ProductVariant::findAndCacheByTemplate($templateProduct->id)
                        ->filter(function ($each) use ($variantKey, $orderRegionCodes) {
                            return $each->variant_key === $variantKey && in_array($each->location_code, $orderRegionCodes);
                        })
                        ->sortBy(function ($each) use ($orderRegionCodes) {
                            return array_search($each['location_code'], $orderRegionCodes);
                        })
                        ->first();
                    if ($p->pricing_mode === PricingModeEnum::ADJUST_PRICE && $storeInfo->enable_dynamic_base_cost) {
                        $templateInfoOrderAndCampLocation = StoreService::getBaseVariantOnOrderAndCampLocation($templateProduct->id, $variantKeyBaseSize, $orderRegionCodes, $campRegionCodes);
                        OrderService::getDynamicBaseCostIndex($templateInfoOrderAndCampLocation['campaign_location'], $templateInfoOrderAndCampLocation['order_location'], $dynamicBaseCostIndex);
                    }
                    if ($templateVariantOnOrderLocation) {
                        $templateVariantOnOrderLocation->out_of_stock = $p->template?->status === ProductStatus::INACTIVE ? 1 : $templateVariantOnOrderLocation->out_of_stock;
                        // calculate product price
                        if ($p->pricing_mode === PricingModeEnum::ADJUST_PRICE) {
                            $productPrice += ceil($templateVariantOnOrderLocation->adjust_price * $currencyRate);
                        } else if ($p->pricing_mode === PricingModeEnum::CUSTOM_PRICE) {
                            $productVariant = ProductVariant::query()
                                ->onSellerConnection($seller)
                                ->select('price')
                                ->firstWhere([
                                    'variant_key' => $variantKey,
                                    'product_id' => $p->id,
                                ]);
                            if (!is_null($productVariant)) {
                                // set product price = variant price
                                $productPrice = $productVariant->price;
                            } else if (($templateVariantOnOrderLocation->price * $currencyRate) > $productPrice) {
                                $productPrice = $templateVariantOnOrderLocation->price * $currencyRate;
                            }
                        }
                        $productBaseCost = $templateVariantOnOrderLocation->base_cost;
                    }
                }
                $isExcludeShipping = FulfillMapping::checkExcludeShipping(
                    $location,
                    $templateProduct->id,
                );

                $reason = match (true) {
                    !$templateVariantOnOrderLocation => 'NOT_FOUND_VARIANT',
                    (bool)$templateVariantOnOrderLocation?->out_of_stock => 'OUT_OF_STOCK',
                    $isExcludeShipping => 'EXCLUDE_SHIPPING',
                    $isLawyerAddress => 'NOT_SHIPPING_TO_LAWYER_ADDRESS',
                    default => null,
                };

                if ($reason) {
                    $_noShipLog = [
                        'content' => $reason,
                        'type' => OrderProductLogEnum::NO_SHIP,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                    $productFulfillStatus = OrderProductFulfillStatus::NO_SHIP;
                    $fulfillStatus = OrderFulfillStatus::NO_SHIP;
                }
            }

            $customOptions = !empty($product['custom_options']) ? json_encode($product['custom_options'], JSON_THROW_ON_ERROR) : null;
            $extraCustomFee = 0;
            $commonExtraCustomFee = 0;
            $customer_custom_options = !empty($product['customer_custom_options']) ? collect($product['customer_custom_options']) : collect();
            $customer_custom_options = $customer_custom_options->filter(fn($item) => !empty($item))->values();
            if ($customer_custom_options->isNotEmpty() && (in_array($p->personalized, [PersonalizedType::CUSTOM_OPTION, PersonalizedType::NONE], true) || $p->full_printed === ProductPrintType::HANDMADE)) {
                $campaignExtraCustomFee = 0;
                $isCampaignMockupCustom = in_array($p->system_type, [ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::AI_MOCKUP], true);
                if (($p->personalized === PersonalizedType::CUSTOM_OPTION || $isCampaignMockupCustom) && !empty($p->campaign->options)) {
                    $decodedOptions = json_decode($p->campaign->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                    $campaignExtraCustomFee = !empty($decodedOptions['group']['extra_custom_fee']) ? (float)$decodedOptions['group']['extra_custom_fee'] : 0;
                    $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float)$decodedOptions['common_options']['extra_custom_fee'] : 0;
                } else if ($p->personalized === PersonalizedType::NONE && $p->full_printed === ProductPrintType::HANDMADE && !empty($p->template->options)) {
                    $decodedOptions = json_decode($p->template->options, true, 512, JSON_THROW_ON_ERROR) ?? [];
                    $campaignExtraCustomFee = !empty($decodedOptions['custom_options']['group']['extra_custom_fee']) ? (float)$decodedOptions['custom_options']['group']['extra_custom_fee'] : 0;
                    $commonExtraCustomFee = !empty($decodedOptions['common_options']['extra_custom_fee']) ? (float)$decodedOptions['common_options']['extra_custom_fee'] : 0;
                }
                $optionGroups = $customer_custom_options;
                $customOptions = $optionGroups->toJson();
                $onlyOptionGroups = $optionGroups->filter(function ($group) {
                    return collect($group)->where('g_type', 'common')->count() === 0;
                })->values();
                if ($p->full_printed === ProductPrintType::HANDMADE) {
                    // Sử dụng idx > 0 để miễn phí cho nhóm đầu tiên
                    $onlyOptionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                        if ($idx > 0) {
                            $group = collect($group);
                            if ($group->some(fn($option) => isset($option['value']) && $option['value'] !== '')) {
                                $extraCustomFee += (float)$campaignExtraCustomFee;
                            }
                            $group->each(function ($option) use (&$extraCustomFee) {
                                if (isset($option['value'], $option['price']) && $option['value'] !== '' && (float)$option['price'] > 0) {
                                    $extraCustomFee += (float)$option['price'];
                                }
                            });
                        }
                    });
                } else {
                    $onlyOptionGroups->each(function ($group, $idx) use ($campaignExtraCustomFee, &$extraCustomFee) {
                        if ($idx > 0 && collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== '')) {
                            $extraCustomFee += (float)$campaignExtraCustomFee;
                        }
                    });
                }
                $commonOptions = $optionGroups->filter(function ($group) {
                    return collect($group)->where('g_type', 'common')->count() > 0;
                })->values();
                if ($commonOptions->isNotEmpty() && $commonOptions->some(fn($group) => collect($group)->some(fn($option) => isset($option['value']) && $option['value'] !== ''))) {
                    $extraCustomFee += (float)$commonExtraCustomFee;
                }
            }

            $dynamicBaseCostIndexForPrice = $dynamicBaseCostIndex * $currencyRate;
            $dynamicBaseCostIndexForPrice = roundToHalf($dynamicBaseCostIndexForPrice);
            $dynamicBaseCostIndexRounded = $dynamicBaseCostIndexForPrice / $currencyRate;
            $productPrice += $extraCustomFee;
            $productPrice /= $currencyRate; // convert price to USD
            $extraCustomFee /= $currencyRate;
            $productBaseCost += $p->extra_print_cost;
            $productPrice += $dynamicBaseCostIndexRounded;

            $data = [
                'order_id' => $orderId,
                'product_id' => $p->id,
                'combo_id' => $product['combo_id'] ?? null,
                'campaign_id' => $product['campaign_id'],
                'campaign_title' => $product['campaign_title'] ?: null,
                'template_id' => $p->template_id,
                'product_name' => $p->name,
                'product_url' => $product['product_url'],
                'sku' => $productSku,
                'thumb_url' => $product['thumb_url'] ?? $p->thumb_url,
                'options' => !empty($product['options']) ? json_encode($product['options']) : null,
                'custom_options' => $customOptions,
                'base_cost' => $productBaseCost,
                'price' => $productPrice,
                'quantity' => $product['quantity'],
                'base_shipping_cost' => $templateProduct->shipping_cost,
                'fulfill_shipping_cost' => $templateProduct->shipping_cost,
                'seller_id' => $p->seller_id,
                'auth_id' => $p->auth_id,
                'personalized' => $p->personalized,
                'full_printed' => $templateProduct->full_printed,
                'fulfill_status' => $productFulfillStatus,
                'extra_custom_fee' => $extraCustomFee,
                'extra_print_cost' => $p->extra_print_cost,
                'dynamic_base_cost_index' => $dynamicBaseCostIndex,
                'dynamic_base_cost_index_rounded' => $dynamicBaseCostIndexRounded,
                'related_campaign_id' => Arr::get($product, 'related_campaign_id'),
                'related_product_id' => Arr::get($product, 'related_product_id'),
                'promotion_rule_id' => Arr::get($product, 'promotion_rule_id'),
                'campaign_type' => $p->system_type,
                'design_by' => $p->system_type === ProductSystemTypeEnum::AI_MOCKUP ? DesignByEnum::SENPRINTS : DesignByEnum::SELLER
            ];

            graylogInfo('PB Order ID: ' . $orderId . ' - Product ID: ' . $p->id, [
                'order_id' => $orderId,
                'product_id' => $p->id,
                'options' => $product['pbCustomInfo'] ?? null,
            ]);

            $orderProduct = new RegionOrderProducts($data);
            if (!empty($product['pbCustomInfo'])) {
                $orderProduct->putCustomOptionsPB([
                    'options' => $product['pbCustomInfo'],
                    'print_url' => $product['pbPrintUrl'] ?? null,
                ]);
                $orderProduct->personalized = PersonalizedType::PB;
            }

            // check if product have custom text
            if (!empty($product['designs'])) {
                $orderProduct['designs'] = $product['designs'];
            }

            if ($p->personalized) {
                $newOrder->personalized = $p->personalized;
            }

            $orderProduct['pricing_mode'] = $p->pricing_mode;
            $orderProduct['adjust_price'] = !empty($templateVariantOnOrderLocation) && $p->pricing_mode === PricingModeEnum::ADJUST_PRICE ? ceil($templateVariantOnOrderLocation->adjust_price) : 0;
            $orderProduct['variant_key'] = !empty($variantKey) ? $variantKey : null;
            if (isset($_noShipLog)) {
                $noShipLogs[$opIdx] = $_noShipLog;
            }
            $orderProducts[$opIdx++] = $orderProduct;

            // correct seller id if product is not public on marketplace
            if ($newOrder->seller_id === User::SENPRINTS_SELLER_ID && $p->public_status !== CampaignPublicStatusEnum::APPROVED) {
                $newOrder->seller_id = $p->seller_id;
            }
        }
        // check if we don't have any product
        if (empty($orderProducts)) {
            throw new BadDataException('All products are invalid.', $errorProducts);
        }
        // attach products to order and calculate
        $newOrder->products = collect($orderProducts);
        CampaignService::mappingCorrectPricing($newOrder->products, $request->input('correct_price'), $storeInfo->seller_id);
        $discountCode = $request->post('discount_code');
        if (empty($discountCode)) {
            $discountCode = $storeInfo->auto_apply_coupon;
        }
        if ($discountCode) {
            app(OrderService::class)->applyDiscount($newOrder, $discountCode, true);
        }

        $newOrder->fulfill_status = $fulfillStatus;
        try {
            if (!isset($location)) {
                $location = getLocationByCode($newOrder->country);
            }
            $newOrder->assignSupplier(source: Order::CHECKOUT_ASSIGN_SUPPLIER, isRegion: true, inCreatingOrder: true);
            $newOrder->calculateOrder($defaultTip);
            RegionOrderProducts::onRegion($this->regionName)->where('order_id', $orderId)->delete();
            $orderLocation = SystemLocation::mappingLocationWithOrder($location, $newOrder);
            $excludeMappings = FulfillMapping::filterByExcludeLocation(
                FulfillMappingEnum::SHIPPING_EXCLUDE_LOCATION,
                $orderLocation,
                $newOrder->shipping_method,
            );
            foreach ($orderProducts as $idx => $orderProduct) {
                $designFiles = [];
                $additionalAttributes = [];
                $designs = Arr::get($orderProduct, 'designs', []);
                unset($orderProduct['designs'], $orderProduct['template']);
                foreach ($designs as $printSpace => $designJson) {
                    $designData = [
                        'product_id' => $orderProduct->product_id,
                        'type' => DesignTypeEnum::PRINT,
                        'print_space' => $printSpace,
                        'design_json' => $designJson,
                    ];
                    $designFiles[] = $designData;
                }
                $orderProduct['custom_designs'] = null;
                if (!empty($designFiles)) {
                    $additionalAttributes['design_files'] = $designFiles;
                    $orderProduct['custom_designs'] = json_encode($designFiles, JSON_THROW_ON_ERROR);
                }

                $orderProduct = $orderProductQuery->create($orderProduct->makeHiddenAll()->toArray());
                $isSupplierExcluded = false;
                $noShipLog = $noShipLogs->get($idx, []);
                if (!empty($noShipLog) && !data_get($noShipLog, 'content')) {
                    $additionalAttributes['no_ship_logs'][] = $noShipLog;
                }
                if ($excludeMappings && $excludeMappings->count() > 0) {
                    $suppliers_id = $excludeMappings->pluck('supplier_id')->filter(fn($v) => !empty($v))->map(fn($v) => (int)$v)->values()->toArray();
                    $isSupplierExcluded = is_null($excludeMappings->first()->supplier_id) || (!empty($orderProduct->supplier_id) && !empty($suppliers_id) && in_array((int)$orderProduct->supplier_id, $suppliers_id, true));
                }

                if ($isSupplierExcluded) {
                    $newOrder->fulfill_status = OrderFulfillStatus::NO_SHIP;
                    $orderProduct->fulfill_status = OrderProductFulfillStatus::NO_SHIP;
                    $orderProduct->save();
                    $noShipLog = [
                        'content' => 'SUPPLIER_EXCLUDE_SHIPPING',
                        'type' => OrderProductLogEnum::NO_SHIP,
                        'created_at' => now(),
                        'updated_at' => now()
                    ];
                    $additionalAttributes['no_ship_logs'][] = $noShipLog;
                }
                $orderProduct->additional_attributes = json_encode($additionalAttributes, JSON_THROW_ON_ERROR);
                $orderProduct->save();
            }
            // remove products from object to prevent SQL error
            unset($newOrder->products, $orderProducts);
            $email = OrderHelper::getValidEmail($request->post('email'));

            if ($email) {
                $newOrder->customer_email = $email;
                $newOrder->status = OrderStatus::PENDING;
            }
            $newOrder->order_number = OrderNumberManager::make([
                'region_name' => $this->regionName,
                'region_order_id' => $newOrder->id,
                'prefix' => $storeInfo->order_prefix,
            ])->encode();

            $newOrder->fraud_status = $fraudStatus;
            if ($fraudStatus !== OrderFraudStatus::TRUSTED) {
                $newOrder->flag_log = $flagLog;
            }

            if ($fraudStatus === OrderFraudStatus::FRAUD) {
                RegionOrderHistory::insertLog(
                    $newOrder,
                    OrderHistoryActionEnum::CHANGE_FRAUD_STATUS,
                    'Changed fraud status from ' . Str::upper($newOrder->fraud_status ?? 'N/A') . ' to ' . Str::upper(OrderFraudStatus::FRAUD),
                    OrderHistoryDisplayLevelEnum::ADMIN,
                    $this->regionName
                );
            }
            $newOrder->save();
            graylogInfo('Create region order sync', [
                'category' => 'create_region_order_start_sync',
                'current_region' => $this->regionName,
                'master_region' => config('app.region_master'),
                'region_order_id' => $newOrder->id,
            ]);
            if ($this->regionName !== config('app.region_master')) {
                SyncOrderJob::dispatch($newOrder->getId(), $this->regionName)->onQueue('sync_order_region');
            }
            return $accessToken;
        } catch (\Throwable $e) {
            logException($e, '[2] Create order error');
            throw new LogicException($e->getMessage());
        }
    }
}
