<?php

namespace Modules\SellerAPI\Http\Resources;

use App\Models\File;
use App\Models\Order;
use App\Models\OrderProduct;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var Order $this */
        return [
            'id' => $this->id,
            'order_number' => $this->order_number_2,
            'store_name' => $this->store_name,
            'store_domain' => $this->store_domain,

            'customer_name' => $this->customer_name,
            'customer_email' => $this->customer_email,
            'customer_phone' => $this->customer_phone,
            'address' => $this->address,
            'address_2' => $this->address_2,
            'house_number' => $this->house_number,
            'mailbox_number' => $this->mailbox_number,
            'city' => $this->city,
            'state' => $this->state,
            'postcode' => $this->postcode,
            'country' => $this->country,
            'order_note' => $this->order_note,
            'shipping_label' => $this->shipping_label,
            'shipping_method' => $this->shipping_method,
            'cross_shipping' => $this->cross_shipping,
            'personalized' => $this->personalized,

            'status' => $this->status,
            'fulfill_status' => $this->fulfill_status,
            'type' => $this->type,
            'address_verified' => $this->address_verified,
            'fulfill_log' => $this->fulfill_log,

            'total_quantity' => $this->total_quantity,
            'total_product_amount' => $this->total_product_amount,
            'total_shipping_amount' => $this->total_shipping_amount,
            'total_amount' => $this->total_amount,
            'total_paid' => $this->total_paid,
            'total_product_cost' => $this->total_product_cost,
            'total_shipping_cost' => $this->total_shipping_cost,
            'total_seller_profit' => $this->total_seller_profit,
            'processing_fee' => $this->processing_fee,
            'total_fulfill_fee' => $this->total_fulfill_fee,
            'processing_fee_paid' => $this->processing_fee_paid,
            'fulfill_fee_paid' => $this->fulfill_fee_paid,

            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'paid_at' => $this->paid_at,
            'delivered_at' => $this->delivered_at,
            'received_at' => $this->received_at,

            'products' => $this->products->map(function (OrderProduct $product) {
                return [
                    'id' => $product->id,
                    'product_name' => $product->product_name,
                    'sku' => $product->sku,
                    'quantity' => $product->quantity,
                    'options' => $product->options,
                    'custom_options' => $product->custom_options,
                    'thumb_url' => $product->thumb_url,
                    'price' => $product->price,
                    'total_amount' => $product->total_amount,
                    'seller_profit' => $product->seller_profit,
                    'fulfill_status' => $product->fulfill_status,
                    'full_printed' => $product->full_printed,
                    'barcode' => $product->barcode,
                    'fulfill_fba_by' => $product->fulfill_fba_by,
                    'campaign_title' => $product->campaign_title,
                    'tracking_code' => $product->tracking_code,
                    'tracking_url' => $product->tracking_url,
                    'shipping_carrier' => $product->shipping_carrier,
                    'cross_shipping' => $product->cross_shipping,
                    'color' => $product->color,
                    'design_by' => $product->design_by,

                    'mockups' => $product->mockups->map(function (File $mockup) {
                        return [
                            'id' => $mockup->id,
                            'file_url' => $mockup->file_url,
                            'print_space' => $mockup->print_space
                        ];
                    }),

                    'designs' => $product->designs->map(function (File $design) {
                        return [
                            'id' => $design->id,
                            'file_url' => $design->file_url,
                            'print_space' => $design->print_space,
                        ];
                    }),
                ];
            }),
        ];
    }
}
