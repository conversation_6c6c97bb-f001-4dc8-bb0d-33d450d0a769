<?php

namespace Modules\SellerAPI\Data;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\DataCollection;
use <PERSON><PERSON>\LaravelData\Optional;

class UpdateOrderData extends Data
{
    public function __construct(
        public Optional|string $order_number_2,
        public Optional|string $status,

        // customer
        public Optional|string $customer_name,
        public Optional|null|string $customer_email,
        public Optional|null|string $customer_phone,
        public Optional|string $address,
        public Optional|null|string $address_2,
        public Optional|null|string $house_number,
        public Optional|null|string $mailbox_number,
        public Optional|null|string $city,
        public Optional|null|string $state,
        public Optional|null|string $postcode,
        public Optional|string $country,
        public Optional|null|string $shipping_method,
        public Optional|null|string $order_note,
        public Optional|null|string $shipping_label,

        /** @var DataCollection<SaveOrderProductData> $products */
        public Optional|DataCollection $products
    ) {
    }
}
