<?php

namespace Modules\SellerAPI\Actions;

use App\Enums\OrderStatus;
use App\Models\Order;
use RuntimeException;
use Throwable;

class UpdateOrderStatus
{
    protected array $statusHandlers = [
        OrderStatus::DRAFT     => MarkOrderStatusAsDraft::class,
        OrderStatus::ON_HOLD   => MarkOrderStatusAsOnHold::class,
        OrderStatus::CANCELLED => MarkOrderStatusAsCancelled::class,
    ];

    public function __construct(
        private Order $order,
        private readonly string $status,
    ) {
        //
    }

    /**
     * @throws Throwable
     */
    public function execute(): void
    {
        $handler = $this->statusHandlers[$this->status] ?? null;

        if (!$handler) {
            throw new RuntimeException(sprintf('Unhandled status: %s', $this->status), 400);
        }

        $handler::make($this->order)->execute();
    }

    public static function make(Order $order, string $status): self
    {
        return new self($order, $status);
    }
}
