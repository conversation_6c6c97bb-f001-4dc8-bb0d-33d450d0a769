<?php

namespace Modules\SellerAPI\Actions;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use Illuminate\Support\Facades\Log;

class MarkOrderFulfillStatusAsInvalid
{
    public function __construct(
        private Order $order,
        private string $fulfillLog,
    ) {
        //
    }

    public function execute(): void
    {
        if (!in_array($this->order->status, [
            OrderStatus::PENDING,
            OrderStatus::DRAFT,
            OrderStatus::ON_HOLD,
        ], true)) {
            $message = sprintf(
                'Can not mark fulfill status as invalid because this order status is %s',
                $this->order->status
            );
            Log::error(__CLASS__ . $message, [
                'order_id' => $this->order->id,
            ]);

            return;
        }

        $this->order->fulfill_status = OrderFulfillStatus::INVALID;
        $this->order->fulfill_log .= '|' . $this->fulfillLog;
        $this->order->save();
    }

    public static function make(Order $order, string $fulfillLog): self
    {
        return new static($order, $fulfillLog);
    }
}
