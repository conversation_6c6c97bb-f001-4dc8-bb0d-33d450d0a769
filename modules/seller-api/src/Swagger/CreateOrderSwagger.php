<?php

namespace Modules\SellerAPI\Swagger;

/**
 * @OA\Post(
 *     path="/seller-api/orders",
 *     summary="Create a new order",
 *     description="This API creates a new order based on the provided data.",
 *     tags={"Orders"},
 *     security={{ "bearerAuth": {} }},
 *     @OA\RequestBody(
 *         required=true,
 *         @OA\MediaType(
 *             mediaType="application/json",
 *             @OA\Schema(
 *                 type="object",
 *                 required={"order_number", "address", "country", "products"},
 *                 @OA\Property(
 *                     property="order_number",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="store_name",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="store_domain",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="customer_name",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="customer_email",
 *                     type="string",
 *                     format="email"
 *                 ),
 *                 @OA\Property(
 *                     property="customer_phone",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="address",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="address_2",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="house_number",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="mailbox_number",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="city",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="state",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="postcode",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="country",
 *                     enum={"US", "CA", "GB", "AU", "DE", "FR", "IT", "ES", "JP"},
 *                     type="string",
 *                     description="Country code (these options are examples, the actual list of countries may vary)",
 *                 ),
 *                 @OA\Property(
 *                     property="shipping_method",
 *                     enum=L5_SWAGGER_CONST_SHIPPING_METHOD,
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="order_note",
 *                     type="string"
 *                 ),
 *                 @OA\Property(
 *                     property="shipping_label",
 *                     type="string",
 *                     format="url",
 *                     nullable=true,
 *                     description="Shipping label URL (required if the order is platform)"
 *                 ),
 *                 @OA\Property(
 *                     property="products",
 *                     type="array",
 *                     @OA\Items(
 *                         type="object",
 *                         required={"quantity", "template_id", "options"},
 *                         @OA\Property(
 *                             property="campaign_title",
 *                             type="string"
 *                         ),
 *                         @OA\Property(
 *                             property="quantity",
 *                             type="integer"
 *                         ),
 *                         @OA\Property(
 *                             property="template_id",
 *                             type="integer"
 *                         ),
 *                         @OA\Property(
 *                             property="options",
 *                             type="object",
 *                             description="Product options as key-value pairs",
 *                             @OA\AdditionalProperties(type="string"),
 *                             example={"color": "white", "size": "30x20in"},
 *                         ),
 *                         @OA\Property(
 *                             property="custom_options",
 *                             type="array",
 *                             nullable=true,
 *                             description="Custom options for the product (e.g., text, image)",
 *                             @OA\Items(
 *                                 type="object",
 *                                 @OA\Property(
 *                                     property="type",
 *                                     type="string",
 *                                     description="Type of the option (e.g., text, image)",
 *                                 ),
 *                                 @OA\Property(
 *                                     property="label",
 *                                     type="string",
 *                                     description="Label of the option",
 *                                 ),
 *                                 @OA\Property(
 *                                     property="value",
 *                                     type="string",
 *                                     description="Value of the option",
 *                                 ),
 *                             )
 *                         ),
 *                         @OA\Property(
 *                             property="barcode",
 *                             type="string",
 *                             nullable=true,
 *                             description="Barcode (required only when fulfill_fba_by is amazon)"
 *                         ),
 *                         @OA\Property(
 *                             property="fulfill_fba_by",
 *                             type="string",
 *                             nullable=true,
 *                             enum=L5_SWAGGER_CONST_FULFILL_FBA_BY,
 *                             description="Fulfillment type (required only when order is platform)"
 *                         ),
 *                         @OA\Property(
 *                             property="design_by_sen",
 *                             type="boolean",
 *                             nullable=true,
 *                             description="If true, the design will be process by SenPrints",
 *                         ),
 *                         @OA\Property(
 *                             property="designs",
 *                             type="array",
 *                             @OA\Items(
 *                                 type="object",
 *                                 @OA\Property(
 *                                     property="file_url",
 *                                     type="string",
 *                                     format="url"
 *                                 ),
 *                                 @OA\Property(
 *                                     property="print_space",
 *                                     type="string",
 *                                     description="Reference to template product",
 *                                 )
 *                             )
 *                         ),
 *                         @OA\Property(
 *                             property="mockups",
 *                             type="array",
 *                             @OA\Items(
 *                                 type="object",
 *                                 @OA\Property(
 *                                     property="file_url",
 *                                     type="string",
 *                                     format="url"
 *                                 ),
 *                                 @OA\Property(
 *                                     property="print_space",
 *                                     type="string",
 *                                     description="Reference to template product",
 *                                 )
 *                             )
 *                         )
 *                     )
 *                 )
 *             ),
 *             @OA\Examples(
 *                 example="Example1",
 *                 summary="Example create order fulfillment",
 *                 value={
 *                     "order_number": "TEST-123456",
 *                     "store_name": "ABC Store",
 *                     "store_domain": "abcstore.com",
 *                     "customer_name": "John Doe",
 *                     "customer_email": "<EMAIL>",
 *                     "customer_phone": "+1234567890",
 *                     "address": "123 ABC Street",
 *                     "address_2": "Apt 4B",
 *                     "house_number": "12",
 *                     "mailbox_number": "34",
 *                     "city": "New York",
 *                     "state": "NY",
 *                     "postcode": "10001",
 *                     "country": "US",
 *                     "shipping_method": "standard",
 *                     "order_note": "Please deliver before weekend.",
 *                     "products": {
 *                         {
 *                             "campaign_title": "T-Shirt Campaign",
 *                             "quantity": 2,
 *                             "template_id": 565,
 *                             "design_by_sen": false,
 *                             "options": {
 *                                 "color": "red",
 *                                 "size": "m"
 *                             },
 *                             "designs": {
 *                                 {
 *                                     "file_url": "https://example.com/design1.png",
 *                                     "print_space": "front"
 *                                 }
 *                             },
 *                             "mockups": {
 *                                 {
 *                                     "file_url": "https://example.com/mockup1.png",
 *                                     "print_space": "front"
 *                                 }
 *                             }
 *                         }
 *                     }
 *                 }
 *             ),
 *             @OA\Examples(
 *                 example="Example2",
 *                 summary="Example create order platform",
 *                 value={
 *                     "order_number": "TEST-789012",
 *                     "store_name": "XYZ Store",
 *                     "store_domain": "xyzstore.com",
 *                     "customer_name": "Jane Doe",
 *                     "customer_email": "<EMAIL>",
 *                     "customer_phone": "+1987654321",
 *                     "address": "456 XYZ Avenue",
 *                     "address_2": "Suite 21",
 *                     "house_number": "45",
 *                     "mailbox_number": "67",
 *                     "city": "Los Angeles",
 *                     "state": "CA",
 *                     "postcode": "90001",
 *                     "country": "US",
 *                     "order_note": "Leave at the front door if not home.",
 *                     "shipping_label": "https://example.com/shipping_label.pdf",
 *                     "products": {
 *                         {
 *                             "campaign_title": "Hoodie Campaign",
 *                             "quantity": 30,
 *                             "template_id": 562,
 *                             "options": {
 *                                 "color": "black",
 *                                 "size": "l"
 *                             },
 *                             "barcode": "123456789012",
 *                             "fulfill_fba_by": "amazon",
 *                             "designs": {
 *                                 {
 *                                     "file_url": "https://example.com/design2.png",
 *                                     "print_space": "back"
 *                                 }
 *                             },
 *                             "mockups": {
 *                                 {
 *                                     "file_url": "https://example.com/mockup2.png",
 *                                     "print_space": "back"
 *                                 }
 *                             }
 *                         }
 *                     }
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=201,
 *         description="Order created successfully",
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="success",
 *                 type="boolean",
 *                 example=true
 *             ),
 *             @OA\Property(
 *                 property="message",
 *                 type="string",
 *                 example="Order created successfully"
 *             ),
 *             @OA\Property(
 *                 property="data",
 *                 ref="#/components/schemas/Order"
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=400,
 *         description="Invalid data",
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="success",
 *                 type="boolean",
 *                 example=false
 *             ),
 *             @OA\Property(
 *                 property="message",
 *                 type="string",
 *                 example="Validation error"
 *             ),
 *             @OA\Property(
 *                 property="errors",
 *                 type="object"
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=500,
 *         description="Server error",
 *         @OA\JsonContent(
 *             @OA\Property(
 *                 property="success",
 *                 type="boolean",
 *                 example=false
 *             ),
 *             @OA\Property(
 *                 property="message",
 *                 type="string",
 *                 example="Error creating order"
 *             )
 *         )
 *     )
 * )
 */
class CreateOrderSwagger
{
    // This class is intentionally left empty.
}
