<?php

namespace Modules\SellerAPI\Swagger;

/**
 * @OA\Schema(
 *     schema="Order",
 *     type="object",
 *     title="Order",
 *     @OA\Property(
 *         property="id",
 *         type="integer",
 *         example=1,
 *         description="Order ID",
 *     ),
 *     @OA\Property(
 *         property="order_number",
 *         type="string",
 *         example="ORD-123456",
 *         description="Your order number",
 *     ),
 *     @OA\Property(
 *         property="store_name",
 *         type="string",
 *         nullable=true,
 *         example="ABC Store",
 *         description="Store name",
 *     ),
 *     @OA\Property(
 *         property="store_domain",
 *         type="string",
 *         nullable=true,
 *         example="abc.com",
 *         description="Store domain",
 *     ),
 *     @OA\Property(
 *         property="customer_name",
 *         type="string",
 *         example="Nguyen Van A",
 *         description="Customer name",
 *     ),
 *     @OA\Property(
 *         property="customer_email",
 *         type="string",
 *         format="email",
 *         nullable=true,
 *         example="<EMAIL>",
 *         description="Customer email",
 *     ),
 *     @OA\Property(
 *         property="customer_phone",
 *         type="string",
 *         nullable=true,
 *         example="0123456789",
 *         description="Customer phone number",
 *     ),
 *     @OA\Property(
 *         property="address",
 *         type="string",
 *         example="123 ABC Street",
 *         description="Customer address",
 *     ),
 *     @OA\Property(
 *         property="address_2",
 *         type="string",
 *         nullable=true,
 *         example="Apartment 101",
 *         description="Additional address",
 *     ),
 *     @OA\Property(
 *         property="house_number",
 *         type="string",
 *         nullable=true,
 *         example="12B",
 *         description="House number",
 *     ),
 *     @OA\Property(
 *         property="mailbox_number",
 *         type="string",
 *         nullable=true,
 *         example="456",
 *         description="Mailbox number",
 *     ),
 *     @OA\Property(
 *         property="city",
 *         type="string",
 *         nullable=true,
 *         example="Hanoi",
 *         description="City",
 *     ),
 *     @OA\Property(
 *         property="state",
 *         type="string",
 *         nullable=true,
 *         example="Ha Dong",
 *         description="State/Province",
 *     ),
 *     @OA\Property(
 *         property="postcode",
 *         type="string",
 *         nullable=true,
 *         example="100000",
 *         description="Postal code",
 *     ),
 *     @OA\Property(
 *         property="country",
 *         type="string",
 *         example="VN",
 *         description="Country code",
 *     ),
 *     @OA\Property(
 *         property="order_note",
 *         type="string",
 *         nullable=true,
 *         example="Deliver during business hours",
 *         description="Order note",
 *     ),
 *     @OA\Property(
 *         property="shipping_label",
 *         type="string",
 *         format="url",
 *         nullable=true,
 *         example="http://example.com/label.pdf",
 *         description="Shipping label URL",
 *     ),
 *     @OA\Property(
 *         property="shipping_method",
 *         type="string",
 *         example="standard",
 *         description="Shipping method",
 *     ),
 *     @OA\Property(
 *         property="cross_shipping",
 *         type="boolean",
 *         nullable=true,
 *         example=true,
 *         description="Whether the order uses cross shipping",
 *     ),
 *     @OA\Property(
 *         property="personalized",
 *         type="boolean",
 *         nullable=true,
 *         example=false,
 *         description="Whether the order is personalized",
 *     ),
 *     @OA\Property(
 *         property="status",
 *         type="string",
 *         example="pending",
 *         description="Order status",
 *     ),
 *     @OA\Property(
 *         property="fulfill_status",
 *         type="string",
 *         example="unfulfilled",
 *         description="Fulfillment status",
 *     ),
 *     @OA\Property(
 *         property="address_verified",
 *         type="boolean",
 *         nullable=true,
 *         example=true,
 *         description="Whether the address has been verified",
 *     ),
 *     @OA\Property(
 *         property="fulfill_log",
 *         type="string",
 *         nullable=true,
 *         example="Order sent to production",
 *         description="Fulfillment log details",
 *     ),
 *     @OA\Property(
 *         property="type",
 *         type="string",
 *         example="standard",
 *         description="Order type",
 *     ),
 *     @OA\Property(
 *         property="created_at",
 *         type="string",
 *         format="date-time",
 *         example="2025-02-28T17:01:29+07:00",
 *         description="Order creation timestamp",
 *     ),
 *     @OA\Property(
 *         property="updated_at",
 *         type="string",
 *         format="date-time",
 *         example="2025-02-28T17:01:29+07:00",
 *         description="Order update timestamp",
 *     ),
 *     @OA\Property(
 *         property="paid_at",
 *         type="string",
 *         format="date-time",
 *         example="2025-03-01T12:00:00+07:00",
 *         description="Timestamp when the order was paid",
 *     ),
 *     @OA\Property(
 *         property="delivered_at",
 *         type="string",
 *         format="date-time",
 *         example="2025-03-05T09:00:00+07:00",
 *         description="Timestamp when the order was delivered",
 *     ),
 *     @OA\Property(
 *         property="received_at",
 *         type="string",
 *         format="date-time",
 *         example="2025-03-06T14:00:00+07:00",
 *         description="Timestamp when the order was received",
 *     ),
 *     @OA\Property(
 *         property="total_quantity",
 *         type="integer",
 *         example=3,
 *         description="Total quantity of products in the order",
 *     ),
 *     @OA\Property(
 *         property="total_product_amount",
 *         type="number",
 *         format="float",
 *         example=59.99,
 *         description="Total amount for products",
 *     ),
 *     @OA\Property(
 *         property="total_shipping_amount",
 *         type="number",
 *         format="float",
 *         example=5.99,
 *         description="Total shipping fee",
 *     ),
 *     @OA\Property(
 *         property="total_amount",
 *         type="number",
 *         format="float",
 *         example=65.98,
 *         description="Total order amount",
 *     ),
 *     @OA\Property(
 *         property="total_paid",
 *         type="number",
 *         format="float",
 *         example=65.98,
 *         description="Total amount paid",
 *     ),
 *     @OA\Property(
 *         property="total_product_cost",
 *         type="number",
 *         format="float",
 *         example=30.00,
 *         description="Total product cost",
 *     ),
 *     @OA\Property(
 *         property="total_shipping_cost",
 *         type="number",
 *         format="float",
 *         example=3.00,
 *         description="Total shipping cost",
 *     ),
 *     @OA\Property(
 *         property="total_seller_profit",
 *         type="number",
 *         format="float",
 *         example=32.98,
 *         description="Total profit for the seller",
 *     ),
 *     @OA\Property(
 *         property="processing_fee",
 *         type="number",
 *         format="float",
 *         example=2.00,
 *         description="Processing fee for the order",
 *     ),
 *     @OA\Property(
 *         property="total_fulfill_fee",
 *         type="number",
 *         format="float",
 *         example=4.00,
 *         description="Total fulfill fee",
 *     ),
 *     @OA\Property(
 *         property="processing_fee_paid",
 *         type="boolean",
 *         example=true,
 *         description="Whether the processing fee has been paid",
 *     ),
 *     @OA\Property(
 *         property="fulfill_fee_paid",
 *         type="boolean",
 *         example=true,
 *         description="Whether the fulfill fee has been paid",
 *     ),
 *     @OA\Property(
 *         property="products",
 *         type="array",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(
 *                 property="id",
 *                 type="integer",
 *                 example=1,
 *                 description="Order Product ID in the order",
 *             ),
 *             @OA\Property(
 *                 property="product_name",
 *                 type="string",
 *                 example="T-shirt",
 *                 description="Product name",
 *             ),
 *             @OA\Property(
 *                 property="sku",
 *                 type="string",
 *                 example="SKU12345",
 *                 description="Product SKU",
 *             ),
 *             @OA\Property(
 *                 property="quantity",
 *                 type="integer",
 *                 example=2,
 *                 description="Product quantity",
 *             ),
 *             @OA\Property(
 *                 property="price",
 *                 type="number",
 *                 format="float",
 *                 example=19.99,
 *                 description="Price of the product",
 *             ),
 *             @OA\Property(
 *                 property="total_amount",
 *                 type="number",
 *                 format="float",
 *                 example=39.98,
 *                 description="Total amount for this product line",
 *             ),
 *             @OA\Property(
 *                 property="seller_profit",
 *                 type="number",
 *                 format="float",
 *                 example=10.00,
 *                 description="Profit earned by seller from this product",
 *             ),
 *             @OA\Property(
 *                 property="fulfill_status",
 *                 type="string",
 *                 example="fulfilled",
 *                 description="Fulfillment status of the product",
 *             ),
 *             @OA\Property(
 *                 property="full_printed",
 *                 type="boolean",
 *                 example=true,
 *                 description="Whether the product is full printed",
 *             ),
 *             @OA\Property(
 *                 property="barcode",
 *                 type="string",
 *                 nullable=true,
 *                 example="ABC123456789",
 *                 description="Barcode of the product",
 *             ),
 *             @OA\Property(
 *                 property="fulfill_fba_by",
 *                 type="string",
 *                 nullable=true,
 *                 example="Amazon",
 *                 description="Fulfillment service provider",
 *             ),
 *             @OA\Property(
 *                 property="design_by",
 *                 type="string",
 *                 nullable=true,
 *                 example="seller",
 *                 description="Design will be done by seller or SenPrints",
 *             ),
 *             @OA\Property(
 *                 property="campaign_title",
 *                 type="string",
 *                 nullable=true,
 *                 example="Valentine Campaign",
 *                 description="Campaign name related to the product",
 *             ),
 *             @OA\Property(
 *                 property="tracking_code",
 *                 type="string",
 *                 nullable=true,
 *                 example="TRACK123456",
 *                 description="Tracking code for this product",
 *             ),
 *             @OA\Property(
 *                 property="tracking_url",
 *                 type="string",
 *                 format="url",
 *                 nullable=true,
 *                 example="http://tracking.com/TRACK123456",
 *                 description="Tracking URL for this product",
 *             ),
 *             @OA\Property(
 *                 property="shipping_carrier",
 *                 type="string",
 *                 nullable=true,
 *                 example="DHL",
 *                 description="Shipping carrier used",
 *             ),
 *             @OA\Property(
 *                 property="cross_shipping",
 *                 type="boolean",
 *                 example=false,
 *                 description="Whether the product uses cross shipping",
 *             ),
 *             @OA\Property(
 *                 property="color",
 *                 type="string",
 *                 nullable=true,
 *                 example="Red",
 *                 description="Product color",
 *             ),
 *             @OA\Property(
 *                 property="options",
 *                 type="object",
 *                 description="Product options as key-value pairs",
 *                 @OA\AdditionalProperties(type="string"),
 *                 example={"color": "white", "size": "30x20in"},
 *             ),
 *             @OA\Property(
 *                 property="custom_options",
 *                 type="array",
 *                 nullable=true,
 *                 description="Custom options for the product (e.g., text, image)",
 *                 @OA\Items(
 *                     type="object",
 *                     @OA\Property(
 *                         property="type",
 *                         type="string",
 *                         description="Type of the option (e.g., text, image)",
 *                     ),
 *                     @OA\Property(
 *                         property="label",
 *                         type="string",
 *                         description="Label of the option",
 *                     ),
 *                     @OA\Property(
 *                         property="value",
 *                         type="string",
 *                         description="Value of the option",
 *                     ),
 *                 )
 *             ),
 *             @OA\Property(
 *                 property="thumb_url",
 *                 type="string",
 *                 format="url",
 *                 nullable=true,
 *                 example="http://example.com/thumb.jpg",
 *                 description="Product thumbnail URL",
 *             ),
 *             @OA\Property(
 *                 property="mockups",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     required={"id", "file_url"},
 *                     @OA\Property(
 *                         property="id",
 *                         type="integer",
 *                         example=1,
 *                         description="Mockup ID",
 *                     ),
 *                     @OA\Property(
 *                         property="file_url",
 *                         type="string",
 *                         format="url",
 *                         example="http://example.com/mockup.jpg",
 *                         description="Mockup file URL",
 *                     ),
 *                     @OA\Property(
 *                         property="print_space",
 *                         type="string",
 *                         nullable=true,
 *                         example="front",
 *                         description="Print space of the mockup",
 *                     ),
 *                 ),
 *                 description="List of product mockups",
 *             ),
 *             @OA\Property(
 *                 property="designs",
 *                 type="array",
 *                 @OA\Items(
 *                     type="object",
 *                     required={"id", "file_url"},
 *                     @OA\Property(
 *                         property="id",
 *                         type="integer",
 *                         example=1,
 *                         description="Design ID",
 *                     ),
 *                     @OA\Property(
 *                         property="file_url",
 *                         type="string",
 *                         format="url",
 *                         example="http://example.com/design.jpg",
 *                         description="Design file URL",
 *                     ),
 *                     @OA\Property(
 *                         property="print_space",
 *                         type="string",
 *                         nullable=true,
 *                         example="front",
 *                         description="Print space of the design",
 *                     ),
 *                 ),
 *                 description="List of product designs",
 *             ),
 *         ),
 *         description="List of products in the order",
 *     )
 * )
 */
class OrderSwagger
{
    // This class is used to define the schema for Order in OpenAPI
}
