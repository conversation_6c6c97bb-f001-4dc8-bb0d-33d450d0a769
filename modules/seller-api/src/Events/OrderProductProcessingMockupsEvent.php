<?php

namespace Modules\SellerAPI\Events;

use Modules\SellerAPI\Data\SaveOrderProductFileData;
use Spatie\LaravelData\DataCollection;

class OrderProductProcessingMockupsEvent
{
    use OrderProductEventTrait;

    public function __construct(
        public readonly int $orderProductId,
        /** @var DataCollection<SaveOrderProductFileData> $mockups */
        public readonly DataCollection $mockups,
    ) {
    }
}
