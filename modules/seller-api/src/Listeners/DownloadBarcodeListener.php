<?php

namespace Modules\SellerAPI\Listeners;

use App\Actions\Commons\ImgDirectLinkAction;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OrderFulfillStatus;
use App\Http\Controllers\UploadController;
use App\Models\File;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use Modules\SellerAPI\Actions\UpdateOrderFulfillStatus;
use Modules\SellerAPI\Events\OrderProductUpdatedEvent;

class DownloadBarcodeListener implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, Queueable;

    private const MAX_RETRY = 5;

    public function handle(OrderProductUpdatedEvent $event): void
    {
        $orderProduct = $event->getOrderProduct();

        if (!$orderProduct) {
            Log::error(__CLASS__ . ' order product not found', [
                'orderProductId' => $event->orderProductId,
            ]);
            return;
        }

        if (!$orderProduct->barcode) {
            return;
        }

        $shippingLabelLink = ImgDirectLinkAction::singleton()->handle($orderProduct->barcode);
        for ($retried = 1; $retried < self::MAX_RETRY; $retried++) {
            $shippingLabelUploaded = UploadController::uploadS3FromDirectLink($shippingLabelLink, 'tmp/');
            if ($shippingLabelUploaded) {
                break;
            }
        }

        if (empty($shippingLabelUploaded['path'])) {
            UpdateOrderFulfillStatus::make(
                order: $orderProduct->order,
                fulfillStatus: OrderFulfillStatus::INVALID,
                fulfillLog: 'Barcode is invalid'
            )->execute();

            return;
        }

        $shippingLabelPath = 'o/' . $orderProduct->id . '/' . basename($shippingLabelUploaded['path']);
        $orderProduct->barcode = saveTempFileAws($shippingLabelUploaded['path'], $shippingLabelPath);
        $orderProduct->save();


        File::query()
            ->where([
                'order_product_id' => $orderProduct->id,
                'seller_id'        => $orderProduct->seller_id,
                'type'             => FileTypeEnum::BARCODE,
            ])
            ->update([
                'status' => FileStatusEnum::INACTIVE,
            ]);

        File::query()->insert([
            'order_id'         => $orderProduct->order_id,
            'order_product_id' => $orderProduct->id,
            'type'             => FileTypeEnum::BARCODE,
            'file_url'         => $orderProduct->barcode,
            'file_url_2'       => $shippingLabelPath,
            'seller_id'        => $orderProduct->seller_id,
            'option'           => null,
            'print_space'      => null,
            'status'           => FileStatusEnum::ACTIVE,
        ]);
    }
}
