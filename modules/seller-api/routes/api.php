<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\CampaignController;
use App\Http\Controllers\Storefront\ProductController;
use Illuminate\Support\Facades\Route;
use Modules\SellerAPI\Http\Controllers\OrderController;
use Modules\SellerAPI\Http\Controllers\CampaignController as SellerCampaignController;

Route::group([
    'prefix' => 'seller-api',
    'middleware' => [
        'access.external.api',
        'check.is_senhub',
    ],
    'as' => 'seller-api.',
], function () {
    Route::get('/me', [AuthController::class, 'me'])->name('me');

    Route::get('/orders/{id}', [OrderController::class, 'show'])->name('orders.show');
    Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
    Route::put('/orders/{id}', [OrderController::class, 'update'])->name('orders.update');
    Route::put('/orders/{id}/{action}', [OrderController::class, 'updateStatus'])->name('orders.update.status');

    Route::get('/catalog', [CampaignController::class, 'listFullTemplateProducts'])
        ->name('catalog.index');
    Route::get('/catalog/info', [ProductController::class, 'catalogGetTemplateProductInfo'])
        ->name('catalog.show');

    Route::get('/campaigns', [SellerCampaignController::class, 'index'])
        ->name('campaigns.index');
    Route::get('/campaigns/filter', [SellerCampaignController::class, 'filter'])
        ->name('campaigns.filter');
});
