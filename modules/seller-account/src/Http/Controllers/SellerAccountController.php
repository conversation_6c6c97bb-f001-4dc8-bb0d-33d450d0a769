<?php
namespace Modules\SellerAccount\Http\Controllers;

use App\Enums\SellerBillingType;
use App\Http\Controllers\Controller;
use App\Jobs\SendSellerBalanceUpdateNotification;
use App\Models\User;
use App\Traits\ApiResponse;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\SellerAccount\Enums\SellerBalanceTypeEnum;
use Modules\SellerAccount\Http\Requests\AdminAdjustSellerBalance2Request;
use Modules\SellerAccount\Models\UserBalance;
use Modules\SellerAccount\Services\SellerAccountService;

class SellerAccountController extends Controller {
    use ApiResponse;

    /**
     * @param AdminAdjustSellerBalance2Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function adminAdjustSellerBalance2(AdminAdjustSellerBalance2Request $request)
    {
        $sellerId = $request->input('seller_id');
        $action = $request->input('action');
        $reward_amount = $request->input('reward_amount');
        $reward_days = $request->input('reward_days');
        $reason = $request->input('reason');
        $seller = User::query()->where('id', $sellerId)->first();
        if (!$seller) {
            return $this->errorResponse();
        }
        if ($action === 'subtract' && $reward_amount > 0) {
            $reward_amount = 0 - $reward_amount;
        }
        try {
            DB::beginTransaction();
            $seller->updateBalance2($reward_amount, SellerBillingType::OTHER, $reason, 0, $reward_days);
            DB::commit();
            $subject = 'Your account balance 2 is updated: ';
            if ($action === 'add') {
                $subject .= ' +';
            }
            $seller_balance = UserBalance::query()->where('seller_id', $sellerId)->where('type', SellerBalanceTypeEnum::BALANCE_2)->first();
            if (!$seller_balance) {
                return $this->errorResponse('Seller balance 2 not found');
            }
            $subject .= SellerAccountService::formatMoney($reward_amount);
            $subject .= ' will expire on: ' . Carbon::parse($seller_balance->expired_at)->format('d/m/Y');
            SendSellerBalanceUpdateNotification::dispatchAfterResponse([
                'email' => $seller->email,
                'subject' => $subject,
                'amount' => SellerAccountService::formatMoney($reward_amount),
                'balance' => SellerAccountService::formatMoney($seller_balance->balance),
                'reason' => $reason
            ]);

            return $this->successResponse('Seller balance 2 updated');
        } catch (\Exception $exception) {
            DB::rollBack();
            return $this->errorResponse($exception->getMessage());
        }
    }
}
