<?php
namespace Modules\SellerAccount\Providers;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;

class SellerAccountServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap SellerAccount service provider service.
     *
     * @return void
     */
    public function boot()
    {
        if(!defined('SELLER_ACCOUNT_MODULE_PATH')) {
            define('SELLER_ACCOUNT_MODULE_PATH', dirname(__DIR__, 2));
        }
        $this->autoload(SELLER_ACCOUNT_MODULE_PATH . '/helpers');
        $this->loadMigrationsFrom(SELLER_ACCOUNT_MODULE_PATH . '/database/migrations');
        if ($this->app->runningInConsole()) {
            $commandDir = dirname(__DIR__) . '/Commands';
            $commands = scan_folder($commandDir);
            $_commands = array();
            foreach ($commands as $command) {
                if(file_exists($commandDir . '/' . $command)) {
                    $command = basename($command, ".php");
                    $_commands[] = "Modules\\SellerAccount\\Commands\\{$command}";
                }
            }
            $this->commands($_commands);
        }
        $this->app->booted(function () {
            $this->loadViewsFrom(SELLER_ACCOUNT_MODULE_PATH . '/resources/views', 'seller_account');
            $this->registerConfigs(['general']);
            $this->registerRoutes();
            if (config('senprints.schedule_enabled')) {
                $schedule = $this->app->make(Schedule::class);
                $schedule->command('seller:account:reset-balance-2')->hourly()->withoutOverlapping(5)->logAfter();
            }
        });
    }

    /**
     * Register the seller_account's configs.
     *
     * @return void
     */
    protected function registerConfigs($fileNames)
    {
        if (!is_array($fileNames)) {
            $fileNames = [$fileNames];
        }
        $config_path = SELLER_ACCOUNT_MODULE_PATH . '/config';
        foreach ($fileNames as $fileName) {
            $full_path = $config_path . '/' . $fileName . '.php';
            $this->mergeConfigFrom($full_path, 'seller.account.config.' . $fileName);
        }
    }

    /**
     * Register the seller_account's routes.
     *
     * @return void
     */
    protected function registerRoutes()
    {
        if ($this->app->routesAreCached()) {
            return;
        }
        $route_path = SELLER_ACCOUNT_MODULE_PATH . '/routes';
        $routes = $this->scanFolder($route_path);
        foreach ($routes as $route) {
            $this->loadRoutesFrom($route_path . '/' . $route);
        }
    }

    /**
     * @param string $path
     * @param array $ignoreFiles
     * @return array
     */
    public function scanFolder($path, array $ignoreFiles = [])
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }

    /**
     * Load helpers from a directory
     * @param string $directory
     */
    public function autoload(string $directory): void
    {
        $helpers = File::glob($directory . '/*.php');
        if(empty($helpers)) {
            return;
        }
        foreach ($helpers as $helper) {
            File::requireOnce($helper);
        }
    }
}
