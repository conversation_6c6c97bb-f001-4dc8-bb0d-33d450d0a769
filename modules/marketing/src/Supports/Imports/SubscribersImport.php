<?php

namespace Modules\Marketing\Supports\Imports;

use App\Models\Order;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use InvalidArgumentException;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Modules\Marketing\Enums\SubscriberEnum;
use Modules\Marketing\Enums\SubscriberStatusEnum;
use Modules\Marketing\Jobs\ImportChunkedSubscribersJob;
use Modules\Marketing\Models\IndexSubscribers;

class SubscribersImport implements ToCollection, WithHeadingRow
{
    private function parseBeforeMapping(&$row): void
    {
        $row = $row->toArray();
        $row = array_map('trim', $row);
    }

    /**
     * Find emails from subscribers
     * @param mixed $emails
     * @return array
     */
    private function findEmailsFromSubscribers($emails): array
    {
        // Find emails which are exists in subscribers table
        $result = IndexSubscribers::query()
            ->select('email')
            ->whereIn('email', $emails)
            ->where('from_system', 1)
            ->get();
        if ($result->isEmpty()) {
            return [[], $emails];
        }
        $emailsExists = $result->pluck('email')->toArray();
        $emailsNotFound = array_diff($emails, $emailsExists);
        return [$emailsExists, $emailsNotFound];
    }

    /**
     * Find emails from orders
     * @param mixed $emails
     * @return (array<string|int, mixed>|array)[]
     */
    private function findEmailsFromOrders($emails): array
    {
        // If emails not found in subscribers table, find in orders table
        $result = Order::query()
            ->select('customer_email')
            ->whereIn('customer_email', $emails)
            ->get();
        if ($result->isEmpty()) {
            return [[], []];
        }
        $emailsExists = $result->pluck('customer_email')->toArray();
        $emailsNotFound = array_diff($emails, $emailsExists);
        return [$emailsExists, $emailsNotFound];
    }

    /**
     * Find exists emails
     * @param mixed $emails
     * @return array
     * @throws InvalidArgumentException
     */
    private function findExistsEmails($emails): array
    {
        // Step 1: Find emails from subscribers
        [$emailsExists, $emails] = $this->findEmailsFromSubscribers($emails);
        // Step 2: Find emails from orders
        [$emailFromOrders, $emailNotFound] = $this->findEmailsFromOrders($emails);
        $emailsExists = array_merge($emailsExists, $emailFromOrders);
        return $emailsExists;
    }

    /**
     * Process emails list
     * @param mixed $emails
     * @return array
     * @throws InvalidArgumentException
     */
    private function processEmailsList($emails): array
    {
        // Split emails into chunks for better performance
        $emails = array_chunk($emails, 1000);
        $emailsFromOrders = [];
        foreach ($emails as $email) {
            $emailsExists = $this->findExistsEmails($email);
            $emailsFromOrders = array_merge($emailsFromOrders, $emailsExists);
        }
        // Remove duplicates
        $emailsFromOrders = array_unique($emailsFromOrders);
        return $emailsFromOrders;
    }

    /**
     * Check subscribers
     * @param array $emails
     * @return array
     * @throws InvalidArgumentException
     */
    private function checkSubscribers(array $emails): array
    {
        $chunked = array_chunk($emails, 1000);
        $subscribers = [];
        foreach ($chunked as $chunk) {
            $results = IndexSubscribers::query()
                ->select('email')
                ->whereIn('email', $chunk)
                ->where('is_deleted', SubscriberEnum::IS_NOT_DELETED)
                ->get();
            if ($results->isNotEmpty()) {
                $subscribers = array_merge($subscribers, $results->pluck('email')->toArray());
            }
        }
        return $subscribers;
    }

    /**
     * @param Collection $rows
     * @return void
     */
    public function collection(Collection $rows): void
    {
        $currentUserId = currentUser()->getUserId();
        try {
            // Process emails list
            $chunkedData = $rows->chunk(1000);
            $emails = $rows->pluck('email')->toArray();
            $emailsFromOrders = $this->processEmailsList($emails);
            foreach ($chunkedData as $chunkedRows) {
                $subscribers = [];
                $subscribers_has_tags = [];
                foreach ($chunkedRows as $row) {
                    if (!$row->filter()->isNotEmpty()) {
                        continue;
                    }
                    $this->parseBeforeMapping($row);

                    $valid = true;
                    $email = $row['email'];
                    $isFromSystem = in_array(trim($email), $emailsFromOrders); // Check if email is from system
                    $name = $row['name'] ?? '';
                    $phone = $row['phone'] ?? '';
                    $city = $row['city'] ?? '';
                    $state = $row['state'] ?? '';
                    $country = $row['country'] ?? '';
                    $validator = Validator::make([
                        'email' => $email,
                    ], [
                        'email' => 'required|email',
                    ]);
                    if (!$validator->passes()) {
                        $valid = false;
                    }
                    $tags = !empty($row['tags']) ? explode('|', $row['tags']) : [];
                    if (!$valid) {
                        continue;
                    }
                    $explodeEmail = explode('@', $email);
                    $subscriber = [
                        'seller_id' => $currentUserId,
                        'name' => $name,
                        'email' => $email,
                        'phone' => $phone,
                        'city' => $city,
                        'state' => $state,
                        'country' => $country,
                        'domain' => trim($explodeEmail[1]),
                        'created_at' => currentTime(),
                        'updated_at' => currentTime(),
                        'status' => SubscriberStatusEnum::NEW,
                        'email_subscribed' => SubscriberEnum::IS_EMAIL_SUBSCRIBED,
                    ];
                    // For limit sending email to from_system subscribers when seller use system mailer
                    if ($isFromSystem) {
                        $subscriber['from_system'] = SubscriberEnum::IS_FROM_SYSTEM;
                    } else {
                        $subscriber['from_system'] = SubscriberEnum::IS_NOT_FROM_SYSTEM;
                    }
                    if (!empty($tags)) {
                        $subscriber['tags'] = $tags;
                        $subscribers_has_tags[] = $subscriber;
                    } else {
                        $subscribers[] = $subscriber;
                    }
                }
                try {
                    ImportChunkedSubscribersJob::dispatch($subscribers, $subscribers_has_tags);
                } catch (\Exception $exception) {
                    logToDiscord('[' . currentTime() . '] - Seller save subscriber failed. Error: ' . $exception->getMessage(), 'email_marketing', true);
                }
            }
        } catch (\Exception $e) {
            throw new \RuntimeException($e->getMessage(), 403);
        }
    }
}
