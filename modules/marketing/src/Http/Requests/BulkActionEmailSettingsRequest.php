<?php

namespace Modules\Marketing\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Marketing\Enums\EmailSettingsTypeEnum;
use Modules\Marketing\Enums\SubscriberEnum;

class BulkActionEmailSettingsRequest extends Request
{

    public function authorize(): bool
    {
        return !currentUser()->isAuthorizedAccount();
    }

    public function rules(): array
    {
        return [
            'ids' => 'required|array',
            'ids.*' => 'required|integer',
            'action' => 'required|string|in:' . implode(',', [SubscriberEnum::ACTION_EDIT, SubscriberEnum::ACTION_DELETE]),
            'sender_name' => [
                'nullable',
                'string',
                Rule::requiredIf(fn() => request('type') === EmailSettingsTypeEnum::EMAIL_MARKETING && request('action') === 'edit'),
            ],
            'sender_email' => [
                'nullable',
                'email',
                Rule::requiredIf(fn() => request('type') === EmailSettingsTypeEnum::EMAIL_MARKETING && request('action') === 'edit'),
            ],
            'delay' => [
                'nullable',
                'integer',
                Rule::requiredIf(fn() => request('type') === EmailSettingsTypeEnum::EMAIL_MARKETING && request('action') === 'edit'),
            ],
            'limit_per_send' => [
                'nullable',
                'integer',
                Rule::requiredIf(fn() => request('type') === EmailSettingsTypeEnum::EMAIL_MARKETING && request('action') === 'edit'),
            ],
            'stores' => [
                'nullable',
                'array',
                Rule::requiredIf(fn() => request('type') === EmailSettingsTypeEnum::EMAIL_SYSTEM && request('action') === 'edit'),
            ],
            'stores.*' => [
                'nullable',
                'integer',
            ]
        ];
    }
}
