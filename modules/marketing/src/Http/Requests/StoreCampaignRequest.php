<?php
namespace Modules\Marketing\Http\Requests;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Modules\Marketing\Enums\CampaignStatusEnum;

class StoreCampaignRequest extends Request
{
    use PreventsRedirectWhenFailedTrait;

    public function rules(): array
    {
        return [
            'id' => 'nullable',
            'name' => 'required|string',
            'status' => 'required|string',
            'subject' => 'required|string',
            'store_id' => 'required|integer',
            'btn_text' => 'required|string',
            'hero_text' => 'required|string',
            'main_btn_text' => 'required|string',
            'recommendations_text' => 'required|string',
            'content_text' => 'required',
            'products' => "required_if:status,==," . CampaignStatusEnum::SCHEDULED . "|array",
            'recipients' => 'required',
        ];
    }
}
