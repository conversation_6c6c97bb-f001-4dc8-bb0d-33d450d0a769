<?php

namespace Modules\Marketing\Entities;

class SubMailerConfig
{
    public string $senderName = '';
    public string $senderEmail = '';
    public bool $useSystemMailer = false;

    public function __construct()
    {
        $this->senderName = config('mail.from.name');
        $this->senderEmail = getSenderEmail();
    }

    public function setSenderName(string $senderName): SubMailerConfig
    {
        $this->senderName = $senderName;
        return $this;
    }

    public function setSenderEmail(string $senderEmail): SubMailerConfig
    {
        $this->senderEmail = $senderEmail;
        return $this;
    }

    public function setUseSystemMailer(bool $useSystemMailer): SubMailerConfig
    {
        $this->useSystemMailer = $useSystemMailer;
        return $this;
    }
}