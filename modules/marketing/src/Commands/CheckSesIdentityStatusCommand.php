<?php
namespace Modules\Marketing\Commands;

use App\Models\StoreDomain;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use InvalidArgumentException;
use Modules\Marketing\Enums\SesSenderStatus;
use Modules\Marketing\Jobs\SesGetStatusIdentityJob;

class CheckSesIdentityStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketing:sender:status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check SES identity status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if(empty(config('marketing.config.general.aws.ses.queue'))) {
            return;
        }
        try {
            $this->info('Starting process...');
            $pendingSenders = $this->getPendingSenders();
            if ($pendingSenders->isEmpty()) {
                $this->info('No pending sender.');
                return;
            }
            $this->addCheckStatusJobToQueue($pendingSenders);
            $this->info('Done.');
        } catch (\Exception $exception) {
            logToDiscord('[' . currentTime() . '] - Process send marketing email failed: ' . $exception->getMessage(), 'email_marketing', true);
        }
    }

    /**
     *  Start process
     *
     * @param mixed $pendingSenders
     * @return void
     */
    private function addCheckStatusJobToQueue($pendingSenders)
    {
        foreach ($pendingSenders as $sender) {
            $this->info('Checking sender: ' . $sender->sender_email);
            SesGetStatusIdentityJob::dispatch($sender->sender_email);
        }
    }

    /**
     * Get pending campaigns
     *
     * @return Collection<int, Builder|StoreDomain> $pendingSenders
     * @throws InvalidArgumentException
     */
    private function getPendingSenders()
    {
        $pendingSenders = StoreDomain::where('sender_verified', SesSenderStatus::PENDING)
            ->where('sender_next_verify_at', '<', now())
            ->get();
        return $pendingSenders;
    }

    /**
     * Handle the failing job.
     *
     * @param \Throwable $exception
     *
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        graylogError('Error when update identity SES', [
            'message' => $exception->getMessage(),
            'category' => 'ses'
        ]);
    }
}
