<?php
namespace Modules\Marketing\Models;

use App\Models\Model;
use App\Models\Tags;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphToMany;

/**
 * Modules\Marketing\Models\Subscribers
 * @property int $id
 * @property int|null $seller_id
 * @property string|null $name
 * @property string|null $email
 * @property string|null $phone
 * @property string|null $country
 * @property string|null $state
 * @property string|null $city
 * @property string|null $domain
 * @property string|null $status
 * @property int|null $email_subscribed
 * @property int|null $sms_subscribed
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|Subscribers filterDomain($domain)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscribers query()
 * @method static \Illuminate\Database\Eloquent\Builder|Subscribers whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Subscribers whereStatus($value)
 * @mixin \Eloquent
 */
class IndexSubscribers extends Model
{
    use HasFactory;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'subscribers';
    protected $connection = 'singlestore';
    protected $guarded = ['id'];

    protected $fillable = [
        'seller_id',
        'name',
        'email',
        'phone',
        'country',
        'state',
        'city',
        'domain',
        'status',
        'email_subscribed',
        'sms_subscribed',
        'from_system'
    ];

    public function tags(): MorphToMany
    {
        return $this->morphToMany(Tags::class, 'taggable', 'taggables', 'taggable_id', 'tag_id');
    }

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'nickname', 'email', 'status', 'custom_payment')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    public function scopeFilterDomain($query, $domain){
        return $query->select($this->fillable)->where('domain', $domain);
    }

    public function unsubscribe()
    {
        $this->email_subscribed = 0;
        $this->save();
    }
}
