<?php
namespace Modules\Marketing\Models;

use App\Models\Model;
use App\Models\Store;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Modules\Marketing\Models\EmailCampaigns
 * @property int $id
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property int|null $setting_id
 * @property string|null $name
 * @property string|null $slug
 * @property string|null $status
 * @property string|null $email_template
 * @property string|null $send_conditions
 * @property string|null $variables
 * @property string|null $send_time
 * @property int $is_deleted
 * @property \Illuminate\Support\Carbon $hold_at
 * @property \Illuminate\Support\Carbon $deleted_at
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder|EmailCampaigns query()
 */
class EmailCampaigns extends Model
{
    use HasFactory;
    /**
     * table name
     *
     * @var string
     */
    protected $table = 'email_campaigns';

    protected $fillable = [
        'seller_id',
        'store_id',
        'name',
        'slug',
        'status',
        'email_template',
        'send_conditions',
        'variables',
        'send_time',
        'is_deleted',
        'hold_at',
        'setting_id',
        'use_system_mailer',
        'created_at',
        'updated_at'
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'nickname', 'email', 'status', 'custom_payment')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown',
                'nickname' => null
            ]);
    }

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class, 'store_id', 'id');
    }

    public function setting(): BelongsTo
    {
        return $this->belongsTo(EmailSettings::class, 'setting_id', 'id');
    }
}
