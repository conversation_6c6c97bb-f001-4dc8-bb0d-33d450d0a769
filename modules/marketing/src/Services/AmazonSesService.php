<?php

namespace Modules\Marketing\Services;

use Aws\Result;
use Aws\Ses\SesClient;
use Modules\Marketing\Enums\SesSenderStatus;

class AmazonSesService
{
    protected $sesClient;

    public function __construct()
    {
        $key = config('marketing.config.general.aws.ses.key');
        $secret = config('marketing.config.general.aws.ses.secret');
        $region = config('marketing.config.general.aws.ses.region');
        // Create SES client
        $this->sesClient = new SesClient([
            'version' => 'latest',
            'region' => $region,
            'credentials' => [
                'key' => $key,
                'secret' => $secret,
            ],
        ]);
    }

    /**
     * Get all identities
     * @return mixed
     */
    public function getIdentities()
    {
        $response = $this->sesClient->listIdentities();
        if ($response->hasKey('Identities')) {
            return $response['Identities'];
        }
        return null;
    }

    /**
     * Get all identities
     *
     * @return mixed
     */
    public function listIdentities()
    {
        $maxPage = 10;
        $nextToken = null;
        do {
            $result = $this->sesClient->listIdentities([
                'IdentityType' => 'EmailAddress',
                'MaxItems' => 1000,
                'NextToken' => $nextToken,
            ]);
            $response = $result->toArray();
            graylogInfo('List identities', [
                'data' => $response,
                'category' => 'ses'
            ]);
            $nextToken = $result->get('NextToken');
            if (empty($nextToken)) {
                break;
            }
            # sleep for 1 second to avoid rate limiting
            sleep(1);
            $maxPage--;
            if ($maxPage <= 0) {
                break;
            }
        } while ($nextToken);
    }

    /**
     * Get verification status of an email address
     * @param mixed $identity
     * @return bool|int
     */
    public function getVerificationStatus($identity)
    {
        $result = $this->sesClient->getIdentityVerificationAttributes([
            'Identities' => [$identity],
        ]);
        if ($result->hasKey('VerificationAttributes')) {
            if (isset($result['VerificationAttributes'][$identity])) {
                $status = $result['VerificationAttributes'][$identity]['VerificationStatus'] ?? null;
                if ($status === null) {
                    return SesSenderStatus::NOT_VERIFIED;
                }
                if ($status === 'Success') {
                    return SesSenderStatus::SUCCESS;
                } elseif ($status === 'Pending') {
                    return SesSenderStatus::PENDING;
                } elseif ($status === 'Failed') {
                    return SesSenderStatus::FAILED;
                } else {
                    return SesSenderStatus::NOT_VERIFIED;
                }
            } else {
                return SesSenderStatus::NOT_VERIFIED;
            }
        }
        return false;
    }

    /**
     * Add a sender to SES
     * @param mixed $email
     * @return Result|bool
     */
    public function addEmailAddress($email)
    {
        $result = $this->sesClient->verifyEmailIdentity([
            'EmailAddress' => $email,
        ]);
        if ($result->get('@metadata')['statusCode'] === 200) {
            return true;
        }
        return false;
    }

    /**
     * Add a sender to SES
     * @param mixed $identity
     * @return Result|bool
     */
    public function deleteIdentity($identity)
    {
        $result = $this->sesClient->deleteIdentity([
            'Identity' => $identity,
        ]);
        if ($result->get('@metadata')['statusCode'] === 200) {
            return true;
        }
        return false;
    }

    /**
     * Add a domain to SES
     * @param mixed $domain
     * @return array{domain: mixed, dns_records: array{dkim: array{name: string, type: string, value: string}[], txt: array{name: string, type: string, value: mixed}}}
     */
    public function addDomain($domain)
    {
        // Verify domain identity
        $result = $this->sesClient->verifyDomainIdentity([
            'Domain' => $domain,
        ]);

        $verificationToken = $result->hasKey('VerificationToken') ? $result['VerificationToken'] : null;

        // Get DKIM verification tokens
        $result = $this->sesClient->verifyDomainDkim([
            'Domain' => $domain,
        ]);
        $dkimTokens = $result->hasKey('DkimTokens') ? $result['DkimTokens'] : null;

        // Get DKIM DNS records
        $dkimRecords = [];
        if (!empty($dkimTokens)) {
            foreach ($dkimTokens as $token) {
                $dkimRecords[] = [
                    'name' => "{$token}._domainkey.",
                    'type' => 'CNAME',
                    'value' => "{$token}.dkim.amazonses.com",
                ];
            }
        }

        // Construct TXT record for domain verification
        $txtRecord = [
            'name' => "_amazonses.",
            'type' => 'TXT',
            'value' => $verificationToken,
        ];

        // Return the DNS records that need to be added to Cloudflare
        $dnsRecords = [
            'dkim' => $dkimRecords,
            'txt' => $txtRecord,
        ];

        return $dnsRecords;
    }
}
