<?php
namespace Modules\Marketing\Listeners;

use App\Models\Campaign;
use Modules\Marketing\Events\UpdateProductVariablesEvent;
use Modules\Marketing\Models\EmailCampaigns;
use Modules\Marketing\Supports\Helper;
use Modules\Marketing\Supports\JsonHelper;

class UpdateProductVariablesListener
{
    /**
     * @param UpdateProductVariablesEvent $event
     * @return void
     * @throws \Exception
     */
    public function handle(UpdateProductVariablesEvent $event)
    {
        $campaign_id = $event->campaign_id;
        $products = $event->products;
        if(empty($campaign_id) || empty($products)) {
            graylogInfo('[' . currentTime() . '] - Email campaign not found. #ID: '. $campaign_id, [
                'category' => 'email_marketing',
            ]);
            return;
        }
        $campaign = EmailCampaigns::query()->firstWhere('id', $campaign_id);
        if($campaign === null) {
            graylogInfo('[' . currentTime() . '] - Email campaign not found. #ID: '. $campaign_id, [
                'category' => 'email_marketing',
            ]);
            return;
        }
        $store_url = Helper::getFullStoreUrl($campaign->store);
        $short_store_url = Helper::createShortUrl($store_url);
        if(empty($short_store_url)) {
            logToDiscord('[' . currentTime() . '] - Can not create a short url. #Campaign ID: '. $campaign_id, 'email_marketing', true);
            return;
        }
        $variables = JsonHelper::decode($campaign->variables, true);
        $variables['store_url'] = $short_store_url;
        $variables['store_name'] = $campaign->store->name;
        $variables['store_color'] = $campaign->store->default_color ?? 'rgb(41, 199, 71)';
        $variables['store_logo'] = imgUrl($campaign->store->logo_url, 'logo');
        $variables['store_address'] = $campaign->store->name . ' | ' . $campaign->store->address;
        $variables['faq_url'] = Helper::createShortUrl($store_url . '/page/faq');
        $variables['contact_url'] = Helper::createShortUrl($store_url . '/page/contact-us');
        $variables['unsubscribe_url'] = Helper::createShortUrl($store_url . '/api/marketing/email/unsubscribe') . '?sid={{subscriber_id}}&cid={{campaign_id}}';
        $variables['image_open_url'] = Helper::createShortUrl($store_url . '/api/marketing/email/open') . '?seid={{email_id}}';
        $variables['product_click_url'] = Helper::createShortUrl($store_url . '/api/marketing/email/click') . '?pid={{product_id}}&seid={{email_id}}&slid=' . $campaign->seller_id;
        $products_variables = array();
        $seller = currentUser($campaign->seller_id)->getInfoAccess();
        foreach ($products as $product_id) {
            $query = Campaign::query()
                ->onSellerConnection($seller)
                ->where('seller_id', $campaign->seller_id);
            $product = $query->firstWhere('id', $product_id);
            if($product === null) {
                continue;
            }
            $products_variables[] = array(
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_price' => '$' . formatCurrency($product->price,'USD', 'en-US'),
                'product_image' => imgUrl($product->thumb_url, 'preview_design')
            );
        }
        $variables['products_variables'] = $products_variables;
        $campaign->variables = JsonHelper::encode($variables);
        if($campaign->save()) {
            graylogInfo('[' . currentTime() . '] - Email campaign variables updated. #ID: '. $campaign_id, [
                'category' => 'email_marketing',
            ]);
            return;
        }
        logToDiscord('[' . currentTime() . '] - Email campaign variables can not update. #ID: '. $campaign_id, 'email_marketing', true);
    }
}
