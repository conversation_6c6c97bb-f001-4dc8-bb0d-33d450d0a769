<?php
namespace Modules\Marketing\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Marketing\Events\SendMailEvent;
use Modules\Marketing\Events\StoreCampaignSubscribersEvent;
use Modules\Marketing\Events\UpdateEmailSentStatusEvent;
use Modules\Marketing\Events\UpdateEmailSettingUsedTimeEvent;
use Modules\Marketing\Events\UpdateProductVariablesEvent;
use Modules\Marketing\Listeners\SendMailListener;
use Modules\Marketing\Listeners\StoreCampaignSubscribersListener;
use Modules\Marketing\Listeners\UpdateEmailSentStatusListener;
use Modules\Marketing\Listeners\UpdateEmailSettingUsedTimeListener;
use Modules\Marketing\Listeners\UpdateProductVariablesListener;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        SendMailEvent::class => [
            SendMailListener::class,
        ],
        UpdateEmailSentStatusEvent::class => [
            UpdateEmailSentStatusListener::class,
        ],
        UpdateProductVariablesEvent::class => [
            UpdateProductVariablesListener::class,
        ],
        StoreCampaignSubscribersEvent::class => [
            StoreCampaignSubscribersListener::class,
        ],
        UpdateEmailSettingUsedTimeEvent::class => [
            UpdateEmailSettingUsedTimeListener::class,
        ],
    ];
    public function boot()
    {
        parent::boot();
    }
}
