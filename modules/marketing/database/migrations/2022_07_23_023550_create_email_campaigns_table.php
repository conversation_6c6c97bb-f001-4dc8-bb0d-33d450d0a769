<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmailCampaignsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_campaigns', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id')->index('email_campaigns_seller_id')->comment('Seller id');
            $table->foreign('seller_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');
            $table->unsignedBigInteger('store_id')->index('email_campaigns_store_id')->comment('Store id');
            $table->foreign('store_id')
                ->references('id')
                ->on('store')
                ->onDelete('cascade');
            $table->string('name', 250)->comment('Name of the campaign');
            $table->string('slug')->comment('Slug of the campaign')->nullable();
            $table->string('email_template', 200)->nullable();
            $table->json('variables')->nullable();
            $table->enum('status', [
                'draft',
                'new',
                'sending',
                'cancelled',
                'finished',
            ])->index('email_campaigns_status')->default('draft');
            $table->json('send_conditions')->nullable();
            $table->timestamp('send_time')->nullable();
            $table->boolean('is_deleted')->index('email_campaigns_is_deleted')->default(0);
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
