<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableEmailSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('email_settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id')->index('email_settings_seller_id')->comment('Seller id');
            $table->foreign('seller_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');
            $table->enum('driver', [
                'smtp',
                'ses',
                'mailgun',
            ])->index('email_settings_driver')->default('smtp');
            $table->string('sender_name', 250)->comment('Sender name');
            $table->string('sender_email', 250)->comment('Sender email');
            $table->mediumText('settings')->comment('All settings of email driver.');
            $table->tinyInteger('delay')->index('email_settings_delay')->default(0)->comment('Setting delay time between 2 times send email');
            $table->integer('limit_per_send')->index('email_settings_limit_per_send')->default(500)->comment('Setting limit send per time');
            $table->enum('status', [
                'valid',
                'invalid',
            ])->index('email_settings_status')->default('invalid');
            $table->boolean('is_deleted')->index('email_settings_is_deleted')->default(0);
            $table->timestamp('last_used_at')->index('email_settings_last_used_at')->nullable();
            $table->timestamp('hold_at')->index('email_settings_hold_at')->nullable();
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
