<?php

namespace Modules\Etsy\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Modules\Etsy\Supports\API;

class HomeController extends Controller
{
    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        return json_success('Etsy API v1');
    }

    /**
     * @return \Illuminate\Http\JsonResponse
     */
    public function ping(): JsonResponse
    {
        try {
            $response = API::ping();

            if ($response->successful()) {
                return json_success($response->json(), 'Ping success !');
            }

            return json_error('Etsy API is not working');
        } catch (\Exception $exception) {
            return json_exception($exception);
        }
    }
}
