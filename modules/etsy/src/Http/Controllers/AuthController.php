<?php

namespace Modules\Etsy\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Modules\Etsy\Supports\API;
use Throwable;

class AuthController extends Controller
{
    /**
     * @param     \Illuminate\Http\Request     $request
     *
     * @return void
     */
    public function authUrl(Request $request)
    {
        return json_success(
            API::generateOAuthCode()
        );
    }

    /**
     * @param     \Illuminate\Http\Request     $request
     *
     * @return void
     */
    public function authCallback(Request $request)
    {
        Log::debug($request->all());

        $this->getAccessToken($request->code);
    }

    /**
     * @throws Throwable
     */
    public function getAccessToken(string $authCode): void
    {
        $code = API::generateOAuthCode();
        $response = API::fetchOAuthToken($authCode, $code['code_verifier']);
        Log::debug($response);
    }
}
