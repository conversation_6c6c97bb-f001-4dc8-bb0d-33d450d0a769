<?php

namespace Modules\Campaign\Filters;

use App\Filters\AbstractFilter;
use App\Models\UserInfo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

/**
 *
 */
class ImportCampaignsDataFilter extends AbstractFilter
{
    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function campaignId($q, $value): void
    {
        $q->where('campaign_id', (int) $value);
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function sellerId($q, $value): void
    {
        $q->whereIn('seller_id', to_list_int($value));
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function campaignName($q, $value): void
    {
        $q->where('campaign_name', 'like', "%$value%");
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function collection($q, $value): void
    {
        $q->where('collection', 'like', "%$value%");
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function type($q, $value): void
    {
        $q->whereIn('type', to_list($value, Str::lower(...)));
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function status($q, $value): void
    {
        $q->whereIn('status', to_list($value, Str::lower(...)));
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function systemStatus($q, $value): void
    {
       $q->where(function($q) use($value) {
           foreach (to_list($value, Str::lower(...)) as $v) {
               $q->orWhere('system_status', $v);
           }
       });
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function createdAt($q, $value): void
    {
        $q->whereDate('created_at', $value);
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function updatedAt($q, $value): void
    {
        $q->whereDate('updated_at', $value);
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function campaignSlug($q, $value): void
    {
        $q->where('campaign_slug', $value);
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function orderBy($q, $values): void
    {
        foreach (to_list($values) as $value) {
            $v = explode(':', $value);

            $q->orderBy($v[0], $v[1] ?? 'desc');
        }
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                            $value
     *
     * @return void
     */
    public function time(Builder $q, $value): void
    {
        $this->timeFilter($q, $value);
    }

    /**
     * @param $q
     * @param $value
     *
     * @return void
     */
    public function sellerBulkCampaignStatus($q, $value): void
    {
        $q->where(function($q) use($value) {
            match ($value) {
                UserInfo::VAL_BULK_CAMPAIGN_ACTIVE => $q->whereDoesntHave('seller_info', fn ($q) => $q->where('key', UserInfo::KEY_BULK_CAMPAIGN_STATUS)),
                UserInfo::VAL_BULK_CAMPAIGN_INACTIVE => $q->whereHas('seller_info', fn ($q) => $q->where('key', UserInfo::KEY_BULK_CAMPAIGN_STATUS)),
            };
        });
    }
}
