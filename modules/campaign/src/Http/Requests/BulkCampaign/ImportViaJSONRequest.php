<?php

namespace Modules\Campaign\Http\Requests\BulkCampaign;

use Illuminate\Foundation\Http\FormRequest;

class ImportViaJSONRequest extends FormRequest
{
    /**
     * @return true
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'campaign_id' => 'required|int',
            'options' => 'array',
            'rows' => 'required|array',
            'rows.*.campaign_name' => 'required|string',
            'rows.*.designs' => 'required|array|min:1',
            'ipAddress' => 'nullable|string',
            'deviceId' => 'nullable|string',
            'sessionId' => 'nullable|string',
            'rows.*.collection' => 'nullable|string',
        ];
    }
}
