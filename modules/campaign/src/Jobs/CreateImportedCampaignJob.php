<?php

namespace Modules\Campaign\Jobs;

use App\Enums\CampaignPublicStatusEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\QueueName;
use App\Jobs\SyncProductsToElasticSearchJob;
use App\Models\Campaign;
use App\Models\Collection;
use App\Models\ExpressCampaign;
use App\Models\File;
use App\Models\Product;
use App\Models\Store;
use App\Models\StoreProduct;
use App\Models\Upsell;
use App\Models\User;
use App\Services\CampaignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Modules\Campaign\Enums\ImportCampaignStatusEnum;
use Modules\Campaign\Enums\ImportCampaignTypeEnum;
use Modules\Campaign\Enums\ProductSystemTypeEnum;
use Modules\Campaign\Models\ImportCampaignsData;

class CreateImportedCampaignJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $main_product_templates = [];
    private array $collections = [];

    public function __construct(readonly protected int $importedCampaignId)
    {
        $this->onQueue(QueueName::BULK_CAMPAIGN);
    }

    public function uniqueId(): string
    {
        return "imported-campaign-" . $this->importedCampaignId;
    }

    public function handle(): void
    {
        try {
            $importedCampaign = ImportCampaignsData::query()
                ->where('id', $this->importedCampaignId)
                ->where('status', ImportCampaignStatusEnum::PENDING)
                ->whereNotIn('type', [ImportCampaignTypeEnum::REGULAR, ImportCampaignTypeEnum::REGULAR_MULTI_SPACE, ImportCampaignTypeEnum::EXPRESS_MULTI_SPACE])
                ->first();
            if (empty($importedCampaign)) {
                return;
            }
            $limitUsers = CampaignService::listUserLimitCreateCampaign();
            $seller_id = $importedCampaign->seller_id;
            if (in_array($seller_id, $limitUsers)) {
                return;
            }
            $seller = currentUser($seller_id);
            if ($seller->getNumberCampaignCanCreate() <= 0) {
                User::query()
                    ->whereKey($seller_id)
                    ->update(['hold_launch_campaign_at' => now()]);
                return;
            }

            $importedCampaign->status = ImportCampaignStatusEnum::PROCESSING;
            $importedCampaign->save();
            $storefronts = $importedCampaign->store_ids ? json_decode($importedCampaign->store_ids, true) : [];
            $logs = [];
            $campaign_id = 0;
            $campaign_slug = null;
            $default_product_id = 0;
            if ($importedCampaign->type === ImportCampaignTypeEnum::AOP || $importedCampaign->type === ImportCampaignTypeEnum::CUSTOM) {
                $campaign_data = array();
                $campaign_data['prefix'] = $importedCampaign->prefix;
                $campaign_data['suffix'] = $importedCampaign->suffix;
                $products = json_decode($importedCampaign->products, true);
                $main_product_id = $products[0]['template_id'] ?? 0;
                $thumb_url = $products[0]['thumb_url'] ?? null;
                if (empty($this->main_product_templates[$main_product_id])) {
                    $main_product_template = Product::query()->find($main_product_id);
                    $this->main_product_templates[$main_product_id] = $main_product_template;
                } else {
                    $main_product_template = $this->main_product_templates[$main_product_id];
                }
                if(!empty($main_product_template) && $main_product_template->status !== ProductStatus::ACTIVE) {
                    $importedCampaign->logs = 'Product template is not active. ' . $main_product_template->sku;
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }
                $campaign_data['id'] = $importedCampaign->campaign_id;
                $campaign_data['seller_id'] = $seller_id;
                $campaign_data['name'] = $importedCampaign->campaign_name;
                $campaign_data['description'] = $importedCampaign->campaign_description;
                $campaign_data['thumb_url'] = $thumb_url;
                $campaign_data['currency_code'] = $importedCampaign->currency;
                $campaign_data['pricing_mode'] = $importedCampaign->pricing_mode;
                $campaign_data['market_location'] = $importedCampaign->market_location;
                if ($importedCampaign->type === ImportCampaignTypeEnum::AOP) {
                    $campaign_data['status'] = ProductStatus::PENDING;
                }
                $campaign_data['system_type'] = $importedCampaign->type;
                $campaign_data['public_status'] = CampaignPublicStatusEnum::NO;
                $campaign = CampaignService::createDraftCampaign($campaign_data, true);
                if(empty($campaign)) {
                    $importedCampaign->logs = 'Create campaign failed';
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }
                SyncSlugJob::dispatchSync(ids: [$campaign->id], seller: $seller);
                $campaign_id = $campaign->id;
                $campaign_slug = $campaign->slug;
                // Now collectionName can be multiple separated by |
                $collection_names = explode('|', trim($importedCampaign->collection));
                foreach ($collection_names as $collection_name) {
                    $collection_name = trim($collection_name);
                    $this->addCampaignCollection($collection_name, $campaign_id, $seller_id);
                }
                foreach ($products as $product) {
                    $template_id = $product['template_id'] ?? 0;
                    if (empty($this->main_product_templates[$template_id])) {
                        $product_template = Product::query()->find($template_id);
                        $this->main_product_templates[$template_id] = $product_template;
                    } else {
                        $product_template = $this->main_product_templates[$template_id];
                    }
                    if (!empty($product_template) && $product_template->status !== ProductStatus::ACTIVE) {
                        $logs[] = 'Product template is not active. ' . $product_template->sku;
                        continue;
                    }
                    $price = (float)$product['price'];
                    $default_option = $product['default_option'];
                    $colors = [];
                    if (isset($product['selectedColors'])) {
                        foreach ($product['selectedColors'] as $color) {
                            $colors[] = $color['name'];
                        }
                    }
                    if (!$product_template->options) {
                        graylogInfo('Template dont have options: ' . $product_template->id, [
                            'category' => 'create_imported_campaign',
                            'template_id' => $product_template->id,
                            'bulk_id' => $importedCampaign->id,
                            'campaign_id' => $importedCampaign->campaign_id,
                            'template_campaign_id' => $importedCampaign->template_campaign_id
                        ]);
                        $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                        $importedCampaign->logs = "Product template is invalid options";
                        $importedCampaign->save();
                        continue;
                    }
                    $options = Str::isJson($product_template->options) ? json_decode($product_template->options, true) : [];
                    if (count($colors) > 0) {
                        $options['color'] = $colors;
                    }
                    $options = json_encode($options);
                    $sku = $product_template->sku;

                    $pricing = get_base_min_max_price_of_product($product_template, $importedCampaign->market_location, $importedCampaign->currency);
                    $min_price = $pricing['min_price'];
                    $max_price = $pricing['max_price'];

                    $status = ProductStatus::DRAFT;
                    if (empty($price) || $price < $min_price || $price > $max_price) {
                        $status = ProductStatus::ERROR;
                    }
                    if ($importedCampaign->type === ImportCampaignTypeEnum::AOP) {
                        $status = ProductStatus::PENDING;
                    }
                    $insertProduct = [
                        'name' => $product_template->name,
                        'default_option' => $default_option,
                        'campaign_id' => $campaign_id,
                        'seller_id' => $seller_id,
                        'auth_id' => $seller_id,
                        'template_id' => $product_template->id,
                        'product_type' => ProductType::PRODUCT,
                        'thumb_url' => $product['thumb_url'],
                        'price' => $price,
                        'currency_code' => $importedCampaign->currency,
                        'pricing_mode' => $importedCampaign->pricing_mode,
                        'market_location' => $importedCampaign->market_location,
                        'status' => $status,
                        'options' => $options,
                        'sku' => $sku,
                        'base_cost' => $product_template->base_cost,
                        'shipping_cost' => $product_template->shipping_cost,
                        'description' => $product_template->description,
                        'description_ts' => $product_template->description_ts,
                        'priority' => (int)$product_template->priority,
                        'full_printed' => $product_template->full_printed,
                        'system_type' => $importedCampaign->type,
                        'public_status' => CampaignPublicStatusEnum::NO,
                        'template_campaign_id' => $importedCampaign->template_campaign_id
                    ];
                    $product_id = Product::query()
                        ->onSellerConnection($seller)
                        ->insertGetId($insertProduct);
                    if (empty($product_id)) {
                        $logs[] = 'Create product failed. ' . $product_template->sku;
                        continue;
                    }
                    if (empty($default_product_id)) {
                        $default_product_id = $product_id;
                        Campaign::query()->onSellerConnection($seller)->where([
                            'id' => $campaign_id,
                            'seller_id' => $seller_id,
                        ])->update([
                            'default_product_id' => $default_product_id,
                            'default_option' => $default_option,
                            'template_id' => $product_template->id,
                            'price' => $price,
                            'status' => $status,
                            'base_cost' => $product_template->base_cost,
                            'shipping_cost' => $product_template->shipping_cost,
                            'options' => $options,
                            'sku' => $sku,
                        ]);
                    }
                    // Create mockups of product
                    if (!empty($product['custom_mockups'])) {
                        foreach ($product['custom_mockups'] as $mockup) {
                            File::query()
                                ->onSellerConnection($seller)
                                ->create([
                                    'type' => FileTypeEnum::IMAGE,
                                    'product_id' => $product_id,
                                    'campaign_id' => $campaign_id,
                                    'file_url' => $mockup,
                                    'type_detail' => FileRenderType::CUSTOM,
                                    'seller_id' => $seller_id,
                                ]);
                        }
                    }
                    // Create designs of product
                    if (!empty($product['design_urls'])) {
                        if ($importedCampaign->type === ImportCampaignTypeEnum::AOP) {
                            $design_data = $product['design_urls'][0];
                            $array_where = [
                                'seller_id' => $seller_id,
                                'campaign_id' => $campaign_id,
                                'product_id' => $product_id,
                                'option' => 'print',
                                'print_space' => $design_data['name'],
                                'type' => FileTypeEnum::DESIGN,
                            ];
                            $design = File::query()
                                ->onSellerConnection($seller)
                                ->where($array_where)->first();
                            if (!empty($design)) {
                                $fileId = $design->id;
                                File::query()
                                    ->onSellerConnection($seller)
                                    ->whereKey($fileId)->update(array(
                                        'file_url' => null,
                                        'file_url_2' => $design_data['url'],
                                    ));
                            } else {
                                File::query()
                                    ->onSellerConnection($seller)
                                    ->create(array_merge($array_where, [
                                        'file_url_2' => $design_data['url'],
                                        'campaign_id' => $campaign_id,
                                    ]));
                            }
                        } else {
                            $print_spaces = [];
                            array_map(static function ($item) use (&$print_spaces) {
                                if (!empty($item['name'])) {
                                    $print_spaces[$item['name']] = Str::lower($item['name']);
                                }
                                return $item;
                            }, !empty($product_template->print_spaces) ? json_decode($product_template->print_spaces, true) : []);
                            $print_spaces = array_values($print_spaces);
                            $designs = [];
                            $full_printed = (int)$product_template->full_printed;
                            foreach ($product['design_urls'] as $design) {
                                if ($design['name'] === 'default') {
                                    if ($full_printed === ProductPrintType::PRINT_2D) {
                                        $designs[$print_spaces[0]] = [
                                            'type' => FileTypeEnum::DESIGN,
                                            'product_id' => $product_id,
                                            'campaign_id' => $campaign_id,
                                            'file_url' => $design['url'],
                                            'option' => 'print',
                                            'print_space' => $print_spaces[0],
                                            'seller_id' => $seller_id,
                                        ];
                                    } else {
                                        foreach ($print_spaces as $print_space) {
                                            $designs[$print_space] = [
                                                'type' => FileTypeEnum::DESIGN,
                                                'product_id' => $product_id,
                                                'campaign_id' => $campaign_id,
                                                'file_url' => $design['url'],
                                                'option' => 'print',
                                                'print_space' => $print_space,
                                                'seller_id' => $seller_id,
                                            ];
                                        }
                                    }
                                } else {
                                    foreach ($print_spaces as $print_space) {
                                        if (Str::contains($design['name'], $print_space)) {
                                            $designs[$print_space] = [
                                                'type' => FileTypeEnum::DESIGN,
                                                'product_id' => $product_id,
                                                'campaign_id' => $campaign_id,
                                                'file_url' => $design['url'],
                                                'option' => 'print',
                                                'print_space' => $print_space,
                                                'seller_id' => $seller_id,
                                            ];
                                            break;
                                        }
                                    }
                                }
                            }
                            File::query()
                                ->onSellerConnection($seller)
                                ->insert(array_values($designs));
                        }
                    }
                }
                dispatch(new DownloadCampaignImagesJob($campaign_id, $seller_id, !empty($product['design_urls'])))->onQueue(config('campaign.config.general.queue'));
            }
            else if ($importedCampaign->type === ImportCampaignTypeEnum::MOCKUP) {
                $prefix = trim($importedCampaign->prefix ?? '');
                $suffix = trim($importedCampaign->suffix ?? '');
                $inputCampaign = json_decode($importedCampaign->products, true);
                $description = $importedCampaign->campaign_description;
                $name = $importedCampaign->campaign_name;
                $templateId = $inputCampaign['template_id'] ?? 0;
                $customMockups = json_decode($importedCampaign->mockups, true);

                $template = Product::query()
                    ->onSellerConnection($seller)
                    ->whereKey($templateId)
                    ->where('status', ProductStatus::ACTIVE)
                    ->first();
                if(empty($template)) {
                    $importedCampaign->logs = 'Product template is not active. ' . $templateId;
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }

                $campaignData = [
                    'id' => $importedCampaign->campaign_id,
                    'name' => $name,
                    'description' => !empty($description) ? $description : $name,
                    'thumb_url' => $inputCampaign['thumb_url'],
                    'currency_code' => $importedCampaign->currency,
                    'pricing_mode' => $importedCampaign->pricing_mode,
                    'market_location' => $importedCampaign->market_location,
                    'template_id' => $template->campaign_id,
                    'default_product_id' => $template->id,
                    'default_option' => $template->default_option,
                    'price' => $template->price,
                    'base_cost' => $template->base_cost,
                    'shipping_cost' => $template->shipping_cost,
                    'options' => $template->options,
                    'sku' => $template->sku,
                    'system_type' => ProductSystemTypeEnum::MOCKUP,
                    'product_type' => ProductType::CAMPAIGN,
                    'public_status' => CampaignPublicStatusEnum::NO,
                    'template_campaign_id' => $templateId ?? null
                ];
                if(!empty($prefix)) {
                    $campaignData['prefix'] = $prefix;
                }
                if(!empty($suffix)) {
                    $campaignData['suffix'] = $suffix;
                }
                $outputCampaign = CampaignService::createDraftCampaign($campaignData ,true);

                if(empty($outputCampaign)) {
                    $importedCampaign->logs = 'Create campaign failed';
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }
                SyncSlugJob::dispatchSync(ids: [$outputCampaign->id], seller: $seller);
                $campaign_id = $outputCampaign->id;
                $campaign_slug = $outputCampaign->slug;
                $collectionName = trim($importedCampaign->collection ?? '');
                if(!empty($collectionName)) {
                    // Now collectionName can be multiple separated by |
                    $collectionNames = explode('|', $collectionName);
                    foreach ($collectionNames as $collectionName) {
                        $collectionName = trim($collectionName);
                        if (empty($collectionName)) {
                            continue;
                        }
                        $collection = Collection::query()
                            ->select(['id', 'name'])
                            ->firstWhere('name', $collectionName);
                        if ($collection) {
                            $collectionId = $collection->id;
                        } else {
                            $newCollection = CampaignService::addCollection($collectionName);
                            $collectionId = $newCollection->id;
                        }
                        CampaignService::addCampaignToCollection($campaign_id, $seller_id, $collectionId);
                    }
                }

                foreach ($customMockups as $mockup) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->create([
                            'type' => FileTypeEnum::IMAGE,
                            'campaign_id' => $campaign_id,
                            'file_url' => $mockup,
                            'type_detail' => FileRenderType::CUSTOM,
                            'seller_id' => $seller_id,
                        ]);
                }
            }
            else if ($importedCampaign->type === ImportCampaignTypeEnum::EXPRESS) {
                $isSellName = $seller->getInfo()->isSellName();
                $inputCampaignId = $importedCampaign->campaign_id;
                $defaultPrintSpace = $importedCampaign->print_space;
                $fileUrl = $importedCampaign->designs;
                $collectionName = trim($importedCampaign->collection ?? '');
                $name = trim($importedCampaign->campaign_name);
                $slug = CampaignService::generateCampaignSlug($name, $importedCampaign->prefix ?? '', $importedCampaign->suffix ?? '');
                $user = currentUser();
                $existCampaign = Campaign::query()
                    ->onSellerConnection($user)
                    ->select(['id', 'name', 'slug', 'thumb_url', 'status'])
                    ->where(['slug' => $slug, 'seller_id' => $seller_id])
                    ->first();
                if ($existCampaign && $isSellName) {
                    $importedCampaign->logs = 'Campaign slug is exist. ' . $slug . " don't create duplicate campaign for seller sell name";
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }

                if ($isSellName && empty($collectionName)) {
                    $collectionName = $name;
                }

                $inputCampaign = ExpressCampaign::query()
                    ->whereKey($inputCampaignId)
                    ->where('seller_id', $seller_id)
                    ->with('defaultProduct')
                    ->first();

                if (empty($inputCampaign)) {
                    $importedCampaign->logs = 'Campaign is not exist. ' . $inputCampaignId;
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }

                $templateProductIds = $inputCampaign->template_products->pluck('template_id')->toArray();
                $mockupIds = File::query()
                    ->whereIn('product_id', $templateProductIds)
                    ->where([
                        'type' => FileTypeEnum::MOCKUP,
                        'render_type' => FileRenderType::RENDER_2D
                    ])
                    ->get()
                    ->pluck('type_detail')
                    ->toArray();

                $mockupIds = array_filter($mockupIds);
                $parsedMockupIds = implode(',', $mockupIds);

                $newCampaign = $inputCampaign->replicate([
                    'slug',
                    'tm_status',
                    'sync_status',
                    'elastic_document_id',
                    'created_at',
                    'updated_at'
                ])->fill([
                    'name' => $name,
                    'slug' => $slug,
                    'product_type' => ProductType::CAMPAIGN_EXPRESS,
                    'status' => ProductStatus::ACTIVE,
                    'template_id' => $inputCampaignId,
                    'default_option' => $defaultPrintSpace,
                    'public_status' => in_array($inputCampaign->public_status, [CampaignPublicStatusEnum::YES, CampaignPublicStatusEnum::APPROVED], true) ? CampaignPublicStatusEnum::YES : CampaignPublicStatusEnum::NO,
                    'template_campaign_id' => $inputCampaign?->id,
                ]);
                $newCampaign->save();
                SyncSlugJob::dispatchSync(ids: [$newCampaign->id], seller: $seller);
                $campaign_id = $newCampaign->id;
                $campaign_slug = $newCampaign->slug;

                CampaignService::cloneCollections($inputCampaignId, $campaign_id, $seller_id);
                CampaignService::cloneStores($inputCampaignId, $campaign_id);
                CampaignService::cloneUpsell($inputCampaignId, $campaign_id, $seller_id);

                if (!$newCampaign->save()) {
                    File::query()
                        ->where('campaign_id', $campaign_id)
                        ->delete();
                    Upsell::query()
                        ->where('product_id', $campaign_id)
                        ->where('seller_id', $seller_id)
                        ->delete();

                    $importedCampaign->logs = 'Create campaign failed';
                    $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                    $importedCampaign->save();
                    return;
                }

                dispatch(new HandleExpressCampaignImage($campaign_id, $fileUrl, $parsedMockupIds, $defaultPrintSpace, sellerId: $seller_id))->onQueue(config('campaign.config.general.queue'));

                if (!empty($collectionName)) {
                    // Now collectionName can be multiple separated by |
                    $collectionNames = explode('|', $collectionName);
                    foreach ($collectionNames as $collectionName) {
                        $collectionName = trim($collectionName);
                        if (empty($collectionName)) {
                            continue;
                        }
                        $outputCollection = CampaignService::addCollection($collectionName);
                        CampaignService::addCampaignToCollection($campaign_id, $seller_id, $outputCollection->id);
                    }
                }
            }

            if(!empty($logs) || empty($campaign_id)) {
                $importedCampaign->status = ImportCampaignStatusEnum::FAILED;
                $importedCampaign->logs = implode(", ", $logs);
                $importedCampaign->save();
                return;
            }
            $importedCampaign->campaign_id = $campaign_id;
            $importedCampaign->campaign_slug = $campaign_slug;
            $importedCampaign->status = ImportCampaignStatusEnum::COMPLETED;
            $importedCampaign->save();
            dispatch(new SyncProductsToElasticSearchJob($campaign_id));
            if (!empty($storefronts)) {
                StoreProduct::query()->where('product_id', $campaign_id)->delete();
                $totalIds = count($storefronts);
                $totalRows = Store::query()
                    ->whereIn('id', $storefronts)
                    ->where('seller_id', $seller_id)
                    ->count();
            }
            if (isset($totalIds, $totalRows) && $totalIds === $totalRows) {
                $data = [];
                foreach ($storefronts as $storeId) {
                    $data[$storeId] = [
                        'store_id' => $storeId,
                        'product_id' => $campaign_id
                    ];
                }
                StoreProduct::query()->insertOrIgnore($data);
            }

            if ($importedCampaign->type === ImportCampaignTypeEnum::MOCKUP) {
                dispatch(new DownloadCampaignImagesJob($campaign_id, $seller_id))->onQueue(config('campaign.config.general.queue'));
            }
        } catch (\Throwable $exception) {
            logException($exception, 'CreateImportedCampaignJob@handle', 'bulk_campaign', true);
        }
    }

    private function addCampaignCollection($collection_name, $campaign_id, $seller_id) {
        if(empty($collection_name)) {
            return 0;
        }
        if (empty($this->collections[Str::slug($collection_name)])) {
            $collection_id = 0;
            $exists = Collection::query()
                ->select(['id', 'name'])
                ->firstWhere('name', $collection_name);
            if ($exists) {
                $collection_id = $exists['id'];
            } else {
                $newCollection = CampaignService::addCollection($collection_name);
                if ($newCollection) {
                    $collection_id = $newCollection->id;
                }
            }
            $this->collections[Str::slug($collection_name)] = $collection_id;
        } else {
            $collection_id = $this->collections[Str::slug($collection_name)];
        }
        CampaignService::addCampaignToCollection($campaign_id, $seller_id, $collection_id);
        return $collection_id;
    }
}
