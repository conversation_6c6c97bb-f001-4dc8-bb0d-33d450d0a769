<?php

use Illuminate\Support\Facades\Route;
use Modules\Campaign\Http\Controllers\CampaignController;
use Modules\Campaign\Http\Controllers\WebhookController;

Route::group(['prefix' => 'admin', 'middleware' => ['api', 'auth.access.token', 'staff_log']], function () {
    Route::prefix('campaign')->group(function () {
        Route::prefix('bulk-v3')->group(function () {
            Route::post('import-json', [CampaignController::class, 'importBulkCampaignViaJson']);
            Route::post('save-processed-row', [CampaignController::class, 'saveProcessedRow']);
            Route::post('mark-row-invalid', [CampaignController::class, 'markRowInvalid']);
            Route::get('pending-record', [CampaignController::class, 'getPendingRecord']);
        });
    });

    Route::prefix('campaign')->middleware('staff_can:update_campaign')->group(function () {
        Route::prefix('bulk-v3')->group(function () {
            Route::post('retry', [CampaignController::class, 'retry']);
            Route::post('remove', [CampaignController::class, 'remove']);
            Route::post('start', [CampaignController::class, 'start']);
            Route::post('stop', [CampaignController::class, 'stop']);
        });
    });
});

Route::group(['prefix' => 'seller', 'middleware' => ['api', 'auth.access.token']], function () {
    Route::prefix('campaign')->middleware('access_switch_account:update_campaign')->group(function () {
        Route::prefix('bulk-v3')->group(function() {
            Route::post('preview-via-google-driver-folder-url', [CampaignController::class, 'previewBulkCampaignViaGoogleDriverFolderUrl']);
            Route::post('import-via-google-driver-folder-url', [CampaignController::class, 'importBulkCampaignViaGoogleDriverFolderUrl']);
            Route::post('preview-csv', [CampaignController::class, 'previewBulkCampaignViaCSV']);
            Route::post('import-csv', [CampaignController::class, 'importBulkCampaignViaCsv']);
            Route::post('preview-design-files', [CampaignController::class, 'previewBulkCampaignViaDesignFiles']);
            Route::post('import-json', [CampaignController::class, 'importBulkCampaignViaJson']);
            Route::get('rows', [CampaignController::class, 'getBulkCampaignRows']);
            Route::post('save-processed-row', [CampaignController::class, 'saveProcessedRow']);
            Route::post('mark-row-invalid', [CampaignController::class, 'markRowInvalid']);
            Route::get('pending-record', [CampaignController::class, 'getPendingRecord']);
            Route::get('campaigns', [CampaignController::class, 'list']);
            Route::post('group-file-names', [CampaignController::class, 'groupFileNames']);
            Route::post('retry', [CampaignController::class, 'retry']);
            Route::post('remove', [CampaignController::class, 'remove']);
            Route::post('start', [CampaignController::class, 'start']);
            Route::post('stop', [CampaignController::class, 'stop']);
            Route::post('start-all', [CampaignController::class, 'startAll']);
            Route::post('stop-all', [CampaignController::class, 'stopAll']);
            Route::get('origin/{id}', [CampaignController::class, 'originCampaign']);
        });

        // CustomCampaign Router
        Route::group(['prefix' => 'custom', 'middleware' => ['staff_log', 'seller_log']], function () {
            Route::post('{campaignId}/mockup-upload', [CampaignController::class, 'customMockupUpload']);
            Route::post('{campaignId}/batch-mockup-upload', [CampaignController::class, 'batchMockupUpload']);
            Route::post('{campaignId}/mockup-remove', [CampaignController::class, 'customMockupRemove']);
            Route::get('{campaignId}/mockups', [CampaignController::class, 'getMockupList']);
            Route::get('{campaign_id}/images', [CampaignController::class, 'getProductImagesFromCampaign']);
            Route::post('upload-import-file', [CampaignController::class, 'uploadImportFile']);
            Route::post('{campaign_id}/images', [CampaignController::class, 'saveProductsThumbnail']);
            Route::post('import', [CampaignController::class, 'importCustomCampaigns']);
            Route::get('template-products', [CampaignController::class, 'listTemplateProducts']);
            Route::post('mockups-import', [CampaignController::class, 'importMockupsExpressCampaigns']);
            Route::delete('delete-design', [CampaignController::class, 'deleteProductDesign']);
            Route::post('{campaignId}/upload-design', [CampaignController::class, 'uploadProductDesign']);
            Route::post('/upload-mockup', [CampaignController::class, 'uploadCustomMockupForPrintSpace']);
            Route::post('/delete-mockup', [CampaignController::class, 'deleteMockup']);
            Route::get('/read-image', [CampaignController::class, 'readImage']);

        });
        // Mockup Campaign Router
        Route::group(['prefix' => 'mockup', 'middleware' => ['staff_log', 'seller_log']], function () {
            Route::get('detail/{campaignId}', [CampaignController::class, 'campaignDetail']);
            Route::post('update', [CampaignController::class, 'updateMockupCampaign']);
            Route::post('change-default-product/{campaignId}', [CampaignController::class, 'changeDefaultProductMockupCampaign']);
        });
        // AOP Campaign Router
        Route::group(['prefix' => 'aop', 'middleware' => ['staff_log', 'seller_log']], function () {
            Route::post('{campaignId}/upload-design', [CampaignController::class, 'uploadProductAopDesign']);
        });

        Route::post('get-designs-from-file', [CampaignController::class, 'getDesignsFromFile']);
        Route::post('get-designs-from-folder', [CampaignController::class, 'getDesignsFromFolder']);
        Route::post('save-import', [CampaignController::class, 'storeImportCampaign']);
        Route::get('list-import', [CampaignController::class, 'listImportCampaign']);
        Route::post('delete-import', [CampaignController::class, 'deleteImportCampaign']);
        Route::get('export-list-import', [CampaignController::class, 'exportListImportCampaign']);
    });
    Route::post('/campaign/regular-import/pending/{id}', [CampaignController::class, 'updateImportCampaignForRegularCampaign']);
    Route::post('/import-campaign/bulk-update-status', [CampaignController::class, 'bulkUpdateImportCampaignStatus'])->middleware('access_switch_account:update_campaign');
    Route::get('/campaign/get-pending-regular-import', [CampaignController::class, 'getPendingRegularImport']);
    Route::post('/campaign/bulk-regular/log-error', [CampaignController::class, 'bulkRegularLog']);
    Route::post('/campaign/bulk-regular/rerun', [CampaignController::class, 'bulkRegularRerun']);
});

Route::group(['prefix' => 'public'], function () {
    Route::put('psd-render-webhook/{mockup_id}', [WebhookController::class, 'saveMockupForFile'])->name('psd-render-webhook');
    Route::post('render-bulk', [CampaignController::class, 'testBulkCampaign']);
});
