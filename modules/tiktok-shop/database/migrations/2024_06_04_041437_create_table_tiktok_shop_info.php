<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tiktok_shop_info', function (Blueprint $table) {
            $table->id();
            $table->integer('session_id')->index();
            $table->string('store_id')->nullable()->index();
            $table->unsignedBigInteger('seller_id')->index();
            $table->string('shop_id')->index();
            $table->string('code')->nullable()->index();
            $table->string('name')->nullable()->index();
            $table->string('type')->nullable()->index();
            $table->string('region')->nullable()->index();
            $table->string('cipher')->nullable()->index();
            $table->bigInteger('last_order_sync_at')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {

    }
};
