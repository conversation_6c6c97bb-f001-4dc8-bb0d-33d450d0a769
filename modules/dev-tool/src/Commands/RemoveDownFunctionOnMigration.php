<?php

namespace Modules\DevTool\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class RemoveDownFunctionOnMigration extends Command
{
    /**
     * The console command signature.
     *
     * @var string
     */
    protected $signature = 'migration:remove-down {--p= : The folder path}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Remove down function on migration files';

    public function handle() {
        $path = $this->input->getOption('p');
        if (empty($path)) {
            $path = 'database/migrations';
        }
        $location = base_path($path);
        $paths = scan_folder($location);
        $correct_paths = [];
        foreach ($paths as $path) {
            if (!str_ends_with($path, '.php')) {
                continue;
            }
            $correct_paths[] = $path;
        }
        $this->process($correct_paths, $location);
        $this->info('Done');
    }

    /**
     * @param $paths
     * @param $location
     * @return void
     */
    protected function process($paths, $location) {
        foreach ($paths as $path) {
            $path = $location . DIRECTORY_SEPARATOR . $path;
            $contents = File::get($path);
            if (!strpos($contents, 'public function down()')) {
                continue;
            }
            $contents = preg_replace('/public function down\(\)(: void)?\s*{.*?(\n\r|\n|\r)\s\s\s\s}/s', 'public function down(): void {}', $contents);
            File::put($path, $contents);
            $this->info('Removed down function on ' . $path);
        }
    }
}
