<?php

use Illuminate\Support\Facades\File;

if (!function_exists('module_path')) {
    /**
     * @param string|null $path
     * @return string
     */
    function module_path($path = null): string
    {
        return base_path('modules/' . $path);
    }
}

if (!function_exists('scan_folder')) {
    /**
     * @param string|null $path
     * @param array $ignoreFiles
     * @return array
     */
    function scan_folder($path, array $ignoreFiles = []): array
    {
        try {
            if (File::isDirectory($path)) {
                $data = array_diff(scandir($path), array_merge(['.', '..', '.DS_Store'], $ignoreFiles));
                natsort($data);
                return array_values($data);
            }
            return [];
        } catch (\Exception $exception) {
            return [];
        }
    }
}
