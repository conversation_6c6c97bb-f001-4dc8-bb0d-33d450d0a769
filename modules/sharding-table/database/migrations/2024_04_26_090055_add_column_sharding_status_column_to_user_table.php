<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user', function (Blueprint $table) {
            $table->tinyInteger('sharding_status')->default(UserShardingStatusEnum::DEFAULT);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
    }
};
