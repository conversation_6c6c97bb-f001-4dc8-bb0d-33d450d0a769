<?php

namespace Modules\ShardingTable\Traits;


use App\Enums\CacheKeys;
use App\Enums\UserRoleEnum;
use App\Models\User;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

trait InitSellerConnection
{
    protected function initConnection(): void
    {
        $sellerIds = cacheAlt()->tags([CacheKeys::SELLER_CONNECTION])->remember(
            CacheKeys::SELLER_CONNECTION,
            CacheKeys::CACHE_30D,
            fn() => User::query()
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->whereIn('sharding_status', [UserShardingStatusEnum::SYNCING, UserShardingStatusEnum::COMPLETED])
                ->pluck('id')
                ->toArray()
        );
        array_walk($sellerIds, function ($sellerId) {
            config()->set([
                "database.connections.mysql_$sellerId" => config("database.connections.mysql_seller"),
                "database.connections.mysql_$sellerId.database" => config("database.connections.mysql.database") . "_$sellerId",
            ]);
        });
    }
}
