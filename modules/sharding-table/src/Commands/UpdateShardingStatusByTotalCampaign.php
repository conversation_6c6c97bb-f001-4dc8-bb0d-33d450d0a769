<?php

namespace Modules\ShardingTable\Commands;

use App\Enums\ProductStatus;
use App\Enums\UserRoleEnum;
use App\Models\Campaign;
use App\Models\User;
use Illuminate\Console\Command;
use Modules\ShardingTable\Enums\UserShardingStatusEnum;

class UpdateShardingStatusByTotalCampaign extends Command
{
    const TOTAL_CAMPAIGN = 50000;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sharding-table:update-sharding-status-by-total-campaign';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update sharding status for seller who have more 50K campaign';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $userIds = [];
            User::query()
                ->select('id', 'sharding_status', 'db_connection')
                ->where('role', '!=', UserRoleEnum::CUSTOMER)
                ->where('db_connection', 'mysql_sub')
                ->where('sharding_status', UserShardingStatusEnum::DEFAULT)
                ->get()
                ->each(function (User $user) use (&$userIds) {
                    $countCampaign = Campaign::query()
                        ->select('id')
                        ->onSellerConnection($user)
                        ->where('seller_id', $user->id)
                        ->where('status', ProductStatus::ACTIVE)
                        ->count();
                    if ($countCampaign > self::TOTAL_CAMPAIGN) {
                        $userIds[] = $user->id;
                        $user->sharding_status = UserShardingStatusEnum::PROCESSING;
                        $user->save();
                    }
                });
            graylogInfo("Update sharding status for users: ". implode(',', $userIds), [
                'category' => 'sharding-table',
                'userIds' => $userIds,
            ]);
        }
        catch (\Exception $e) {
            logException($e, 'UpdateShardingStatusByTotalCampaign@handle');
        }

    }
}
