<?php
namespace Modules\ShardingTable\Enums;

use BenSampo\Enum\Enum;

final class UserShardingStatusEnum extends Enum
{
    const DEFAULT = 0;
    const PROCESSING = 1;
    const SYNCING = 2;
    const COMPLETED = 3;

    public static function hasPrivateConnection($value): bool
    {
        return in_array((int)$value, [self::SYNCING, self::COMPLETED], true);
    }

    public static function shardingCompleted($value): bool
    {
        return (int)$value === self::COMPLETED;
    }
}
