<?php

namespace Modules\CrispBot\Services;

use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Models\Customer;
use App\Models\Order;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\OpenAI;
use <PERSON>risp\CrispClient;
use Crisp\CrispException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Psr\Http\Client\ClientExceptionInterface;

class CrispBotService
{
    /**
     *
     * @var CrispClient
     */
    public CrispClient $crispClient;

    /**
     * @var mixed
     */
    private $websiteId;
    /**
     * @var mixed
     */
    private $identifier;
    /**
     * @var mixed
     */
    private $key;

    private $defaultUser = [
        "type" => "website",
        "nickname" => "Auto-reply",
        "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
    ];

    public function __construct()
    {
        $this->websiteId = config('crisp.bot.config.general.crisp_bot_website_id');
        $this->identifier = config('crisp.bot.config.general.crisp_bot_identifier');
        $this->key = config('crisp.bot.config.general.crisp_bot_key');

        $this->crispClient = new CrispClient;
        $this->crispClient->setTier("plugin");
        $this->crispClient->authenticate($this->identifier, $this->key);
    }

    public function sendMessage($sessionId, $content)
    {
        $message = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $content,
        ];

        return $this->sendToCrisp($sessionId, $message);
    }

    public function getConversationMeta(string $sessionId)
    {
        try {
            return $this->crispClient->websiteConversations->getOne($this->websiteId, $sessionId);
        } catch (\Throwable $e) {
            return [];
        }
    }

    /**
     * Get user info from user_id
     *
     * @param string $sessionId
     * @param string $orderNumber
     * @return Builder|Model|object|Order|null
     */
    public function getUserLastOrder(string $sessionId, string $orderNumber = '')
    {
        $sessionInfo = $this->getConversationMeta($sessionId);
        $userEmail = data_get($sessionInfo, 'meta.email');
        $userPhone = data_get($sessionInfo, 'meta.phone');
        $storeId = data_get($sessionInfo, 'meta.data.store_id');
        $storeDomain = data_get($sessionInfo, 'meta.data.domain');

        if (empty($userEmail) && empty($userPhone) && empty($orderNumber)) {
            return null;
        }

        return Order::query()
            ->select([
                'access_token',
                'store_domain',
                'store_name',
                'order_number',
                'paid_at'
            ])
            ->when($orderNumber, function ($query, $orderNumber) {
                return $query->where('order_number', 'like', "%$orderNumber%");
            })
            ->when(empty($orderNumber) && !empty($userEmail), function ($query) use ($userEmail) {
                return $query->where('customer_email', 'like', "%$userEmail%");
            })
            ->when(empty($orderNumber) && empty($userEmail) && !empty($userPhone), function ($query) use ($userPhone) {
                return $query->where('customer_phone', 'like', "%$userPhone%");
            })
            ->when($storeId, function ($query) use ($storeId) {
                return $query->where('store_id', $storeId);
            })
            ->when($storeDomain, function ($query) use ($storeDomain) {
                return $query->where('store_domain', $storeDomain);
            })
            ->with('tracking_statuses')
            ->orderByDesc('paid_at')
            ->first();
    }

    /**
     * @param string $sessionId
     * @return User|Customer|Builder|Model|object|null
     */
    public function findCustomer(string $sessionId)
    {
        $sessionInfo = $this->getConversationMeta($sessionId);

        $userEmail = $sessionInfo['meta']['email'];
        $userPhone = $sessionInfo['meta']['phone'];

        $user = null;

        if (!empty($userEmail)) {
            $user = Customer::query()
                ->with(['recent_store'])
                ->where('email', 'like', "%$userEmail%")
                ->first();
        }

        if (!empty($userPhone) && empty($user)) {
            $user = Customer::query()
                ->with(['recent_store'])
                ->where('phone', 'like', "%$userPhone%")
                ->first();
        }

        return $user;
    }

    /**
     * @param string $sessionId
     * @param string $timeStampBefore
     * @return array
     */
    public function getConversationMessages(string $sessionId, string $timeStampBefore = ''): array
    {
        try {
            return $this->crispClient->websiteConversations->getMessages($this->websiteId, $sessionId, $timeStampBefore);
        } catch (\Throwable $e) {
            return [];
        }
    }

    private function createLink(string $domain, string $path): string
    {
        return "https://{$domain}$path";
    }

    /**
     * @param $binary
     * @return mixed
     */
    private function convertBinaryToString($binary)
    {
        return json_decode(json_encode(mb_convert_encoding($binary, 'UTF-8', 'UTF-8')));
    }


    /**
     * @param string $sessionId
     * @param string $phoneReceived
     * @return array
     */
    public function autoReplyVoiceMail(string $sessionId, $phoneReceived = '')
    {
        $message = "RE: Voicemail @ +$phoneReceived:
        Hi! please send us your email or order number (SP-...) so we can assist you.
        You can email <NAME_EMAIL>. Thank you!";

        $msgMetaData = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $this->convertBinaryToString($message),
        ];

        return $this->sendToCrisp($sessionId, $msgMetaData);
    }

    /**
     * @param string $sessionId
     * @param string $phoneReceived
     * @return array|null
     */
    public function autoReplyVoiceMailWithOrderGuide(string $sessionId, string $phoneReceived = ''): ?array
    {
        $message = "RE: Voicemail @ +$phoneReceived:
        Here is the instructions to place an order: https://sen.lc/order-guide
        You can email <NAME_EMAIL>. Thank you!";

        $msgMetaData = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $this->convertBinaryToString($message),
        ];

        return $this->sendToCrisp($sessionId, $msgMetaData);
    }

    private function sendToCrisp(string $sessionId, array $metaData): ?array
    {
        try {
            return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $sessionId, $metaData);
        } catch (\Throwable $e) {
            return null;
        }
    }

    // Send payment reminder if order not paid

    /**
     * @param string $sessionId
     * @param string $name
     * @param string $domain
     * @param string $orderToken
     * @param string $storeName
     * @return array|null
     */
    public function sendPaymentReminder(string $sessionId, string $name, string $domain, string $orderToken, string $storeName): ?array
    {
        $checkoutUrl = $this->createLink($domain, '/checkout/' . $orderToken);

        $message = "Hi {$name},
        Thanks so much for reaching out!
        This auto-reply is just to let you know that we have received your email and will get back to you with a (human) response within 24-48 business hours.

        Did you forget to checkout your order? You can always go back to your cart using this link below:
        [$checkoutUrl]($checkoutUrl)
        If you don't know how to place an order with us, please refer to: [https://sen.lc/order-guide](https://sen.lc/order-guide)

        Please read the articles below if you need any additional information.
        [Return Policy]({$this->createLink($domain, '/page/return-policy')})
        [Shipping Policy]({$this->createLink($domain, "/page/shipping-policy")})
        [Terms & Conditions]({$this->createLink($domain, '/page/terms-of-service')})
        [Privacy Policy]({$this->createLink($domain, '/page/privacy')})
        [DMCA]({$this->createLink($domain, '/page/dmca')})

        If you have any additional information that you think will help us to assist you, please feel free to reply to this email. We look forward to chatting soon, our response within 24-48 business hours.

        Regards,

        The $storeName Team";

        $msgMetaData = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $this->convertBinaryToString($message),
        ];

        return $this->sendToCrisp($sessionId, $msgMetaData);
    }

    /**
     * @param string $sessionId
     * @param string $subject
     * @param string $message
     * @param string|null $customerName
     * @param string|null $storeDomain
     * @param string $orderStatusUrl
     * @param string $storeName
     * @param string $countryCode
     * @param array $allTrackingUrls
     * @return void
     * @throws ClientExceptionInterface
     */
    public function sendAutoResponse(string $sessionId, string $subject, string $message, ?string $customerName, ?string $storeDomain, string $orderStatusUrl, string $storeName, string $countryCode = '', array $allTrackingUrls = []): void
    {
        // Verify domain
        try {
            $conversationMeta = $this->crispClient
                ->websiteConversations
                ->getMeta($this->websiteId, $sessionId);

            $metaDomain = data_get($conversationMeta, 'data.domain', []);

            if (!empty($metaDomain) && $metaDomain !== $storeDomain) {
                $message = mentionDiscord(DiscordUserIdEnum::TUANHM);
                $message .= "\nCrisp Auto Reply Domain Mismatch:";

                $embedDesc = [
                    [
                        'description' => "Session ID: {$sessionId}\nMeta Domain: {$metaDomain}\nProvided Domain: {$storeDomain}",
                        'color' => 15548997
                    ]
                ];

                logToDiscord($message, DiscordChannel::ERROR, embeds: $embedDesc);
            }
        } catch (\Exception $e) {
            logException($e);
        }

        $SenPrintsTrackOrderUrl = $this->createLink($storeDomain, '/order/track');
        $delayShippingCountries = ['NZ', 'AU', 'AT', 'CA'];
        $subject = strtolower($subject);
        $template = SystemConfig::query()
            ->where('key', 'crisp_auto_reply_template')
            ->value('json_data');

        if (empty($template)) {
            logToDiscord('Crisp auto reply template not found');
            return;
        }

        $json = json_decode($template, true);
        $rules = [
            '{StoreName}' => $storeName,
            '{SenPrintsTrackOrderUrl}' => "[$SenPrintsTrackOrderUrl]($SenPrintsTrackOrderUrl)",
            '{OrderStatusUrl}' => "[$orderStatusUrl]($orderStatusUrl)",
            '{CustomerName}' => $customerName,
            '{DelayedMessage}' => '',
            '{AllTrackingUrls}' => '',
            '{ReturnPolicy}' => "[Return Policy]({$this->createLink($storeDomain, '/page/return-policy')})",
            '{ShippingPolicy}' => "[Shipping Policy]({$this->createLink($storeDomain, "/page/shipping-policy")})",
            '{PrivacyPolicy}' => "[Privacy Policy]({$this->createLink($storeDomain, '/page/privacy')})",
            '{Terms&Conditions}' => "[Terms & Conditions]({$this->createLink($storeDomain, '/page/terms-of-service')})",
            '{DMCA}' => "[DMCA]({$this->createLink($storeDomain, '/page/dmca')})",
        ];

        $segments = $this->getSegments($sessionId);
        $detectCategory = '';
        $isFromEmail = in_array('email', $segments);

        // check if ticket is from email
        if ($isFromEmail) {
            $detectCategory = strtolower($this->categorizeEmail($subject, $message));

//            if ($detectCategory === 'spam') {
//                // See value: https://docs.crisp.chat/references/rest-api/v1/#change-conversation-state
//                $this->updateConversationState($sessionId, 'resolved');
//                return;
//            }
        }

        if (self::isCategory($subject, $detectCategory, 'where is my order?')) {
            if (empty($orderStatusUrl)) {
                $message = $json['where_is_my_order'];
            } else {
                $message = $json['where_is_my_order_with_order_status_url'];
            }
        } elseif (self::isCategory($subject, $detectCategory, 'return my order')) {
            $message = $json['return_my_order'];
        } elseif (self::isCategory($subject, $detectCategory, 'item not as describe')) {
            $message = $json['item_not_as_describe'];
        } else {
            if (empty($orderStatusUrl)) {
                $orderStatusUrl = $SenPrintsTrackOrderUrl;
            }

            $rules['{OrderStatusUrl}'] = "[$orderStatusUrl]($orderStatusUrl)";
            $message = $json['default'];
        }

        if (!empty($allTrackingUrls)) {
            $replaceString = "\r\nAnd tracking links:";
            foreach ($allTrackingUrls as $trackingUrl) {
                $replaceString .= "\n- [$trackingUrl]($trackingUrl)";
            }
            $rules['{AllTrackingUrls}'] = $replaceString . "\r\n";
        }

        if (in_array($countryCode, $delayShippingCountries)) {
            $rules['{DelayedMessage}'] = $json['delay_message'];
        }

        if ($isFromEmail && $detectCategory !== '') {
            $segments[] = 'email_auto_reply';
            $this->updateSegment($sessionId, $segments);
        }

        $searches = array_keys($rules);
        $replaces = array_values($rules);
        $content = str_replace($searches, $replaces, $message);

        $msgMetaData = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $this->convertBinaryToString($content),
        ];

        $this->sendToCrisp($sessionId, $msgMetaData);
    }

    // Auto response when order found

    private function categorizeEmail(string $subject, string $message): string
    {
        $system = <<<TXT
        We are an eCommerce store focus on print-on-demand products.
We assign support requests based on one of the below categories:

- Where is my order?
- Change my order
- Return my order
- Payment/Discount issue
- Item not as described
- Legal / DMCA notice
- Spam
- Other

I will send you contact request from a customer. Please return the best category for the request in JSON format: {"category":"category","summary":"short summary under 20 words"}
TXT;

        $prompt = [
            [
                'role' => 'system',
                'content' => $system,
            ],
            [
                'role' => 'user',
                'content' => 'Subject: ' . $subject . "\n" . 'Message: ' . $message
            ]
        ];

        $aiResponse = OpenAI::completions($prompt, null, true);

        if ($aiResponse && $aiResponse !== 'No response.') {
            $json = json_decode($aiResponse, true);
            return data_get($json, 'category', 'Unknown');
        }

        return 'Unknown';
    }

    private static function isCategory(string $subject, string $detectedCategory, string $category): bool
    {
        if (str_starts_with($subject, $category)) {
            return true;
        }

        if ($detectedCategory !== '' && $detectedCategory === $category) {
            return true;
        }

        return false;
    }

    private function getSegments(string $sessionId): array
    {
        try {
            $result = $this->crispClient
                ->websiteConversations
                ->getMeta($this->websiteId, $sessionId);

            return data_get($result, 'segments', []);
        } catch (CrispException $e) {
            logException($e);
            return [];
        }
    }

    private function updateSegment(string $sessionId, array $segment): void
    {
        try {
            $this->crispClient
                ->websiteConversations
                ->updateMeta($this->websiteId, $sessionId, [
                    'segments' => $segment
                ]);
        } catch (CrispException $e) {
            // logException($e);
        }
    }

    /**
     * @param string $sessionId
     * @param string $name
     * @return array|null
     */
    public function sendAutoResponseWithoutInfo(string $sessionId, string $name): ?array
    {
        $message = "
        Hi {$name},

        Thanks so much for reaching out!
        This auto-reply is just to let you know that we have received your email and will get back to you with a (human) response within 24-48 business hours.";

        $msgMetaData = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => $this->defaultUser,
            "content" => $this->convertBinaryToString($message),
        ];

        return $this->sendToCrisp($sessionId, $msgMetaData);
    }

    private function updateConversationState(string $sessionId, string $state): void
    {
        try {
            $this->crispClient
                ->websiteConversations
                ->setState($this->websiteId, $sessionId, $state);
        } catch (CrispException $e) {
            logException($e);
        }
    }
}
