<?php

namespace Modules\EmailMarketingKlaviyo\Supports;

final class JsonHelper
{
    /**
     * Determines whether the input is a valid JSON string.
     *
     * @param string $string The input string.
     *
     * @return bool
     */
    public static function isJson($string)
    {
        return (is_string($string) && is_array(json_decode($string, true)) && (json_last_error() === JSON_ERROR_NONE));
    }

    /**
     * Encodes the given data into JSON.
     *
     * @param mixed $data The data to encode
     * @param int $options Bitmask consisting of JSON_* constants
     * @param int $maxDepth The maximum depth allowed for serializing $data
     *
     * @throws \RuntimeException If the encoding failed
     */
    public static function encode($data, int $options = 0, int $maxDepth = 512): string
    {
        if ($maxDepth < 1) {
            throw new \InvalidArgumentException('The $maxDepth argument must be an integer greater than 0.');
        }
        $options |= \JSON_UNESCAPED_UNICODE | \JSON_INVALID_UTF8_SUBSTITUTE;
        $encodedData = json_encode($data, $options, $maxDepth);
        if (\JSON_ERROR_NONE !== json_last_error()) {
            throw new \RuntimeException(sprintf('Could not encode value into JSON format. Error was: "%s".', json_last_error_msg()));
        }
        return $encodedData;
    }

    /**
     * Decodes the given data from JSON.
     *
     * @param $json
     * @param bool $assoc
     * @param int $depth
     * @param int $options
     * @return mixed
     *
     */
    public static function decode($json, $assoc = false, $depth = 512, $options = 0)
    {
        if (is_array($json)) {
            return $json;
        }
        $result = json_decode($json, $assoc, $depth, $options);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \InvalidArgumentException('JsonException : ' . json_last_error_msg());
        }
        return $result;
    }
}
