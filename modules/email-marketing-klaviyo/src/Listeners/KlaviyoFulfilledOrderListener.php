<?php

namespace Modules\EmailMarketingKlaviyo\Listeners;

use App\Events\OrderFulfilled;
use App\Models\Order;
use App\Models\Store;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use Modules\OrderService\Models\RegionOrders;
use App\Services\OrderService;

class KlaviyoFulfilledOrderListener implements ShouldQueue
{
    use InteractsWithQueue;

    public Order|RegionOrders $order;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = KlaviyoQueueName::TrackEvents;

    public function handle(OrderFulfilled $event): void
    {
        $this->order = $event->order;
        $this->order->refresh();
        $this->order->customer_phone = phone($this->order->customer_phone)->isValid() ? phone($this->order->customer_phone)->formatE164() : null;
        $store = Store::whereId($this->order->store_id)->first();
        if (empty($store)) {
            return;
        }
        $klaviPrivateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        if (empty($klaviPrivateKey)) {
            return;
        }
        $domain = $store->domain;
        if (empty($domain)) {
            $domain = $store->sub_domain . "." . getStoreBaseDomain();
        }
        if (!KlaviyoService::isOrderTypeAllowed($this->order)) {
            graylogInfo("Order type is not regular", [
                "order_id" => $this->order->id,
                "order_type" => $this->order->type,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        if (!OrderService::isFulfilled($this->order)) {
            graylogInfo("Order is not fulfilled", [
                "order_id" => $this->order->id,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $result = StoreService::getOrderItems($this->order);
        if ($result === null) {
            graylogInfo("Order has no products", [
                "order_id" => $this->order->id,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $categories = $result->categories;
        $itemNames = $result->item_names;
        /**
            $value - The total value of the fulfilled order.
            Items - The names of the products in someone's order, e.g., t-shirt or pants.
            Collections - The complete set of the collections of the products in someone's order, e.g., t-shirts, men's, pants, and sale.
            Item Count - The count of line items in the order, e.g., 2. Note that this does not account for the quantity of items. Additionally, it is possible to add the same item to a cart multiple times and have it counted as multiple line items in Shopify; Klaviyo does not check for uniqueness or merge these for Item Count.
            Discount Codes - Any discount or coupon codes someone used towards the order, e.g., SPRING2015.
            Total Discounts - The total amount of any coupons or discounts if someone used a code, e.g., 10.00.
            Customer Locale - The customer’s locale when placing the order, e.g., en-US.
            $extra - Additional data including localized product information.
            OptedInToSmsOrderUpdates - This value (True or False) indicates that the shopper has provided their phone number with their shipping information during checkout.
         */
        $properties = [
            '$value' => $this->order->total_amount,
            'Items' => $itemNames,
            'Collections' => $categories,
            'Item Count' => count($itemNames),
            'Discount Codes' => $this->order->discount_code ?? '',
            'Total Discounts' => $this->order->total_discount ?? 0,
            'Customer Locale' => $this->order->country ?? '',
            'OptedInToSmsOrderUpdates' => optional($this->order->customer)->sms_subscribed === 1 ? 'True' : 'False',
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Fulfilled Order"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => $this->order->customer_email,
                                "phone_number" => $this->order->customer_phone,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $store->name ?? "",
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($klaviPrivateKey, $payload));
    }
}
