<?php

namespace Modules\EmailMarketingKlaviyo\Listeners;

use App\Enums\TrackingStatusEnum;
use App\Events\KlaviyoTrackingUpdated;
use App\Models\Order;
use App\Models\Store;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendTrackEventJob;
use Modules\EmailMarketingKlaviyo\Services\KlaviyoService;
use Modules\EmailMarketingKlaviyo\Services\StoreService;
use Modules\OrderService\Models\RegionOrders;

class KlaviyoDeliveredShipmentListener implements ShouldQueue
{
    use InteractsWithQueue;

    public Order|RegionOrders $order;
    public $trackingCode;
    public $trackingStatus;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = KlaviyoQueueName::TrackEvents;

    public function handle(KlaviyoTrackingUpdated $event): void
    {
        $this->order = $event->order;
        $this->order->refresh();
        $this->order->customer_phone = phone($this->order->customer_phone)->isValid() ? phone($this->order->customer_phone)->formatE164() : null;
        $this->trackingCode = $event->trackingCode;
        $this->trackingStatus = $event->trackingStatus;
        if ($this->trackingStatus !== TrackingStatusEnum::DELIVERED) {
            return;
        }
        $store = Store::whereId($this->order->store_id)->first();
        if (empty($store)) {
            return;
        }
        $klaviPrivateKey = $store->klaviyo_private_key ?? $store->seller->klaviyo_private_key;
        if (empty($klaviPrivateKey)) {
            return;
        }
        $domain = $store->domain;
        if (empty($domain)) {
            $domain = $store->sub_domain . "." . getStoreBaseDomain();
        }
        if (!KlaviyoService::isOrderTypeAllowed($this->order)) {
            graylogInfo("Order type is not regular", [
                "order_id" => $this->order->id,
                "order_type" => $this->order->type,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $result = StoreService::getOrderItems($this->order);
        if ($result === null) {
            graylogInfo("Order has no products", [
                "order_id" => $this->order->id,
                "category" => "email-marketing-klaviyo",
            ]);
            return;
        }
        $categories = $result->categories;
        $itemNames = $result->item_names;
        $totalAmount = $this->order->total_amount;
        /**
            $value | The sum of product prices, including discounts, for item(s) in the shipment.
            Collections | The product collections for product(s) in the shipment.
            Customer Locale | The customer’s locale when placing the order, e.g., en-US.
            Estimated Delivery | The estimated delivery date for the shipment.
            Fulfillment Service | The fulfillment service associated with the shipment, e.g., manual.
            Items | An array of the product(s) in the shipment.
            Item Count | The total number of items in the shipment.
            Transit Hours | The number of hours the shipment has been in transit.
            Shipment Status | The status of the shipment, which is delivered.
            Source Name | The source of the order, e.g., web.
            Tracking Company | The name of the tracking company.
            Tracking Numbers | The tracking number(s) of the shipment.
            OptedInToSmsOrderUpdates | This value (True or False) indicates that the shopper has provided their phone number with their shipping information during checkout.
         */
        $properties = [
            '$value' => $totalAmount,
            'Collections' => $categories,
            'Customer Locale' => $this->order->country,
            'Estimated Delivery' => $this->order->estimate_delivery_date,
            'Fulfillment Service' => env('APP_NAME', 'SenPrints'),
            'Items' => $itemNames,
            'Item Count' => count($itemNames),
            'Transit Hours' => 0,
            'Shipment Status' => 'delivered',
            'Source Name' => 'web',
            'Tracking Company' => '17Track',
            'Tracking Numbers' => $this->trackingCode,
            'OptedInToSmsOrderUpdates' => optional($this->order->customer)->sms_subscribed === 1 ? 'True' : 'False',
        ];
        $payload = [
            "data" => [
                "type" => "event",
                "attributes" => [
                    "properties" => $properties,
                    "metric" => [
                        "data" => [
                            "type" => "metric",
                            "attributes" => [
                                "name" => "Delivered Shipment"
                            ]
                        ]
                    ],
                    "profile" => [
                        "data" => [
                            "type" => "profile",
                            "attributes" => [
                                "email" => $this->order->customer_email,
                                "phone_number" => $this->order->customer_phone,
                                "properties" => [
                                    "store_domain" => $domain,
                                    "store_name" => $store->name ?? "",
                                ]
                            ]
                        ]
                    ]
                ]
            ]
        ];
        dispatch(new KlaviyoSendTrackEventJob($klaviPrivateKey, $payload));
    }
}
