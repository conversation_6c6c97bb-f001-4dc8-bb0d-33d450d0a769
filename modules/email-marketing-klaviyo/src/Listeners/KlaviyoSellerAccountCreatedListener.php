<?php

namespace Modules\EmailMarketingKlaviyo\Listeners;

use App\Events\AccountCreated;
use App\Models\User;
use Modules\EmailMarketingKlaviyo\Data\ProfileData;
use Modules\EmailMarketingKlaviyo\Enums\KlaviyoQueueName;
use Modules\EmailMarketingKlaviyo\Jobs\KlaviyoSendProfileJob;
use TheIconic\NameParser\Parser;

class KlaviyoSellerAccountCreatedListener
{
    public User $user;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string|null
     */
    public $queue = KlaviyoQueueName::ImportProfiles;

    public function handle(AccountCreated $event)
    {
        $this->user = $event->user;
        $parser = new Parser();
        $firstName = $parser->parse($this->user->name)->getFirstName();
        $lastName = $parser->parse($this->user->name)->getLastName();
        $profileData = ProfileData::from([
            'email' => $this->user->email,
            'first_name' => $firstName,
            'last_name' => $lastName,
        ]);
        dispatch(new KlaviyoSendProfileJob($profileData));
    }
}
