<?php

namespace App\Helpers;

use App\Enums\QueueName;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Enums\ProductType;
use App\Jobs\LogProductDeleteRecord;
class CustomQueryBuilder extends Builder
{
    public function delete()
    {
        $selectQuery = $this->select([
            'id',
            'product_type',
            'supplier_id',
            'name',
            'slug',
            'status',
            'sku'
        ]);
        LogProductDeleteRecord::dispatch($selectQuery->toRawSql(), Auth::user())->onQueue(QueueName::LOW_PRIORITY);
        return parent::delete();
    }
}
