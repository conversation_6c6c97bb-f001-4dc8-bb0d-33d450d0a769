<?php

namespace App\Exceptions;

use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Request;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\URL;
use PhpAmqpLib\Exception\AMQPChannelClosedException;
use PhpAmqpLib\Exception\AMQPConnectionClosedException;
use PhpAmqpLib\Exception\AMQPIOException;
use PhpAmqpLib\Exception\AMQPRuntimeException;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;

class Handler extends ExceptionHandler {

    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
            //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register() {
        $this->reportable(function (\Throwable $e) {
            if ($e instanceof AMQPIOException || $e instanceof AMQPChannelClosedException || $e instanceof AMQPConnectionClosedException) {
                $message = $e->getMessage();
                $message .= "\n URL: " . URL::full();
                $message .= "\n HOST: " . gethostname();
                if (isset($_ENV['SERVER_INFO'])) {
                    $message .= "\n SERVER: " . $_ENV['SERVER_INFO'];
                }
                throw new AMQPRuntimeException(
                    'Lost connection: ' . $message,
                    $e->getCode(),
                    $e
                );
            }
            if ($this->shouldReport($e) && app()->bound('sentry')) {
                app('sentry')->captureException($e);
            }
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param Request $request
     * @param \Throwable $e
     * @return Response
     *
     */
    public function render($request, \Throwable $e)
    {
        $errors = null;
        if(config('app.debug')) {
            $errors = [
                'file' => $e->getFile() ?? '',
                'line' => $e->getLine() ?? 0,
                'trace' => $e->getTrace() ?? [],
            ];
        }

        if ($e instanceof ValidationException) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null,
                'errors' => $errors ?? $e->errors(),
            ], 400);
        }

        if ($e instanceof AuthenticationException) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated',
                'data' => null,
                'errors' => $errors,
            ], 401);
        }

        if ($e instanceof AuthorizationException) {
            return response()->json([
                'success' => false,
                'message' => 'Forbidden',
                'data' => null,
                'errors' => $errors,
            ], 403);
        }

        if ($e instanceof NotFoundHttpException || $e instanceof ModelNotFoundException) {
            return response()->json([
                'success' => false,
                'message' => 'Resource not found',
                'data' => null,
                'errors' => $errors
            ], 404);
        }

        if ($e instanceof MethodNotAllowedHttpException) {
            return response()->json([
                'success' => false,
                'message' => 'Method not allowed',
                'data' => null,
                'errors' => $errors
            ], 405);
        }

        if ($e instanceof TokenMismatchException) {
            return response()->json([
                'success' => false,
                'message' => 'Token mismatch',
                'data' => null,
                'errors' => $errors,
            ], 419);
        }

        if ($e instanceof TooManyRequestsHttpException) {
            return response()->json([
                'success' => false,
                'message' => 'Too many requests, please try again later',
                'data' => null,
                'errors' => $errors
            ], 429);
        }

        if ($e instanceof HttpException) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage() ?: 'Something went wrong',
                'data' => null,
                'errors' => $errors,
            ], $e->getStatusCode());
        }

        return response()->json([
            'success' => false,
            'message' => $e->getMessage() ?: 'Something went wrong',
            'data' => null,
            'errors' => $errors
        ], 500);
    }
}
