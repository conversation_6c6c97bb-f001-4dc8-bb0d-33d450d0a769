<?php

namespace App\Services;

use App\Contracts\TrackingServiceContract;
use App\Data\TrackingItemData;
use App\Data\TrackingItemRejectedData;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\TrackingStatus;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class TrackingService
{
    protected ?string $service;
    public function __construct(?string $service = null)
    {
        $this->service = $service;
    }

    /**
     * @param $service
     * @return self
     */
    public function setService($service): self
    {
        $this->service = $service;
        return $this;
    }

    /**
     * @return string
     */
    public function getService(): string
    {
        return $this->service;
    }

    /**
     * @return TrackingServiceContract
     */
    public function trackService(): TrackingServiceContract
    {
        return match ($this->service) {
            TrackingServiceEnum::TRACK_123 => app(TrackOneTwoThree::class),
            TrackingServiceEnum::TRAKOW => app(Trakow::class),
            default => app(SeventeenTrack::class),
        };
    }

    /**
     * @param $trackingCodes
     * @return bool
     */
    public function registerOrderTracking($trackingCodes): bool
    {
        try {
            $trackingServices = collect(config('services.tracking'))->filter(fn($item) => $item['weight'] > 0)->toArray();
            $totalWeight = array_sum(array_column(array_values($trackingServices), 'weight'));
            if (empty($totalWeight)) {
                return false;
            }
            $rand = random_int(1, $totalWeight);
            $pickedService = $this->getService();
            $current = 0;
            foreach ($trackingServices as $service => $setting) {
                $current += $setting['weight'];
                if ($rand <= $current) {
                    $pickedService = $service;
                    break;
                }
            }
            $this->setService($pickedService);
            [$insertCodes, $updateCodes] = $this->trackService()->registerOrderTracking($trackingCodes);
            if (!empty($insertCodes)) {
                foreach ($insertCodes as $insertCode) {
                    TrackingStatus::query()->updateOrCreate($insertCode[0], $insertCode[1]);
                }
            }
            if (!empty($updateCodes)) {
                foreach ($updateCodes as $updateCode) {
                    OrderProduct::query()->where('tracking_code', 'like', $updateCode[0]['tracking_code'] . '%')->update($updateCode[1]);
                }
            }
            graylogInfo("End register tracking code on the {$this->service}.", [
                'category' => 'tracking_status_logs',
                'updateCodes' => !empty($updateCodes) ? json_encode($updateCodes, JSON_THROW_ON_ERROR) : '',
                'insertCodes' => !empty($insertCodes) ? json_encode($insertCodes, JSON_THROW_ON_ERROR) : '',
            ]);
            return true;
        } catch (Throwable $e) {
            logToDiscord("[{$this->service}] Register tracking number failed - Message: " . $e->getMessage(), 'tracking_status_logs', true);
            graylogError('Error in register tracking_code.', [
                'category' => 'error_register_tracking_code',
                'error' => $e,
                'param_input' => $trackingCodes,
            ]);
            return false;
        }
    }

    /**
     * @param $trackingNumbers
     * @return array
     */
    public function getTrackInfo($trackingNumbers): array
    {
        $response = [
            'accepted' => [],
            'rejected' => []
        ];
        $result = $this->trackService()->getTrackInfo($trackingNumbers);
        if (!empty($result['accepted'])) {
            $response['accepted'] = TrackingItemData::withMapping($result['accepted'])->toArray();
        }
        if (!empty($result['rejected'])) {
            $response['rejected'] = TrackingItemRejectedData::withMapping($result['rejected'])->toArray();
        }
        return $response;
    }

    /**
     * @param $status
     * @return string|null
     */
    public function convertTrackingStatus($status): ?string
    {
        return $this->trackService()->convertTrackingStatus($status);
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public function isSkipCheck($code, string $message = ''): bool
    {
        return $this->trackService()->isSkipCheck($code, $message);
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public function isNeedRegister($code, string $message = ''): bool
    {
        return $this->trackService()->isNeedRegister($code, $message);
    }

    /**
     * @param $request
     * @return bool
     */
    public function verifySignature($request): bool
    {
        return $this->trackService()->verifySignature($request);
    }

    /**
     * @return Collection
     */
    public function carriers(): Collection
    {
        return $this->trackService()->carriers();
    }

    /**
     * @param $trackingNumber
     * @param null $carrier
     * @param null $supplier
     * @param null $trackingUrl
     * @return false|null|string
     */
    public function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null): false|null|string
    {
        return $this->trackService()->detectCarrier($trackingNumber, $carrier, $supplier, $trackingUrl);
    }

    /**
     * @param $trackingCodes
     * @param $tracking_code
     * @return object|null
     */
    public static function getObjectTrackingCode($trackingCodes, $tracking_code)
    {
        $code = null;
        try {
            foreach ($trackingCodes as $trackingCode) {
                if ((string)data_get($trackingCode, 'tracking_code', '') === (string)$tracking_code) {
                    $code = $trackingCode;
                    break;
                }
            }
        } catch (Throwable $e) {
            logToDiscord('Get object tracking code error ' . $e . 'input tracking codes : ' . $trackingCodes, 'tracking_status_logs', true);
            return null;
        }
        return $code ? (object)$code : null;
    }

    /**
     * @param $trackingStatus
     * @param $trackingCode
     * @param $shippingCarrier
     * @param $webhookStatus
     * @param $deliveredTime
     * @param $source
     * @return bool
     */
    public static function handleUpdateTrackingWebhook($trackingStatus, $trackingCode, $shippingCarrier, $webhookStatus, $deliveredTime, $source): bool
    {
        $updates = [
            'status' => $webhookStatus,
            'tracking_service' => $source
        ];

        if (empty($trackingStatus->shipping_carrier) && !empty($shippingCarrier)) {
            $updates['shipping_carrier'] = $shippingCarrier;
        }
        DB::beginTransaction();
        try {
            TrackingStatus::query()->where('tracking_code', $trackingCode)->update($updates);
            OrderProduct::query()->where('tracking_code', 'like', $trackingCode . '%')->update(['tracking_status' => $webhookStatus, 'tracking_service' => $source]);
            if (in_array($webhookStatus, TrackingStatusEnum::inTransitStatuses(), true)) {
                $orderProducts = OrderProduct::query()
                    ->with('order')
                    ->where([
                        'order_id' => $trackingStatus->order_id,
                        'fulfill_status' => OrderProductFulfillStatus::PROCESSING,
                    ])
                    ->where('tracking_code', 'like', $trackingStatus->tracking_code . '%')
                    ->whereIn('tracking_status', [
                        TrackingStatusEnum::TRANSIT,
                        TrackingStatusEnum::OUT_FOR_DELIVERY,
                        TrackingStatusEnum::INFO_RECEIVED,
                        TrackingStatusEnum::NEW,
                        TrackingStatusEnum::NOTFOUND
                    ])
                    ->whereNotNull('tracking_code')
                    ->get();
                if ($orderProducts->isNotEmpty()) {
                    $orderProducts->each(function (OrderProduct $orderProduct) {
                        $orderProduct->delivered_at = now();
                        $orderProduct->processing_day = diffProcessingDay($orderProduct->fulfilled_at);
                        $orderProduct->fulfill_status = OrderProductFulfillStatus::ON_DELIVERY;
                        $orderProduct->save();
                        $countProductPreShipment = OrderProduct::query()
                            ->where('order_id', $orderProduct->order_id)
                            ->whereIn('tracking_status', [
                                TrackingStatusEnum::INFO_RECEIVED,
                                TrackingStatusEnum::NEW,
                                TrackingStatusEnum::NOTFOUND
                            ])
                            ->whereNotNull('tracking_code')
                            ->count();
                        $countUnfulfilled = OrderProduct::query()
                            ->where('order_id', $orderProduct->order_id)
                            ->whereNotIn('fulfill_status', [
                                OrderProductFulfillStatus::FULFILLED,
                                OrderProductFulfillStatus::CANCELLED,
                                OrderProductFulfillStatus::ON_DELIVERY,
                            ])
                            ->count();
                        if ($countUnfulfilled === 0) {
                            $data = [];
                            if (in_array($orderProduct->order->status, [OrderStatus::PENDING, OrderStatus::PROCESSING], true)) {
                                $data['status'] = OrderStatus::COMPLETED;
                            }
                            $data['fulfill_status'] = OrderFulfillStatus::ON_DELIVERY;
                            if ($countProductPreShipment > 0) {
                                $data['fulfill_status'] = OrderFulfillStatus::PROCESSING;
                            }
                            Order::query()->whereKey($orderProduct->order_id)->update($data);
                        }
                    });
                }
            }
            if ($webhookStatus === TrackingStatusEnum::DELIVERED) {
                $received_at = now();
                if (!empty($deliveredTime)) {
                    $received_at = Carbon::parse($deliveredTime);
                }
                Order::query()->where('id', $trackingStatus->order_id)->update(['received_at' => $received_at]);
                OrderProduct::query()->where('tracking_code', 'like', $trackingCode . '%')->update([
                    'received_at' => $received_at,
                    'fulfill_status' => OrderProductFulfillStatus::FULFILLED,
                    'shipping_day' => DB::raw('timestampdiff(second, fulfilled_at, now()) / (24 * 60 * 60)'),
                ]);

                $productsOfDeliveredOrder = OrderProduct::query()->where('order_id', $trackingStatus->order_id)->get();

                if ($productsOfDeliveredOrder->isNotEmpty()) {
                    $fulfillStatuses = $productsOfDeliveredOrder->pluck('fulfill_status')->unique();

                    if ($fulfillStatuses->count() === 1 && $fulfillStatuses->first() === OrderProductFulfillStatus::FULFILLED) {
                        Order::query()->where('id', $trackingStatus->order_id)->update(['fulfill_status' => OrderFulfillStatus::FULFILLED, 'status' => OrderStatus::COMPLETED]);
                    }
                }

                $order = Order::query()
                    ->with('order_products', function ($query) use ($trackingCode) {
                        $query->where('tracking_code', 'like', $trackingCode . '%');
                    })
                    ->whereHas('seller', function ($query) {
                        $query->where('smart_remarketing', true);
                    })
                    ->whereNotNull(['customer_name', 'customer_phone'])
                    ->find($trackingStatus->order_id);

                if ($order) {
                    OrderService::sendUpsellSms($order);
                }
            }
            DB::commit();
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            logToDiscord('Webhook update tracking info failed - Number: ' . $trackingCode . ' - Message: ' . $e->getMessage(), 'tracking_status_logs');
            return false;
        }
    }
}
