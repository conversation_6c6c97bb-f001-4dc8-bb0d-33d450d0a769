<?php

namespace App\Services;

use App\Library\SPHash;
use Illuminate\Support\Str;

class SenPrintsApi
{
    public static function generateKey(array $customData = [], string $secret = ''): ?string
    {
        if (empty($secret)) {
            $secret = config('senprints.external.jwt.secret');
        }

        $hash = new SPHash($secret);
        $data = ['deviceId' => Str::uuid()->toString()];

        // Merge current data & custom data
        if (!empty($customData)) {
            $data = array_merge($data, $customData);
        }
        // Convert array to collection
        $data = collect($data);

        $encryptDataArray = [];
        $data->map(function ($value, $name) use (&$encryptDataArray) {
            $encryptDataArray[] = "{$name}:{$value}";
        });

        $encryptData = implode('::', $encryptDataArray);
        $encryptData = $hash->encrypt($encryptData);
        return !is_null($encryptData) ? base64_encode($encryptData) : null;
    }

    public static function parseKey($encryptStr, string $secret = ''): ?array
    {
        if (empty($secret)) {
            $secret = config('senprints.external.jwt.secret');
        }
        $encryptStr = base64_decode($encryptStr);
        $hash = new SPHash($secret);
        $decryptData = $hash->decrypt($encryptStr);
        if (is_null($decryptData)) {
            return null;
        }
        // I'll be back, and use it
//        $countNotChar = preg_match('/[[:^print:]]/', $decryptData, $matchNotChar);
//        if ($countNotChar > 0) {
//            return null;
//        }
        if (Str::contains($decryptData, '::')) {
            $decryptDataArray = explode('::', $decryptData);
            // Convert data array to collection
            $decryptDataCollection = collect($decryptDataArray);
            unset($decryptDataArray);
            $decryptDataArray = [];
            $decryptDataCollection->map(function ($item) use (&$decryptDataArray) {
                if (Str::contains($item, ':')) {
                    [$name, $value] = explode(':', $item);
                    if (!empty($name) && !empty($value)) {
                        $decryptDataArray[$name] = (int)$value ?: (string)$value;
                    }
                }
            });
            unset($decryptDataCollection);
            return $decryptDataArray;
        }

        if (Str::contains($decryptData, ':')) {
            [$name, $value] = explode(':', $decryptData);
            if (!empty($name) && !empty($value)) {
                return [$name => (int)$value ?: (string)$value];
            }
        }
        return null;
    }
}
