<?php

namespace App\Services;

use InvalidArgumentException;

class ImageService
{
    const DEFAULT_CONFIG = [
        'sample_rate' => 3,
        'max_colors' => 15,
        'ignore_background' => true,
        'dark_threshold' => 50,
        'white_threshold' => 250,
        'color_tolerance' => 5,        // Fine grouping
        'pre_group_tolerance' => 25,   // Aggressive pre-grouping
        'min_percentage' => 85,
        'max_percentage' => 95,
        'group_similar_colors' => true,
        'white_grouping_tolerance' => 30, // Special for white variants
        'enable_pre_grouping' => true,
        'similarity_threshold' => 15 // Threshold for similarity verification
    ];

    /**
     * @param string $imageUrl
     * @param string $productHexColor
     * @return bool
     */
    public function validateColorSameWithDesign(string $imageUrl, string $productHexColor, int $orderId = null): bool
    {
        $start = microtime(true);
        $minPercentage = 90;
        $config = [
            'max_colors' => 30,
            'color_tolerance' => 100,
            'min_percentage' => $minPercentage,
            'sample_rate' => 10,
            'dark_threshold' => 20
        ];

        $similarityThreshold = data_get(self::DEFAULT_CONFIG, 'similarity_threshold', 10);

        $result = $this->analyzeImageDetailsColors($imageUrl, $config);
        $end = microtime(true);
        $colors = data_get($result, 'colors');

        $color2 = [
            'hex' => $productHexColor,
            'light' => data_get($this->hexToHsl($productHexColor), 'l')
        ];

        $colorVerified = [];
        $verifyConfirmed = true;
        foreach ($colors as $color) {
            $percentage = data_get($color, 'percentage', 0);
            if ($percentage < $minPercentage) {
                continue;
            }
            $hexColor = data_get($color, 'hex');
            $color1 = [
                'hex' => $hexColor,
                'light' => data_get($color, 'hsl.l')
            ];
            $verifySameOfColor = $this->verify($color1, $color2, $similarityThreshold);
            $colorVerified[] = [
                'color' => $color1['hex'],
                'verified' => $verifySameOfColor['verify'],
                'similarity_percent' => $verifySameOfColor['similarity_percent'] . '%',
                'similarity_weight' => $verifySameOfColor['similarity_weight'] . '%',
                'similarity_hsl' => $verifySameOfColor['similarity_hsl'] . '%',
                'similarity_euclidean' => $verifySameOfColor['similarity_euclidean'] . '%'
            ];
            if (!$verifySameOfColor['verify']) {
                $verifyConfirmed = false;
                break;
            }
        }

        graylogInfo('scan image colors', [
            'category' => 'start_scan_image_color',
            'image_url' => $imageUrl,
            'product_hex_color' => $productHexColor,
            'colors' => $colors,
            'time_execute' => $end - $start,
            'order_id' => $orderId,
            'color_percentage' => data_get($result, 'color_percentages'),
            'color_verified' => $colorVerified,
            'confirm' => $verifyConfirmed
        ]);
        if (!$verifyConfirmed) {
            return false;
        }

        return true;
    }

    /**
     * @param $imageUrl
     * @param $options
     * @return array
     */
    public function analyzeImageDetailsColors($imageUrl, $options = [])
    {
        $defaults = self::DEFAULT_CONFIG;

        $config = array_merge($defaults, $options);
        try {
            $imageData = file_get_contents($imageUrl);
            if (!$imageData) {
                throw new \Exception("Could not load image from URL");
            }

            $image = imagecreatefromstring($imageData);
            if (!$image) {
                throw new \Exception("Could not read image's format");
            }

            $width = imagesx($image);
            $height = imagesy($image);

            if (!imageistruecolor($image)) {
                $trueColorImage = imagecreatetruecolor($width, $height);
                imagealphablending($trueColorImage, false);
                imagesavealpha($trueColorImage, true);
                imagecopy($trueColorImage, $image, 0, 0, 0, 0, $width, $height);
                imagedestroy($image);
                $image = $trueColorImage;
            }

            $rawColors = [];
            $totalPixels = 0;
            for ($x = 0; $x < $width; $x += $config['sample_rate']) {
                for ($y = 0; $y < $height; $y += $config['sample_rate']) {
                    $rgb = imagecolorat($image, $x, $y);

                    $alpha = ($rgb >> 24) & 0xFF;
                    if ($alpha > 64) {
                        continue;
                    }

                    $r = ($rgb >> 16) & 0xFF;
                    $g = ($rgb >> 8) & 0xFF;
                    $b = $rgb & 0xFF;

                    // Background filtering
                    if (
                        $config['ignore_background'] &&
                        (($r < $config['dark_threshold'] && $g < $config['dark_threshold'] && $b < $config['dark_threshold']) ||
                            ($r > $config['white_threshold'] && $g > $config['white_threshold'] && $b > $config['white_threshold']))
                    ) {
                        continue;
                    }

                    // === PRE-GROUPING: Group color ===
                    if ($config['enable_pre_grouping']) {
                        $groupKey = $this->getColorGroupKey($r, $g, $b, $config);
                    } else {
                        $groupKey = sprintf("#%02x%02x%02x", $r, $g, $b);
                    }

                    $brightness = ($r * 0.299 + $g * 0.587 + $b * 0.114);

                    if (!isset($rawColors[$groupKey])) {
                        $rawColors[$groupKey] = [
                            'hex' => $groupKey,
                            'rgb' => ['r' => $r, 'g' => $g, 'b' => $b],
                            'brightness' => $brightness,
                            'count' => 0,
                            'variants' => [] // Track original colors
                        ];
                    }

                    $rawColors[$groupKey]['count']++;
                    // Track variants for analysis
                    $originalHex = sprintf("#%02x%02x%02x", $r, $g, $b);
                    if (!isset($rawColors[$groupKey]['variants'][$originalHex])) {
                        $rawColors[$groupKey]['variants'][$originalHex] = 0;
                    }
                    $rawColors[$groupKey]['variants'][$originalHex]++;

                    $totalPixels++;
                }
            }
            imagedestroy($image);
            if ($config['group_similar_colors']) {
                $rawColors = $this->groupSimilarColors($rawColors, $config['color_tolerance']);
            }
            $avgMinPercent = 0;
            if (count($rawColors) > 0) {
                $avgMinPercent = $this->getAvgPercentOfColorInImage(count($rawColors), $config);
            }
            $filteredColors = [];
            $colorPercentages = [];
            foreach ($rawColors as $key => $color) {
                $percentage = ($color['count'] / $totalPixels) * 100;
                $colorPercentages[] = [
                    'color' => $color['hex'],
                    'percentage' => $percentage,
                ];
                if ($percentage >= $avgMinPercent) {
                    $color['percentage'] = round($percentage, 2);
                    $color['brightness_percent'] = round(($color['brightness'] / 255) * 100, 1);
                    $hsl = $this->hexToHsl($color['hex']);
                    $color['hsl'] = $hsl;
                    $color['color_info'] = $this->analyzeColorCharacteristics($hsl);
                    unset($color['count']);
                    $filteredColors[] = $color;
                }
            }
            // sort with appearance percentage
            usort($filteredColors, function ($a, $b) {
                return $b['percentage'] - $a['percentage'];
            });

            // Analyze color similarity groups
            // $colorGroups = $this->categorizeColors($filteredColors);

            return [
                'success' => true,
                'colors' => array_slice($filteredColors, 0, $config['max_colors']),
                'color_percentages' => $colorPercentages,
                //                'color_groups' => $colorGroups,
                'statistics' => [
                    'total_colors_found' => count($rawColors),
                    'significant_colors' => count($filteredColors),
                    'image_dimensions' => ['width' => $width, 'height' => $height],
                    'most_dominant' => !empty($filteredColors) ? $filteredColors[0] : null
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'colors' => []
            ];
        }
    }

    /**
     * @param $colors
     * @param $tolerance
     * @return array
     */
    public function groupSimilarColors($colors, $tolerance = 10)
    {
        $grouped = [];
        $processed = [];

        foreach ($colors as $hex => $color) {
            if (isset($processed[$hex])) continue;

            $group = [$color];
            $groupCount = $color['count'];
            $totalR = $color['rgb']['r'] * $color['count'];
            $totalG = $color['rgb']['g'] * $color['count'];
            $totalB = $color['rgb']['b'] * $color['count'];

            foreach ($colors as $compareHex => $compareColor) {
                if ($hex === $compareHex || isset($processed[$compareHex])) continue;

                $distance = $this->colorDistance($color['rgb'], $compareColor['rgb']);
                if ($distance <= $tolerance) {
                    $group[] = $compareColor;
                    $groupCount += $compareColor['count'];
                    $totalR += $compareColor['rgb']['r'] * $compareColor['count'];
                    $totalG += $compareColor['rgb']['g'] * $compareColor['count'];
                    $totalB += $compareColor['rgb']['b'] * $compareColor['count'];
                    $processed[$compareHex] = true;
                }
            }
            $avgR = round($totalR / $groupCount);
            $avgG = round($totalG / $groupCount);
            $avgB = round($totalB / $groupCount);
            $avgHex = sprintf("#%02x%02x%02x", $avgR, $avgG, $avgB);

            $grouped[$avgHex] = [
                'hex' => $avgHex,
                'rgb' => ['r' => $avgR, 'g' => $avgG, 'b' => $avgB],
                'brightness' => ($avgR * 0.299 + $avgG * 0.587 + $avgB * 0.114),
                'count' => $groupCount,
                'similar_colors' => count($group)
            ];

            $processed[$hex] = true;
        }

        return $grouped;
    }

    /**
     * @param $rgb1
     * @param $rgb2
     * @return float
     */
    public function colorDistance($rgb1, $rgb2)
    {
        return sqrt(
            pow($rgb1['r'] - $rgb2['r'], 2) +
                pow($rgb1['g'] - $rgb2['g'], 2) +
                pow($rgb1['b'] - $rgb2['b'], 2)
        );
    }

    // Phân tích đặc điểm màu
    /**
     * @param $hsl
     * @return array
     */
    public function analyzeColorCharacteristics($hsl)
    {
        $characteristics = [];
        // Phân loại theo độ sáng
        if ($hsl['l'] < 20) $characteristics[] = 'very_dark';
        elseif ($hsl['l'] < 40) $characteristics[] = 'dark';
        elseif ($hsl['l'] < 60) $characteristics[] = 'medium';
        elseif ($hsl['l'] < 80) $characteristics[] = 'light';
        else $characteristics[] = 'very_light';

        // Phân loại theo độ bão hòa
        if ($hsl['s'] < 10) $characteristics[] = 'grayscale';
        elseif ($hsl['s'] < 30) $characteristics[] = 'muted';
        elseif ($hsl['s'] < 70) $characteristics[] = 'moderate';
        else $characteristics[] = 'vivid';

        // Phân loại theo màu sắc
        $colorName = $this->getDetailedColorName($hsl['h'], $hsl['s'], $hsl['l']);

        return [
            'color_name' => $colorName,
            'characteristics' => $characteristics,
            'is_warm' => $this->isWarmColor($hsl['h']),
            'is_neutral' => $hsl['s'] < 10,
            'temperature' => $this->getColorTemperature($hsl['h'])
        ];
    }

    // Lấy tên màu chi tiết

    /**
     * @param $h
     * @param $s
     * @param $l
     * @return int|string
     */
    public function getDetailedColorName($h, $s, $l)
    {
        if ($s < 10) {
            if ($l < 20) return 'black';
            if ($l > 80) return 'white';
            return 'gray';
        }

        $hueNames = [
            [0, 15, 'red'],
            [15, 45, 'orange'],
            [45, 75, 'yellow'],
            [75, 105, 'yellow-green'],
            [105, 135, 'green'],
            [135, 165, 'green-cyan'],
            [165, 195, 'cyan'],
            [195, 225, 'cyan-blue'],
            [225, 255, 'blue'],
            [255, 285, 'blue-magenta'],
            [285, 315, 'magenta'],
            [315, 345, 'magenta-red'],
            [345, 360, 'red']
        ];

        foreach ($hueNames as [$min, $max, $name]) {
            if ($h >= $min && $h < $max) {
                // Thêm prefix theo độ sáng
                if ($l < 30) return "dark_{$name}";
                if ($l > 70) return "light_{$name}";
                return $name;
            }
        }

        return 'unknown';
    }

    // Phân loại màu theo nhóm

    /**
     * @param $colors
     * @return array|array[]
     */
    public function categorizeColors($colors)
    {
        $groups = [
            'warm' => [],
            'cool' => [],
            'neutral' => [],
            'bright' => [],
            'dark' => []
        ];

        foreach ($colors as $color) {
            $hsl = $color['hsl'];

            // Phân nhóm theo temperature
            if ($color['color_info']['is_neutral']) {
                $groups['neutral'][] = $color;
            } elseif ($color['color_info']['is_warm']) {
                $groups['warm'][] = $color;
            } else {
                $groups['cool'][] = $color;
            }

            // Phân nhóm theo độ sáng
            if ($hsl['l'] > 60) {
                $groups['bright'][] = $color;
            } elseif ($hsl['l'] < 40) {
                $groups['dark'][] = $color;
            }
        }

        return $groups;
    }

    /**
     * @param $hue
     * @return string
     */
    private function getColorTemperature($hue)
    {
        if (($hue >= 0 && $hue <= 60) || ($hue >= 300 && $hue <= 360)) {
            return 'warm';
        } elseif ($hue >= 180 && $hue <= 240) {
            return 'cool';
        } else {
            return 'neutral';
        }
    }

    /**
     * @param $hex
     * @return array
     */
    private function hexToHsl($hex)
    {
        // Loại bỏ dấu # nếu có
        $hex = ltrim($hex, '#');

        // Kiểm tra định dạng hex hợp lệ
        if (!preg_match('/^[a-fA-F0-9]{6}$/', $hex)) {
            throw new InvalidArgumentException("Mã hex không hợp lệ: #{$hex}");
        }

        // Chuyển hex thành RGB
        $r = hexdec(substr($hex, 0, 2)) / 255;
        $g = hexdec(substr($hex, 2, 2)) / 255;
        $b = hexdec(substr($hex, 4, 2)) / 255;

        $max = max($r, $g, $b);
        $min = min($r, $g, $b);
        $diff = $max - $min;

        // Tính Lightness (độ sáng)
        $l = ($max + $min) / 2;

        // Tính Saturation (độ bão hòa)
        if ($diff == 0) {
            $s = 0; // Màu xám
        } else {
            $s = $l > 0.5 ? $diff / (2 - $max - $min) : $diff / ($max + $min);
        }

        // Tính Hue (sắc độ)
        if ($diff == 0) {
            $h = 0; // Màu xám, không có hue
        } else {
            switch ($max) {
                case $r:
                    $h = (($g - $b) / $diff) + ($g < $b ? 6 : 0);
                    break;
                case $g:
                    $h = ($b - $r) / $diff + 2;
                    break;
                case $b:
                    $h = ($r - $g) / $diff + 4;
                    break;
            }
            $h /= 6;
        }

        // Chuyển đổi thành độ và phần trăm
        $h = round($h * 360);
        $s = round($s * 100);
        $l = round($l * 100);

        return [
            'h' => $h,      // Hue: 0-360 độ
            's' => $s,      // Saturation: 0-100%
            'l' => $l,      // Lightness: 0-100%
            'hsl_string' => "hsl({$h}, {$s}%, {$l}%)",
            'css' => "hsl({$h}, {$s}%, {$l}%)"
        ];
    }

    /**
     * @param $h
     * @param $s
     * @param $l
     * @return string
     */
    private function getColorType($h, $s, $l)
    {
        if ($s < 10) return 'grayscale';
        if ($l > 90) return 'very_light';
        if ($l < 10) return 'very_dark';

        if ($h >= 0 && $h < 30) return 'red';
        if ($h >= 30 && $h < 60) return 'yellow';
        if ($h >= 60 && $h < 120) return 'green';
        if ($h >= 120 && $h < 180) return 'cyan';
        if ($h >= 180 && $h < 240) return 'blue';
        if ($h >= 240 && $h < 300) return 'magenta';
        if ($h >= 300 && $h < 360) return 'red';

        return 'unknown';
    }

    /**
     * @param $hue
     * @return bool
     */
    private function isWarmColor($hue)
    {
        return ($hue >= 0 && $hue <= 60) || ($hue >= 300 && $hue <= 360);
    }

    /**
     * @param $lightness
     * @return string
     */
    private function getBrightnessLevel($lightness)
    {
        if ($lightness < 20) return 'very_dark';
        if ($lightness < 40) return 'dark';
        if ($lightness < 60) return 'medium';
        if ($lightness < 80) return 'light';
        return 'very_light';
    }

    /**
     * @param $rgb1
     * @param $rgb2
     * @return float
     */
    private function calculateCIE76Similarity($rgb1, $rgb2)
    {
        $lab1 = $this->rgbToLab($rgb1);
        $lab2 = $this->rgbToLab($rgb2);

        $deltaE = sqrt(
            pow($lab1['l'] - $lab2['l'], 2) +
                pow($lab1['a'] - $lab2['a'], 2) +
                pow($lab1['b'] - $lab2['b'], 2)
        );

        // Delta E < 1: Không phân biệt được
        // Delta E < 3: Khó phân biệt
        // Delta E < 5: Dễ phân biệt
        // Delta E > 5: Rất khác biệt

        $similarity = max(0, (100 - ($deltaE * 2))); // Rough conversion
        return round($similarity, 2);
    }

    /**
     * @param $rgb
     * @return array
     */
    private function rgbToLab($rgb)
    {
        $r = $rgb['r'] / 255;
        $g = $rgb['g'] / 255;
        $b = $rgb['b'] / 255;

        $l = ($r * 0.299 + $g * 0.587 + $b * 0.114) * 100;
        $a = ($r - $g) * 128;
        $b = ($g - $b) * 128;

        return ['l' => $l, 'a' => $a, 'b' => $b];
    }

    /**
     * @param $hex
     * @return array
     */
    private function hexToRgb($hex)
    {
        $hex = ltrim($hex, '#');
        return [
            'r' => hexdec(substr($hex, 0, 2)),
            'g' => hexdec(substr($hex, 2, 2)),
            'b' => hexdec(substr($hex, 4, 2))
        ];
    }

    /**
     * @param $rgb1
     * @param $rgb2
     * @return float
     */
    private function calculateWeightedSimilarity($rgb1, $rgb2)
    {
        $rWeight = 0.3;
        $gWeight = 0.59;
        $bWeight = 0.11;

        $distance = sqrt(
            $rWeight * pow($rgb1['r'] - $rgb2['r'], 2) +
                $gWeight * pow($rgb1['g'] - $rgb2['g'], 2) +
                $bWeight * pow($rgb1['b'] - $rgb2['b'], 2)
        );

        $maxDistance = sqrt(
            $rWeight * pow(255, 2) +
                $gWeight * pow(255, 2) +
                $bWeight * pow(255, 2)
        );

        $similarity = (1 - ($distance / $maxDistance)) * 100;
        return round($similarity, 2);
    }

    /**
     * @param $hex1
     * @param $hex2
     * @return float
     */
    private function calculateHSLSimilarity($hex1, $hex2)
    {
        $hsl1 = $this->hexToHsl($hex1);
        $hsl2 = $this->hexToHsl($hex2);

        $hueDiff = abs($hsl1['h'] - $hsl2['h']);
        if ($hueDiff > 180) {
            $hueDiff = 360 - $hueDiff;
        }
        $hueDistance = $hueDiff / 180; // Normalize 0-1

        $satDistance = abs($hsl1['s'] - $hsl2['s']) / 100;
        $lightDistance = abs($hsl1['l'] - $hsl2['l']) / 100;

        $totalDistance = (0.6 * $hueDistance) + (0.2 * $satDistance) + (0.2 * $lightDistance);
        $similarity = (1 - $totalDistance) * 100;

        return round($similarity, 2);
    }

    /**
     * @param $rgb1
     * @param $rgb2
     * @return float
     */
    private function calculateEuclideanSimilarity($rgb1, $rgb2)
    {
        $distance = sqrt(
            pow($rgb1['r'] - $rgb2['r'], 2) +
                pow($rgb1['g'] - $rgb2['g'], 2) +
                pow($rgb1['b'] - $rgb2['b'], 2)
        );

        $maxDistance = sqrt(3 * pow(255, 2));
        $similarity = (1 - ($distance / $maxDistance)) * 100;

        return round($similarity, 2);
    }

    /**
     * @param $color1
     * @param $color2
     * @return array
     */
    private function verify($color1, $color2, $similarityThreshold = 15)
    {
        $hex1 = data_get($color1, 'hex');
        $hex2 = data_get($color2, 'hex');
        $light1 = data_get($color1, 'light');
        $light2 = data_get($color2, 'light');

        if (!$hex1 || !$hex2 || !$light1 || !$light2) {
            return [
                'verify' => true,
                'similarity_percent' => null
            ];
        }

        $similarityWeight = $this->calculateWeightedSimilarity($this->hexToRgb($hex1), $this->hexToRgb($hex2));
        $similarityHsl = $this->calculateHSLSimilarity($hex1, $hex2);
        $similarityEucl = $this->calculateEuclideanSimilarity($this->hexToRgb($hex1), $this->hexToRgb($hex2));
        $similarity = $similarityHsl;
        $isVerified = true;
        if (100 - $similarity < $similarityThreshold && abs($light1 - $light2) < $similarityThreshold) {
            $isVerified = false;
        }

        return [
            'verify' => $isVerified,
            'similarity_percent' => $similarity,
            'similarity_weight' => $similarityWeight,
            'similarity_hsl' => $similarityHsl,
            'similarity_euclidean' => $similarityEucl
        ];
    }

    /**
     * @param $colorValue
     * @param $x
     * @param $y
     * @return array|null
     */
    function validateAndFixColorOverflow($colorValue, $x = 0, $y = 0)
    {
        static $overflowCount = 0;
        static $negativeCount = 0;

        // Case 1: Signed integer overflow (2147483647)
        if ($colorValue == 2147483647) {
            $overflowCount++;

            return [
                'color' => 0xFFFFFFFF, // Trắng hoàn toàn với alpha
                'is_overflow' => true,
                'overflow_type' => 'max_signed_int'
            ];
        }

        // Case 2: Negative values (signed overflow)
        if ($colorValue < 0) {
            $negativeCount++;

            // Chuyển sang unsigned
            $unsignedValue = $colorValue + 4294967296; // 2^32

            // Validate unsigned value
            if ($unsignedValue > 0xFFFFFFFF) {
                return null; // Invalid, skip pixel
            }

            return [
                'color' => $unsignedValue,
                'is_overflow' => true,
                'overflow_type' => 'negative_signed'
            ];
        }

        // Case 3: Validate RGB components từ giá trị bình thường
        $testR = ($colorValue >> 16) & 0xFF;
        $testG = ($colorValue >> 8) & 0xFF;
        $testB = $colorValue & 0xFF;

        // Check if RGB values are reasonable
        if ($testR > 255 || $testG > 255 || $testB > 255) {
            return null; // Skip this pixel
        }

        // Case 4: Normal value
        return [
            'color' => $colorValue,
            'is_overflow' => false,
            'overflow_type' => 'none'
        ];
    }

    // === NEW METHOD: Generate group key for similar colors ===
    /**
     * @param $r
     * @param $g
     * @param $b
     * @param $config
     * @return string
     */
    private function getColorGroupKey($r, $g, $b, $config)
    {
        // Special handling for white variants
        if ($this->isWhiteVariant($r, $g, $b, $config['white_grouping_tolerance'])) {
            return $this->getWhiteGroupKey($r, $g, $b);
        }

        // Special handling for gray variants
        if ($this->isGrayVariant($r, $g, $b)) {
            return $this->getGrayGroupKey($r, $g, $b);
        }

        // General color grouping
        return $this->getGeneralGroupKey($r, $g, $b, $config['pre_group_tolerance']);
    }

    /**
     * @param $r
     * @param $g
     * @param $b
     * @param $tolerance
     * @return bool
     */
    private function isWhiteVariant($r, $g, $b, $tolerance = 30)
    {
        // Check if all components are in high range
        return ($r > 190 && $g > 190 && $b > 190) &&
            (abs($r - $g) < $tolerance && abs($g - $b) < $tolerance && abs($r - $b) < $tolerance);
    }

    /**
     * @param $r
     * @param $g
     * @param $b
     * @return string
     */
    private function getWhiteGroupKey($r, $g, $b)
    {
        // Group all whites into ranges
        if ($r > 240 && $g > 240 && $b > 240) {
            return "#ffffff"; // Pure white group
        } elseif ($r > 220 && $g > 220 && $b > 220) {
            return "#f0f0f0"; // Light white group
        } else {
            return "#e0e0e0"; // Off-white group
        }
    }

    /**
     * @param $r
     * @param $g
     * @param $b
     * @param $tolerance
     * @return bool
     */
    private function isGrayVariant($r, $g, $b, $tolerance = 15)
    {
        return abs($r - $g) < $tolerance && abs($g - $b) < $tolerance && abs($r - $b) < $tolerance;
    }

    /**
     * @param $r
     * @param $g
     * @param $b
     * @return string
     */
    private function getGrayGroupKey($r, $g, $b)
    {
        $avg = intval(($r + $g + $b) / 3);

        // Round to nearest 20 for grouping
        $grouped = round($avg / 20) * 20;
        $grouped = max(0, min(255, $grouped));

        return sprintf("#%02x%02x%02x", $grouped, $grouped, $grouped);
    }

    /**
     * @param $r
     * @param $g
     * @param $b
     * @param $tolerance
     * @return string
     */
    private function getGeneralGroupKey($r, $g, $b, $tolerance)
    {
        // Round each component to nearest step
        $step = $tolerance;

        $groupedR = round($r / $step) * $step;
        $groupedG = round($g / $step) * $step;
        $groupedB = round($b / $step) * $step;

        $groupedR = max(0, min(255, $groupedR));
        $groupedG = max(0, min(255, $groupedG));
        $groupedB = max(0, min(255, $groupedB));

        return sprintf("#%02x%02x%02x", $groupedR, $groupedG, $groupedB);
    }

    /**
     * @param $numberColor
     * @param $config
     * @return float|int|mixed
     */
    private function getAvgPercentOfColorInImage($numberColor, $config)
    {
        $avg = 100 / $numberColor;
        if ($avg < $config['min_percentage']) {
            return $config['min_percentage'];
        }

        if ($avg > $config['max_percentage']) {
            return $config['max_percentage'];
        }

        return $avg;
    }
}
