<?php

namespace App\Services;

use App\Enums\DateRangeEnum;
use App\Http\Controllers\Analytic3\SellerController;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\User;
use Illuminate\Http\Request;

class NewOrderNotification
{
    /**
     * @param Order $order
     * @return string
     */
    public static function generate(Order $order): string
    {
        $br = "\n\n";
        $message = '🎉 New Order: ' . $order->order_number . $br;
        $message .= 'Total Amount: ' . UserService::formatCurrency($order->total_amount) . $br;
        $message .= 'Today Profit: ' . self::getTodayProfit($order->seller_id) . $br;
        $message .= 'Total Balance: ' . self::getSellerBalance($order->seller_id) . $br;

        if ($order->store_domain) {
            $message .= 'Store: ' . $order->store_domain . $br;
        }

        $message .= 'Country: ' . self::getCountryName($order->country) . $br;
        $message .= '----- Total Quantity: ' . $order->total_quantity . ' -----' . $br;
        $message .= self::getProducts($order);
        return $message;
    }

    /**
     * @param $countryCode
     * @return mixed
     */
    private static function getCountryName($countryCode)
    {
        $location = getLocationByCode($countryCode);

        return ($location && isset($location->name)) ? $location->name : $countryCode;
    }

    /**
     * @param Order $order
     * @return string
     */
    private static function getProducts(Order $order): string
    {
        $order->loadMissing('order_products');
        $products = $order->order_products;
        $result = '';
        $products->each(function (OrderProduct $product) use (&$result) {
            $result .= ' + ' . $product->campaign_title . ' - ' . $product->product_name . ' x ' . $product->quantity . "\n\n";
        });

        return $result;
    }

    /**
     * @param $sellerId
     * @return string
     */
    public static function getTodayProfit($sellerId): string
    {
        $filterRequest = new Request();
        $filterRequest['date_type'] = DateRangeEnum::TODAY;
        $filterRequest['seller_id'] = $sellerId;
        $controller = new SellerController();
        $controller->setCommonFilter($filterRequest);
        $profit = $controller->getRealtimeAnalyticOrderQuery($sellerId)->selectRaw('SUM(`order`.total_seller_profit) AS profits')->value('profits');
        return UserService::formatCurrency($profit);
    }

    private static function getSellerBalance($sellerId): string
    {
        $balance = User::query()
            ->select('balance')
            ->where('id', $sellerId)
            ->value('balance');

        return UserService::formatCurrency($balance);
    }
}
