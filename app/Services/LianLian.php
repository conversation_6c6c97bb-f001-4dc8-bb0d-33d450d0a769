<?php

namespace App\Services;

use App\Enums\LianLianPayoutPurpose;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class LianLian
{
    private $httpClient;
    private $developerId;
    private $accessToken;
    private $privateKey;
    private $originalPrivateKey; // for debugging
    private $createdSignature; // for debugging

    const DEFAULT_REGION = 'sandbox';
    const DEFAULT_CURRENCY = 'USD';
    const DEFAULT_PURPOSE = LianLianPayoutPurpose::E_COMMERCE_COLLECTION;

    public function __construct(string $developerId, string $accessToken, string $privateKey, string $region = self::DEFAULT_REGION)
    {
        $this->privateKey = self::priKey($privateKey);
        $this->originalPrivateKey = $privateKey;
        $baseDomain = $this->getBaseDomain($region);

        $this->developerId = $developerId;
        $this->accessToken = $accessToken;

        $this->httpClient = Http::withBasicAuth($developerId, $accessToken)
            ->timeout(30)
            ->baseUrl($baseDomain . '/gateway/v1');
    }

    private function getBaseDomain(string $region): string
    {
        $region = strtolower($region);

        // https://docs.lianlianglobal.com/get-started.html#section/Get-Started-with-the-API/Apply-for-a-Account
        switch ($region) {
            case 'us':
                return 'https://us-api.lianlianglobal.com';
            case 'uk':
                return 'https://uk-api.lianlianglobal.com';
            case 'cn':
                return 'https://global-api.lianlian.com';
            case 'sg':
            case 'vn':
            case 'th':
            case 'hk':
                return 'https://singapore-api.lianlianglobal.com';
            default:
                return 'https://test-global-api.lianlianpay-inc.com';
        }
    }

    public function getAccountInfo()
    {
        $this->generateLLPaySignature('/ew-accounts/account');
        $res = $this->httpClient->get('/ew-accounts/account');
        return $this->handleResponse($res);
    }

    public function getAllBalances()
    {
        $this->generateLLPaySignature('/ew-balances');
        $res = $this->httpClient->get('/ew-balances');
        return $this->handleResponse($res);
    }

    private function handleResponse($res)
    {
        $json = $res->json();

        if ($res->successful()) {
            return $json;
        }

        if (app()->isProduction()) {
            $logMessage = 'LianLian API error: ' . "\n```\n" . json_encode($json) . "\n```\n";
            $logMessage .= 'Developer ID: `' . $this->developerId . "`\n";
            $accessToken = substr($this->accessToken, 0, 5) . '...' . substr($this->accessToken, -5);
            $logMessage .= 'Access token: `' . $accessToken . "`\n";
            $privateKey = substr($this->originalPrivateKey, 0, 10) . '...' . substr($this->originalPrivateKey, -10);
            $logMessage .= 'Private key: `' . $privateKey . "`\n";

            logToDiscord($logMessage, 'payout', true);
        }

        return null;
    }

    public function createPayout(
        string $payeeId,
        string $amount,
        string $payCurrency = self::DEFAULT_CURRENCY,
        string $sendCurrency = self::DEFAULT_CURRENCY,
        string $purpose = self::DEFAULT_PURPOSE,
        string $requestId = ''
    )
    {
        if (empty($requestId)) {
            $requestId = Str::uuid()->toString();
        }

        // notes: payee_id is the network contact id (see createNetworkContact method)
        // https://docs.lianlianglobal.com/api-reference.html#tag/Payouts/operation/post-payout
        $requestBody = [
            'request_id' => $requestId,
            'payee_id' => $payeeId,
            'pay_currency' => $payCurrency,
            'send_currency' => $sendCurrency,
            'purpose' => $purpose,
            'pay_amount' => $amount,
        ];

        $this->generateLLPaySignature('/ew-payouts', json_encode($requestBody), 'POST');

        $res = $this->httpClient
            ->asJson()
            ->post('/ew-payouts', $requestBody);

        return $this->handleResponse($res);
    }

    public function createNetworkContact(string $email, string $countryCode)
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            return null;
        }

        // https://docs.lianlianglobal.com/api-reference.html#tag/Network-Contacts/operation/post-ew-payees-network
        $requestBody = [
            'entity_type' => 'INDIVIDUAL',
            'base_info' => [
                'country_code' => $countryCode,
                'email' => $email,
            ],
        ];

        $this->generateLLPaySignature('/ew-contacts/network', json_encode($requestBody), 'POST');

        $res = $this->httpClient
            ->asJson()
            ->post('/ew-contacts/network', $requestBody);

        return $this->handleResponse($res);
    }

    public function getAllNetworkContacts(int $page = 1, int $pageSize = 10)
    {
        $queryString = urlencode('page=' . $page . '&page_size=' . $pageSize);

        // this API does not require signature, just add for consistency
        $this->generateLLPaySignature('/ew-contacts/network/list', '', 'GET', $queryString);
        $res = $this->httpClient->get('/ew-contacts/network/list', [
            'page' => $page,
            'page_size' => $pageSize,
        ]);

        return $this->handleResponse($res);
    }

    private function makeSignature($time, $URI, $payloadJSON = '', $QUERY_STRING_urlencode = '', $method = 'GET'): string
    {
        if ($payloadJSON && $QUERY_STRING_urlencode) {
            $v = $method . '&' . $URI . '&' . $time . '&' . $payloadJSON . '&' . $QUERY_STRING_urlencode;
        } elseif ($payloadJSON && !$QUERY_STRING_urlencode) {
            $v = $method . '&' . $URI . '&' . $time . '&' . $payloadJSON;
        } elseif (!$payloadJSON && $QUERY_STRING_urlencode) {
            $v = $method . '&' . $URI . '&' . $time . '&&' . $QUERY_STRING_urlencode;
        } else {
            $v = $method . '&' . $URI . '&' . $time . '&';
        }

        $signature = '';
        openssl_sign($v, $signature, $this->privateKey, OPENSSL_ALGO_SHA256);
        return base64_encode($signature);
    }

    private function makeHeaderStr($time, $URI, $payloadJSON = '', $QUERY_STRING_urlencode = '', $method = 'GET'): string
    {
        $v = $this->makeSignature($time, $URI, $payloadJSON, $QUERY_STRING_urlencode, $method);
        return 't=' . $time . ',v=' . $v;
    }

    private static function priKey(string $privateKey): string
    {
        $privateKey = chunk_split($privateKey, 64, "\n");
        return "-----BEGIN RSA PRIVATE KEY-----\n$privateKey-----END RSA PRIVATE KEY-----\n";
    }

    private function generateLLPaySignature(string $URI, string $payloadJSON = '', string $method = 'GET', string $queryString = ''): void
    {
        $signature = $this->makeHeaderStr(time(), '/gateway/v1' . $URI, $payloadJSON, $queryString, $method);
        $this->createdSignature = $signature;
        $this->httpClient->replaceHeaders(['LLPAY-Signature' => $signature]);
    }
}
