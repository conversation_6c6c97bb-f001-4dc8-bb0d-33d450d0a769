<?php

namespace App\Services;

use App\Enums\OpenAIImageFormatEnum;
use App\Enums\OpenAIImageQualityEnum;
use App\Enums\OpenAIImageSizeEnum;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Http;
use InvalidArgumentException;

class OpenAIImageService
{
    /**
     * Base URL for OpenAI API
     */
    protected const BASE_URL = 'https://api.openai.com/v1';

    /**
     * API key for OpenAI
     */
    protected string $apiKey;

    /**
     * HTTP request timeout in seconds
     */
    protected int $timeout;

    /**
     * Default model for image generation
     */
    protected string $defaultModel = 'gpt-image-1';

    /**
     * Allowed image types for upload
     */
    public const ALLOWED_IMAGE_TYPES = [
        'image/jpeg',
        'image/png',
        'image/webp'
    ];

    /**
     * Constructor
     *
     * @param int|null $timeout HTTP request timeout in seconds
     */
    public function __construct(?int $timeout = null)
    {
        $this->apiKey = config('services.openai.api_key');
        $this->timeout = $timeout ?? 30; // Default to 30 seconds if not specified
        $this->validateApiKey();
    }

    /**
     * Validate API key
     *
     * @throws \RuntimeException
     */
    protected function validateApiKey(): void
    {
        if (empty($this->apiKey)) {
            throw new \RuntimeException('OpenAI API key not set.');
        }
    }

    /**
     * Generate images from text prompt using gpt-image-1 model
     *
     * @param string $prompt The text prompt to generate images from
     * @param int $numImages Number of images to generate (default: 1)
     * @param string $size Image size (default: OpenAIImageSizeEnum::SQUARE - 1024x1024)
     * @param string|null $output_format Format for returned images (default: OpenAIImageFormatEnum::PNG)
     * @param string|null $quality Quality of the generated image (default: OpenAIImageQualityEnum::AUTO)
     * @return array|null The response from OpenAI API
     * @throws \Exception
     */
    public function generateImage(
        string  $prompt,
        int     $numImages = 1,
        string  $size = OpenAIImageSizeEnum::SQUARE,
        ?string $output_format = OpenAIImageFormatEnum::PNG,
        ?string $quality = OpenAIImageQualityEnum::AUTO
    ): ?array
    {
        try {
            $this->validateApiKey();

            // Validate size
            if (!$this->isValidSize($size)) {
                throw new InvalidArgumentException(
                    "Invalid size. Supported sizes are: " . implode(', ', OpenAIImageSizeEnum::getValidSizes())
                );
            }

            // Validate output_format
            if (!$this->isValidFormat($output_format)) {
                throw new InvalidArgumentException(
                    "Invalid output_format. Supported formats are: " . implode(', ', OpenAIImageFormatEnum::getValidFormats())
                );
            }

            // Validate quality
            if (!$this->isValidQuality($quality)) {
                throw new InvalidArgumentException(
                    "Invalid quality. Supported qualities are: " . implode(', ', OpenAIImageQualityEnum::getValidQualities())
                );
            }

            // Prepare request data
            $requestData = [
                'model' => $this->defaultModel,
                'prompt' => $prompt,
                'n' => $numImages,
                'size' => $size
            ];

            // Add output_format if provided
            if ($output_format !== null) {
                $requestData['output_format'] = $output_format;
            }

            // Add quality if provided
            if ($quality !== null) {
                $requestData['quality'] = $quality;
            }

            $response = Http::withToken($this->apiKey)
                ->asJson()
                ->timeout($this->timeout)
                ->connectTimeout($this->timeout)
                ->retry(3, 1000) // Retry up to 3 times with 1 second delay between attempts
                ->post(self::BASE_URL . '/images/generations', $requestData);

            if ($response->successful()) {
                $result = $response->json();

                // Format the response to match the expected format in the controller
                return [
                    'data' => array_map(function ($item) {
                        return [
                            'url' => $item['url'] ?? null,
                            'b64_json' => $item['b64_json'] ?? null
                        ];
                    }, $result['data'] ?? [])
                ];
            }

            return [
                'error' => true,
                'message' => 'OpenAI API error: ' .
                    $response->status() . ' - ' .
                    ($response->json('error.message') ?? 'Unknown error')
            ];
        } catch (\Exception $e) {
            return [
                'error' => true,
                'message' => 'Image processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Edit an image using OpenAI's image edit API with gpt-image-1 model
     *
     * @param UploadedFile|string $image The image file to edit (can be UploadedFile or path to file)
     * @param string $prompt The text prompt to guide the edit
     * @param UploadedFile|string|array|null $referenceImages Reference images to use for editing (optional)
     * @param string $size Image size (default: OpenAIImageSizeEnum::SQUARE - 1024x1024)
     * @param string|null $output_format Format for returned images (default: OpenAIImageFormatEnum::PNG)
     * @param string|null $quality Quality of the generated image (default: OpenAIImageQualityEnum::AUTO)
     * @return array|null The response from OpenAI API
     */
    public function editImage(
        UploadedFile|string            $image,
        string                         $prompt,
        UploadedFile|string|array|null $referenceImages = null,
        string                         $size = OpenAIImageSizeEnum::SQUARE,
        ?string                        $output_format = OpenAIImageFormatEnum::PNG,
        ?string                        $quality = OpenAIImageQualityEnum::AUTO
    ): ?array
    {
        try {
            $this->validateApiKey();

            $imageFile = $image instanceof UploadedFile ? $image->path() : $image;

            // Validate size
            if (!$this->isValidSize($size)) {
                throw new InvalidArgumentException(
                    "Invalid size. Supported sizes are: " . implode(', ', OpenAIImageSizeEnum::getValidSizes())
                );
            }

            // Check if the image file exists and is readable
            if (!file_exists($imageFile) || !is_readable($imageFile)) {
                throw new InvalidArgumentException(
                    'Image file does not exist or is not readable: ' . $imageFile
                );
            }

            // Get mime type
            $mimeType = $image instanceof UploadedFile
                ? $image->getMimeType()
                : mime_content_type($imageFile);

            // Validate image type
            if (!$this->isValidImageType($mimeType)) {
                throw new InvalidArgumentException(
                    'Invalid image type. Supported types are: ' . implode(', ', self::ALLOWED_IMAGE_TYPES)
                );
            }

            // Validate output_format
            if (!$this->isValidFormat($output_format)) {
                throw new InvalidArgumentException(
                    "Invalid output_format. Supported formats are: " . implode(', ', OpenAIImageFormatEnum::getValidFormats())
                );
            }

            // Validate quality
            if (!$this->isValidQuality($quality)) {
                throw new InvalidArgumentException(
                    "Invalid quality. Supported qualities are: " . implode(', ', OpenAIImageQualityEnum::getValidQualities())
                );
            }

            // Start building the request
            $request = Http::withToken($this->apiKey)
                ->timeout($this->timeout)
                ->connectTimeout($this->timeout)
                ->retry(3, 1000) // Retry up to 3 times with 1 second delay between attempts
                ->asMultipart();

            // Add the main image with explicit Content-Type
            $request->attach(
                'image[]',
                file_get_contents($imageFile),
                basename($imageFile),
                ['Content-Type' => $mimeType]
            );

            // Add reference images if provided
            if ($referenceImages) {
                if (is_array($referenceImages)) {
                    foreach ($referenceImages as $refImage) {
                        $refImageFile = $refImage instanceof UploadedFile ? $refImage->path() : $refImage;

                        // Check if the reference image file exists and is readable
                        if (!file_exists($refImageFile) || !is_readable($refImageFile)) {
                            throw new InvalidArgumentException(
                                'Reference image file does not exist or is not readable: ' . $refImageFile
                            );
                        }

                        // Get mime type of reference image
                        $refMimeType = $refImage instanceof UploadedFile
                            ? $refImage->getMimeType()
                            : mime_content_type($refImageFile);

                        // Add reference image with explicit Content-Type
                        $request->attach(
                            'image[]',
                            file_get_contents($refImageFile),
                            basename($refImageFile),
                            ['Content-Type' => $refMimeType]
                        );
                    }
                } else {
                    $refImageFile = $referenceImages instanceof UploadedFile ? $referenceImages->path() : $referenceImages;

                    // Check if the reference image file exists and is readable
                    if (!file_exists($refImageFile) || !is_readable($refImageFile)) {
                        throw new InvalidArgumentException(
                            'Reference image file does not exist or is not readable: ' . $refImageFile
                        );
                    }

                    // Get mime type of reference image
                    $refMimeType = $referenceImages instanceof UploadedFile
                        ? $referenceImages->getMimeType()
                        : mime_content_type($refImageFile);

                    // Add reference image with explicit Content-Type
                    $request->attach(
                        'image[]',
                        file_get_contents($refImageFile),
                        basename($refImageFile),
                        ['Content-Type' => $refMimeType]
                    );
                }
            }

            // Add other form fields
            $formData = [
                'model' => $this->defaultModel,
                'prompt' => $prompt,
                'size' => $size
            ];

            // Add output_format if provided
            if ($output_format !== null) {
                $formData['output_format'] = $output_format;
            }

            // Add quality if provided
            if ($quality !== null) {
                $formData['quality'] = $quality;
            }

            // Make the request with form data
            $response = $request->post(self::BASE_URL . '/images/edits', $formData);

            if ($response->successful()) {
                $result = $response->json();

                // Format the response to match the expected format in the controller
                return [
                    'data' => array_map(function ($item) {
                        return [
                            'url' => $item['url'] ?? null,
                            'b64_json' => $item['b64_json'] ?? null
                        ];
                    }, $result['data'] ?? [])
                ];
            }

            return [
                'error' => true,
                'message' => 'OpenAI API error: ' .
                    $response->status() . ' - ' .
                    ($response->json('error.message') ?? 'Unknown error')
            ];
        } catch (\Exception $e) {
            return [
                'error' => true,
                'message' => 'Image processing error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if the image type is valid
     *
     * @param string $mimeType
     * @return bool
     */
    protected function isValidImageType(string $mimeType): bool
    {
        return in_array($mimeType, self::ALLOWED_IMAGE_TYPES);
    }

    /**
     * Validate if the size is supported by OpenAI's image model
     *
     * @param string $size
     * @return bool
     */
    protected function isValidSize(string $size): bool
    {
        return OpenAIImageSizeEnum::isValidSize($size);
    }

    /**
     * Validate if the quality is supported by OpenAI's image model
     *
     * @param string $quality
     * @return bool
     */
    protected function isValidQuality(string $quality): bool
    {
        return OpenAIImageQualityEnum::isValidQuality($quality);
    }

    /**
     * Validate if the format is supported by OpenAI's image model
     *
     * @param string $format
     * @return bool
     */
    protected function isValidFormat(string $format): bool
    {
        return OpenAIImageFormatEnum::isValidFormat($format);
    }
}
