<?php

namespace App\Services;

use App\Contracts\TrackingServiceContract;
use App\Enums\TrackingServiceEnum;
use App\Enums\TrackingStatusEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class Trakow implements TrackingServiceContract
{
    // Main status
    public const PENDING = 'pending';
    public const NO_RECORD = 'no_record';
    public const NOT_FOUND = 'not_found';
    public const INFO_RECEIVED = 'information_received';
    public const AVAILABLE_FOR_PICKUP = 'available_for_pickup';
    public const IN_TRANSIT = 'in_transit';
    public const OUT_FOR_DELIVERY = 'out_for_delivery';
    public const WAITING_DELIVERY = 'waiting_delivery';
    public const DELIVERY_FAILED = 'delivery_failed';
    public const ABNORMAL = 'abnormal';
    public const EXCEPTION = 'exception';
    public const FAILED_ATTEMPT = 'failed_attempt';
    public const DELIVERED = 'delivered';
    public const EXPIRED = 'expired';
    public const COURIER_NOT_SUPPORTED = 'courier_not_supported';

    protected $apiEndpoint;
    protected $secretKey;

    public function __construct()
    {
        $this->apiEndpoint = config('services.tracking.trakow.api_endpoint');
        $this->secretKey = config('services.tracking.trakow.secret_key');
    }
    /**
     * @param $trackingNumbers
     * @return array|mixed|null
     * @throws \Throwable
     */
    public static function register($trackingNumbers)
    {
        return (new self)->postRequest('tracking/push-tracking', $trackingNumbers);
    }

    /**
     * Trakow does not support delete track
     * @param $trackingNumbers
     * @return false
     * @throws \Throwable
     */
    public static function deleteTrack($trackingNumbers)
    {
        return false;
    }

    /**
     * @param $trackingNumbers
     * @return array
     * @throws \Throwable
     */
    public static function getTrackInfo($trackingNumbers)
    {
        $result = [
            'accepted' => [],
            'rejected' => []
        ];
        $response = (new self)->postRequest('tracking/get-tracking-info', $trackingNumbers);
        if (empty($response)) {
            return $result;
        }
        if (!empty($response['accepted'])) {
            $result['accepted'] = $response['accepted'];
        }
        if (!empty($response['rejected'])) {
            $rejected = [];
            if (is_array($response['rejected'])) {
                $rejected = array_map(static function ($item) {
                    return [
                        'trackingId' => $item,
                        'error' => [
                            'code' => self::NO_RECORD,
                            'message' => 'TrackingId not registered'
                        ]
                    ];
                }, $response['rejected']);
            }
            $result['rejected'] = $rejected;
        }
        $result['service'] = TrackingServiceEnum::TRAKOW;
        return $result;
    }

    /**
     * Temporary used the Trakow carriers
     *
     * @return Collection
     */
    public static function carriers(): Collection
    {
        try {
            $carriersFilePath = resource_path('/json/carriers/trakow.json');
            if (!File::exists($carriersFilePath)) {
                return collect();
            }
            $carriers = File::get($carriersFilePath);
            $carriers = json_decode($carriers, true, 512, JSON_THROW_ON_ERROR);
            return collect($carriers);
        } catch (\Throwable $e) {
            return collect();
        }
    }

    /**
     * @param $trackingCodes
     * @return array|false
     */
    public static function registerOrderTracking($trackingCodes): array|false
    {
        try {
            $insertCodes = [];
            $updateCodes = [];
            $orderProducts = $trackingCodes->toArray();
            $register = self::register($orderProducts);
            if (empty($register['state'])) {
                graylogInfo('1. [Trakow] Register tracking number failed', [
                    'category' => 'tracking_status_logs',
                    'tracking_codes' => $trackingCodes,
                    'response' => $register,
                ]);
                return false;
            }
            if (empty($register['rejected']) && empty($register['accepted'])) {
                graylogInfo('2. [Trakow] Register tracking number failed', [
                    'category' => 'tracking_status_logs',
                    'tracking_codes' => $trackingCodes,
                    'response' => $register,
                ]);
                return false;
            }
            if (!empty($register['accepted'])) {
                foreach ($register['accepted'] as $acceptedCode) {
                    $code = TrackingService::getObjectTrackingCode($trackingCodes, $acceptedCode);
                    if (empty($code)) {
                        continue;
                    }
                    $insertCodes[$code->tracking_code] = [
                        [
                            'tracking_code' => $code->tracking_code,
                            'order_number' => data_get($code, 'order.order_number', data_get($code, 'order_id')),
                        ],
                        [
                            'order_id' => data_get($code, 'order_id', data_get($code, 'order.order_number')),
                            'status' => TrackingStatusEnum::NEW,
                            'shipping_carrier' => self::detectCarrier(data_get($code, 'tracking_code'), data_get($code, 'shipping_carrier'), (data_get($code, 'supplier_name') ? strtolower(trim(data_get($code, 'supplier_name'))) : null), data_get($code, 'tracking_url')),
                            'tracking_service' => TrackingServiceEnum::TRAKOW
                        ]
                    ];
                    $updateCodes[] = [
                        [
                            'tracking_code' => $code->tracking_code
                        ],
                        [
                            'tracking_status' => TrackingStatusEnum::NEW,
                            'tracking_service' => TrackingServiceEnum::TRAKOW
                        ]
                    ];
                }
            }
            return [$insertCodes, $updateCodes];
        } catch (\Throwable $e) {
            return [[], []];
        }
    }

    /**
     * @return array
     */
    public static function statusesMapWithTrackingStatusEnum(): array
    {
        return [
            self::PENDING => TrackingStatusEnum::NEW,
            self::NO_RECORD => TrackingStatusEnum::NOTFOUND,
            self::NOT_FOUND => TrackingStatusEnum::NOTFOUND,
            self::INFO_RECEIVED => TrackingStatusEnum::INFO_RECEIVED,
            self::IN_TRANSIT => TrackingStatusEnum::TRANSIT,
            self::AVAILABLE_FOR_PICKUP => TrackingStatusEnum::TRANSIT,
            self::OUT_FOR_DELIVERY => TrackingStatusEnum::TRANSIT,
            self::DELIVERY_FAILED => TrackingStatusEnum::TRANSIT,
            self::WAITING_DELIVERY => TrackingStatusEnum::OUT_FOR_DELIVERY,
            self::EXPIRED => TrackingStatusEnum::EXPIRED,
            self::DELIVERED => TrackingStatusEnum::DELIVERED,
            self::ABNORMAL => TrackingStatusEnum::EXCEPTION,
            self::FAILED_ATTEMPT => TrackingStatusEnum::EXCEPTION,
            self::EXCEPTION => TrackingStatusEnum::EXCEPTION,
            self::COURIER_NOT_SUPPORTED => TrackingStatusEnum::EXCEPTION,
        ];
    }

    /**
     * @param $status
     * @return string|null
     */
    public static function convertTrackingStatus($status): ?string
    {
        $statuses = self::statusesMapWithTrackingStatusEnum();
        return $statuses[$status] ?? null;
    }

    /**
     * @return Collection
     */
    public static function detectPatterns(): Collection
    {
        return collect([
            [
                'carrier_name' => ['springgds'],
                'carrier_key' => "spring_gds",
                'tracking_code_prefix' => ['3SDOKC'],
            ],
            [
                'carrier_name' => ['ups'],
                'carrier_key' => "ups",
            ],
            [
                'carrier_name' => ['usps', 'genericstandard'],
                'carrier_key' => "usps",
                'tracking_code_prefix' => ['92', '94'],
                'tracking_code_length' => [26]
            ],
            [
                'carrier_name' => ['royalmail'],
                'carrier_key' => "royal_mail",
            ],
            [
                'carrier_name' => ['bartolini', 'brtbartolini'],
                'carrier_key' => "brt_bartolini",
            ],
            [
                'carrier_name' => ['gls'],
                'carrier_key' => 'gls',
                'suppliers' => ['jondo', 'textildruck'],
            ],
            [
                'carrier_name' => ['asendiausa'],
                'carrier_key' => "asendia_usa",
                'suppliers' => ['monsterdigital', 'dreamship', 'gooten', 'jondo', 'printlogistic', 'printlogisticv2'],
            ],
            [
                'carrier_name' => ['dhl', 'dhlpaket'],
                'carrier_key' => "dhl_paket",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['dhlecommercecn'],
                'carrier_key' => "dhl_ecommerce_cn",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['dhlecommerceus'],
                'carrier_key' => "dhl_ecommerce_us",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['dhlecommerce', 'dhlexpress', 'dhlecs', 'dhlgm', 'genericstandard', 'dhlecs', 'dhlwarenpost'],
                'carrier_key' => "dhl",
                'suppliers' => ['gooten', 'dreamship', 'gearment', 'moteefe', 'textildruck', 'printforia', 'printlogistic', 'optondemand', 'monsterdigital'],
                'tracking_code_prefix' => ['GM', '00'],
                'tracking_code_length' => [20, 22]
            ],
            [
                'carrier_name' => ['fedex'],
                'carrier_key' => 'fedex',
            ],
            [
                'carrier_name' => ['yunexpress'],
                'carrier_key' => 'yun_express',
                'tracking_code_prefix' => ['YT'],
            ],
            [
                'carrier_name' => ['chitchats'],
                'carrier_key' => 'chit_chat',
            ],
            [
                'carrier_name' => ['gtagsm'],
                'carrier_key' => 'gta_gsm',
                'tracking_code_prefix' => ['GR'],
                'tracking_code_length' => [19]
            ],
            [
                'carrier_name' => ['canadapost'],
                'carrier_key' => "canada_post",
                'suppliers' => ['printgeek'],
                'tracking_code_prefix' => ['201506'],
                'tracking_code_length' => [16]
            ],
            [
                'carrier_name' => ['dpd'],
                'suppliers' => ['printlogisticv2'],
                'carrier_key' => "dpd",
            ]
        ]);
    }

    /**
     * @param $code
     * @param $message
     * @return false
     */
    public static function isSkipCheck($code, $message = '')
    {
        return false;
    }

    /**
     * @param $code
     * @param string $message
     * @return bool
     */
    public static function isNeedRegister($code, $message = ''): bool
    {
        return $code && !empty($message) && str_ends_with($message, 'not registered');
    }

    /**
     * @param $trackingNumber
     * @param $carrier
     * @param $supplier
     * @param $trackingUrl
     * @return \string|false|null
     */
    public static function detectCarrier($trackingNumber, $carrier = null, $supplier = null, $trackingUrl = null)
    {
        if (empty($trackingNumber)) {
            return null;
        }
        $carrier = str_replace(' ', '', strtolower(trim($carrier)));
        $supplier = str_replace(' ', '', strtolower(trim($supplier)));
        $trackingUrl = trim($trackingUrl);
        $patterns = self::detectPatterns();
        $carrierFilter = $patterns->first(function ($pattern) use ($trackingNumber, $carrier, $supplier, $trackingUrl) {
            $carrierMatch = !empty($pattern['carrier_name']) && !empty($carrier) && (in_array($carrier, $pattern['carrier_name'], true) || Str::startsWith($carrier, $pattern['carrier_name']));
            $supplierMatch = !empty($pattern['suppliers']) && in_array($supplier, $pattern['suppliers'], true);
            $trackingCodePrefixMatch = !empty($pattern['tracking_code_prefix']) && collect($pattern['tracking_code_prefix'])->first(fn($prefix) => str_starts_with($trackingNumber, $prefix));
            $trackingCodeLength = !empty($pattern['tracking_code_length']) && in_array(strlen($trackingNumber), $pattern['tracking_code_length'], true);
            $domainMatch = false;
            if (!empty($pattern['domain']) && !empty($trackingUrl)) {
                $parseUrl = parse_url($trackingUrl);
                if ($parseUrl && !empty($parseUrl['host'])) {
                    $domain = str_replace('www.', '', $parseUrl['host']);
                    $domainMatch = in_array($domain, $pattern['domain'], true);
                }
            }
            if ($carrier === 'usps' && $trackingCodePrefixMatch && str_starts_with($trackingNumber, 91) && $trackingCodeLength && strlen($trackingNumber) === 21) {
                return true;
            }
            if (!empty($pattern['suppliers'])) {
                return $supplierMatch && ($carrierMatch || $domainMatch || $trackingCodePrefixMatch);
            }
            return $carrierMatch || $domainMatch || $trackingCodePrefixMatch;
        });
        return data_get($carrierFilter, 'carrier_key');
    }

    /**
     * @param $url
     * @param array $data
     * @param bool $handleExcept
     * @return array|mixed|null
     * @throws \Throwable
     */
    public function postRequest($url, array $data = [], bool $handleExcept = false)
    {
        if (!app()->isProduction()) {
            return null;
        }
        if (!$handleExcept) {
            [$url, $data] = $this->handle($url, $data);
        }
        $response = Http::asJson()
            ->withoutVerifying()
            ->withUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36')
            ->withHeaders([
                'Authorization-Token' => $this->secretKey,
            ])
            ->post($this->apiEndpoint . $url, $data);
        if ($response->failed()) {
            if ($response->status() === 429) {
                return null;
            }
            graylogError('[Trakow] Post request failed to ' . $url, [
                'category' => 'tracking_status_logs',
                'url' => $url,
                'response_status' => $response->status(),
                'response_data' => $response->json(),
                'response_header' => json_encode($response->headers(), JSON_THROW_ON_ERROR),
            ]);
            logToDiscord('[Trakow] Post request failed to ' . $url . ' - Status: ' . $response->status(), 'tracking_status_logs', true);
            return null;
        }
        $response = $response->json();
        return !empty($response['data']) ? $response['data'] : $response;
    }

    /**
     * @param $request
     * @return true
     */
    public function verifySignature($request)
    {
        return true;
    }

    /**
     * @param $url
     * @param $trackingNumbers
     * @return array
     */
    public function handle($url, $trackingNumbers)
    {
        $url = !str_starts_with($url, '/') ? ('/' . $url) : $url;
        $trackingNumbers = is_array($trackingNumbers) ? $trackingNumbers : [$trackingNumbers];
        if (!empty($trackingNumbers)) {
            if ($url === '/tracking/get-tracking-info') {
                return [$url, ['trackingIds' => $trackingNumbers]];
            }
            $trackingNumbers = array_map(function ($trackingNumber) use ($url) {
                if ($url === '/tracking/push-tracking') {
                    $trackingNumber = (array) $trackingNumber;
                    $carrier = self::detectCarrier($trackingNumber['tracking_code'], $trackingNumber['shipping_carrier'], $trackingNumber['supplier_name'] ? strtolower(trim($trackingNumber['supplier_name'])) : null, $trackingNumber['tracking_url'] ?? null);
                    $data = [
                        'trackingId' => $trackingNumber['tracking_code']
                    ];
                    $data['courierName'] = null;
                    if ($carrier) {
                        $data['courierName'] = $carrier;
                    }
                    $additionInfo = [];
                    $additionInfo['clientId'] = '';
                    if (!empty($trackingNumber['tracking_url'])) {
                        $additionInfo['clientId'] = $trackingNumber['tracking_url'] . PHP_EOL;
                    }
                    if (data_get($trackingNumber, 'order.order_number')) {
                        $additionInfo['orderId'] = data_get($trackingNumber, 'order.order_number');
                    } else if (data_get($trackingNumber, 'order_id')) {
                        $additionInfo['orderId'] = data_get($trackingNumber, 'order_id');
                    }
                    $data['additionInfo'] = $additionInfo;
                    return $data;
                }
                return ['trackingId' => $trackingNumber];
            }, $trackingNumbers);
        }
        return [$url, $trackingNumbers];
    }
}
