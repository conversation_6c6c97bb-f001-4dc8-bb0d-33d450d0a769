<?php

namespace App\Services;

use App\Enums\OrderTypeEnum;
use App\Enums\SendMail\Template;
use App\Enums\StoreStatusEnum;
use App\Models\Campaign;
use App\Models\Order;
use App\Models\ReportedCampaign;
use App\Models\Store;
use App\Models\SystemConfig;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Tymon\JWTAuth\Facades\JWTAuth;
use Tymon\JWTAuth\Facades\JWTFactory;
use Modules\OrderService\Models\RegionOrders;

class MailService
{
    public static function makeAddressInvalidMailToken(Order|RegionOrders $order): string
    {
        $threshold = 60 * 60 * 24 * 2; // 2 days
        $time = isset($order->paid_at) ? Carbon::parse($order->paid_at)->timestamp : time();
        return self::makeToken([
            'sub' => 'address_invalid',
            'order_id' => $order->id,
        ], $time + $threshold);
    }

    public static function isAddressInvalidMailTokenValid(string $token, int $orderId): bool
    {
        try {
            $payload = JWTAuth::setToken($token)->getPayload();
            $sub = $payload->get('sub');
            $t_orderId = $payload->get('order_id');
            $expiration = $payload->get('exp');
            return $sub === 'address_invalid' && $orderId === $t_orderId && $expiration > time();
        } catch (\Exception $e) {
            return false;
        }
    }

    private static function makeToken(array $data, int $expiry): string
    {
        $factory = JWTFactory::customClaims([
            ...$data,
            'exp' => $expiry
        ]);
        $payload = $factory->make();
        $token = JWTAuth::encode($payload);
        return $token->get();
    }

    public static function generateBaseUrl(Order|RegionOrders $order): string
    {
        $store = Store::query()
            ->where('id', $order->store_id)
            ->whereIn('status', [StoreStatusEnum::ACTIVE, StoreStatusEnum::VERIFIED])
            ->first();

        $domain = SystemConfig::getConfig('preview_domain', 'senstores.com');
        if ($store && $store->domain) {
            $domain = $store->domain;
        }

        $urlInfo = parse_url($domain);
        if (!isset($urlInfo['scheme'])) {
            $domain = 'https://' . $domain;
        }

        return $domain;
    }

    public static function sendMailInvalidAddressNotification(Order|RegionOrders $order):void
    {
        if (!in_array($order->type, [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM])) {
            return;
        }
        $store = StoreService::getStoreInfo($order->store_id);
        sendEmail([
            'to' => $order->customer_email,
            'template' => Template::BUYER_ORDER_INVALID_ADDRESS_NOTIFICATION,
            'data' => [
                'subject'          => 'Confirm Address #' . $order->order_number,
                'name'             => $order->customer_name,
                'email'            => $order->customer_email,
                'order'            => [
                    'token'          => $order->access_token,
                    'order_number'   => $order->order_number,
                    'status_url'     => $order->status_url,
                    'address'        => $order->address,
                    'address_2'      => $order->address_2 ?? '',
                    'house_number'   => $order->house_number ?? '',
                    'mailbox_number' => $order->mailbox_number ?? '',
                    'city'           => $order->city,
                    'state'          => $order->state ?? '',
                    'postcode'       => $order->postcode,
                    'country'        => $order->country ?? '',
                ],
                'token'      => MailService::makeAddressInvalidMailToken($order),
                'store_name' => $store['name'],
                'base_url'   => MailService::generateBaseUrl($order),
                'store_info' => $store,
            ],
            'sendMailLog' => [
                'sellerId' => $order->seller_id ?? null,
                'storeId' => $order->store_id ?? null,
                'orderId' => $order->id ?? null
            ],
            'hash' => gen_unique_hash(),
        ]);
    }

    public static function getLabel($config, $key, $default): ?string
    {
        if (!empty($config)) {
            $single = Arr::get($config, $key);
            if ($single) {
                return Arr::get($single, 'label', $default) ?? $default;
            }
        }
        return $default;
    }

    public static function calculateWidth($config, $key): int
    {
        if (!empty($config)) {
            $single = Arr::get($config, $key);
            if ($single) {
                $width = Arr::get($single, 'width');
                if ($width) {
                    $percent = ($width / 12) * 100;
                    return (int)$percent;
                }
                return 100;
            }
        }
        return 100;
    }

    public static function sendNotificationBlockedCampaign(array $campaignIds, User $seller): void
    {
        $reports = ReportedCampaign::query()
            ->with(['campaign' => function ($query) use ($seller){
                $query->onSellerConnection($seller);
            }])
            ->whereIn('campaign_id', $campaignIds)
            ->where('seller_id', $seller->id)
            ->get();

        if ($reports->isEmpty()) {
            $campaigns = Campaign::query()
                ->onSellerConnection($seller)
                ->whereIn('id', $campaignIds)
                ->get();
            foreach ($campaigns as $campaign) {
                self::sendMailBlockByCampaign($campaign);
            }
            return;
        }

        foreach ($reports as $report) {
            self::sendMailBlockByReport($report);
        }
    }

    private static function sendMailBlockByCampaign(Campaign $campaign): void
    {
        $seller = User::query()->where('id', $campaign->seller_id)->whereNotNull('email')->first();
        if (!$seller) {
            return;
        }
        $dataSendMailLog = [
            'sellerId' => $seller->id
        ];
        $preview_domain = SystemConfig::getConfig('preview_domain', 'senstores.com');
        $reason = 'violate our policy';
        $config = [
            'to' => $seller->email,
            'template' => Template::SELLER_BLOCK_CAMPAIGN,
            'data' => [
                'subject' => 'We have blocked your campaign #' . $campaign->id . ' due to ' . $reason,
                'reason' => $reason,
                'name' => $seller->name,
                'email' => $seller->email,
                'original_work' => null,
                'campaign' => $campaign,
                'domain' => $campaign->domain ?? $preview_domain
            ],
            'sendMailLog' => $dataSendMailLog
        ];
        sendEmail($config);
    }

    private static function sendMailBlockByReport(ReportedCampaign $report): void
    {
        $seller = User::query()
            ->where('id', $report->campaign->seller_id)
            ->whereNotNull('email')
            ->first();
        if (!$seller) {
            return;
        }
        $dataSendMailLog = [
            'sellerId' => $seller->id
        ];
        $preview_domain = SystemConfig::getConfig('preview_domain', 'senstores.com');
        $additionalInfo = json_decode($report->additional_info, true);
        $reason = $report->reason ?? 'violate our policy';
        $config = [
            'to' => $seller->email,
            'template' => Template::SELLER_BLOCK_CAMPAIGN,
            'data' => [
                'subject' => 'We have blocked your campaign #' . $report->campaign_id . ' due to ' . $reason,
                'reason' => $reason,
                'name' => $seller->name,
                'email' => $seller->email,
                'original_work' => $additionalInfo['original_work'] ?? null,
                'domain' => $report->campaign?->domain ?? $preview_domain
            ],
            'sendMailLog' => $dataSendMailLog
        ];
        sendEmail($config);
    }
}
