<?php
namespace App\Jobs;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\AbandonedLogTypeEnum;
use App\Enums\NotificationChannelEnum;
use App\Enums\OrderStatus;
use App\Enums\TwilioCallbackStatus;
use App\Events\OrderAbandoned;
use App\Http\Controllers\SmsController;
use App\Models\AbandonedLog;
use App\Models\Currency;
use App\Models\NotificationSetting;
use App\Models\Order;
use App\Models\Product;
use App\Models\Store;
use App\Models\User;
use App\Services\SMSService;
use App\Services\StoreService;
use App\Traits\GetStoreDomain;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use TheIconic\NameParser\Name;
use TheIconic\NameParser\Parser;

define('CHANNELS', [
    AbandonedLogTypeEnum::EMAIL => NotificationChannelEnum::EMAIL,
    AbandonedLogTypeEnum::SMS => NotificationChannelEnum::SMS,
]);

class ProcessOrderAbandonedJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, GetStoreDomain;

    private AbandonedLog $abandonedLog;
    /**
     * Create a new job instance.
     */
    public function __construct($abandonedLog)
    {
        $this->abandonedLog = $abandonedLog;
    }

    /**
     * @return string
     */
    public function uniqueId(): string
    {
        return 'abandoned_' . $this->abandonedLog->id;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $abandonedLog = $this->abandonedLog;
        try {
            $order = $abandonedLog->order;
            if ($order === null) {
                $abandonedLog->cancel('Order invalid');
                return;
            }

            if ($order->status !== OrderStatus::PENDING) {
                $abandonedLog->cancel('Order status is not pending');
                return;
            }

            OrderAbandoned::dispatch($order);

            if (!empty($order->customer)) {
                if (($abandonedLog->type === AbandonedLogTypeEnum::SMS) && $order->customer->isSmsSubscribed() === false) {
                    $abandonedLog->cancel('Unsubscribe SMS');
                    return;
                }

                if (($abandonedLog->type === AbandonedLogTypeEnum::EMAIL) && $order->customer->isEmailSubscribed() === false) {
                    $abandonedLog->cancel('Unsubscribe EMAIL');
                    return;
                }
            }

            $settings = NotificationSetting::query()
                ->where('type', $abandonedLog->notification_key)
                ->whereIn('store_id', [$abandonedLog->store_id, Store::SENPRINTS_STORE_ID])
                ->get();

            if ($settings->contains('store_id', '!==', Store::SENPRINTS_STORE_ID)) {
                $settings = $settings->filter(
                    fn($notifySetting) => $notifySetting->store_id !== Store::SENPRINTS_STORE_ID
                );
            }

            if (empty($settings)) {
                $abandonedLog->cancel('Setting empty');
                return;
            }

            $setting = $settings->first();

            if ($abandonedLog->type === AbandonedLogTypeEnum::SMS) {
                if (empty($order->customer_phone)) {
                    $abandonedLog->cancel('No phone');
                    return;
                }
                if ((int)$setting->send_after <= 0) {
                    $abandonedLog->cancel('Seller disabled send sms');
                    return;
                }
                $this->recoverBySms($setting, $abandonedLog, $order);
            }

            if ($abandonedLog->type === AbandonedLogTypeEnum::EMAIL) {
                if (empty($order->customer_email)) {
                    $abandonedLog->cancel('No Email');
                    return;
                }
                if ((int)$setting->send_after <= 0) {
                    $abandonedLog->cancel('Seller disabled send email');
                    return;
                }
                $this->recoverByEmail($setting, $abandonedLog, $order);
            }
            return;
        } catch (\Exception $e) {
            logToDiscord($e->getMessage(), 'abandoned.error');
            return;
        }
    }

    /**
     * @param NotificationSetting $setting
     * @param AbandonedLog $abandonedLog
     * @param Order $order
     * @return bool
     */
    public function recoverBySms(NotificationSetting $setting, AbandonedLog $abandonedLog, Order $order): bool
    {
        // check available credits
        $pendingSmsJobs = $order->seller->pending_sms_jobs;
        $sellerCredits = $order->seller->sms_credit;
        $availableCredits = $sellerCredits - $pendingSmsJobs;

        if ($availableCredits <= 0) {
            $abandonedLog->cancel('SMS credit is not enough');
            return false;
        }

        // get promotions
        $promo = $setting->getStorePromotions();

        if (is_null($promo)) {
            $coupon = '';
            $promotionTitle = '';
        } else {
            $coupon = $promo['coupon'] ?? '';
            $promotionTitle = $promo['title'] ?? '';
        }

        // get cart url
        $cartKey = $abandonedLog->id;
        $cartUrl = $this->getCartUrl($order, $setting, $cartKey, $coupon);

        // get short url
        $result = SmsController::generateShortUrl($cartUrl);

        if ($result->isError()) {
            $abandonedLog->setDescription(!empty($result->message) ? $result->message : 'Gen short url error');
            return false;
        }

        // get store info
        $storeInfo = StoreService::getStoreInfo($order->store_id);

        if (is_null($storeInfo)) {
            return false;
        }

        // parse name
        if (empty($order->customer_name)) {
            $abandonedLog->setDescription('Parse name failed (0)');
            return false;
        }

        $name = self::parseName($order->customer_name);

        if ($name === null) {
            $abandonedLog->setDescription('Parse name failed (1)');
            return false;
        }

        $firstName = $name->getFirstname();
        $lastName = $name->getLastname();

        $default_setting = NotificationSetting::query()
            ->where('type', $abandonedLog->notification_key)
            ->where('store_id', Store::SENPRINTS_STORE_ID)
            ->first();
        if ($default_setting === null) {
            $abandonedLog->setDescription('Default setting not found');
            return false;
        }
        $message = "{{STORE_NAME}}\n{$setting->content}";

        // replace vars
        $shortUrl = preg_replace('/^https?:\/\//', '', $result->shortUrl);
        $message = str_replace(array('{{URL}}', '{{STORE_NAME}}', '{{FIRST_NAME}}', '{{LAST_NAME}}', '{{PROMO_TITLE}}', '{{COUPON}}'), array($shortUrl, $order->store_name, $firstName, $lastName, $promotionTitle, $coupon), $message);
        if (strlen($message) > 140) {
            $message = substr($message, 0, 140);
        }
        $phoneNumber = $order->formatted_phone_number;

        if (empty($phoneNumber)) {
            $abandonedLog->cancel('Phone number is empty');
            return false;
        }

        if (is_array($phoneNumber)) {
            $abandonedLog->cancel($phoneNumber['message']);
            return false;
        }
        $totalSms = AbandonedLog::query()->where('reference_id', $order->id)
            ->where('type', AbandonedLogTypeEnum::SMS)
            ->whereIn('status', [AbandonedLogStatusEnum::SCHEDULED, AbandonedLogStatusEnum::PENDING, AbandonedLogStatusEnum::SENT, AbandonedLogStatusEnum::FAILED])
            ->count();
        $status = TwilioCallbackStatus::UNDELIVERED;
        // make sure the message is not sent more than 3 times
        if ($totalSms < 4) {
            $smsId = Str::uuid();
            $sendBy = SMSService::getDriver($order->formatted_phone_number);
            if ($sendBy === 'whatsapp' && !str_contains($message, 'Reply STOP to')) {
                $message .= "\nReply STOP to opt-out";
            }
            $sms = new SMSService($smsId, $message, $order->formatted_phone_number, $sendBy, null, $cartKey, $order->id, $order->seller_id);
            [$status, $twilioMsg] = $sms->sendNow();
            if ($status === 'duplicate' || $status === 'error') {
                graylogInfo("Duplicate hash!", [
                    'category' => 'abandoned_error',
                    'text' => json_encode($twilioMsg),
                    'status' => $status,
                    'action' => 'resend_sms',
                ]);
                $status = TwilioCallbackStatus::FAILED;
            }
        }

        switch ($status) {
            case TwilioCallbackStatus::UNSUBSCRIBED:
                if (!empty($order->customer)) {
                    $order->customer->unsubscribeSms();
                }
                $abandonedStatus = AbandonedLogStatusEnum::CANCELLED;
                break;
            case TwilioCallbackStatus::UNDELIVERED:
            case TwilioCallbackStatus::FAILED:
                $abandonedStatus = AbandonedLogStatusEnum::FAILED;
                break;
            default:
                $abandonedStatus = AbandonedLogStatusEnum::PENDING;
        }

        // save logs
        $abandonedLog->description = $message;
        $abandonedLog->status = $abandonedStatus;
        $abandonedLog->save();
        return true;
    }

    /**
     * @param NotificationSetting $setting
     * @param AbandonedLog $abandonedLog
     * @param Order $order
     * @return bool
     */
    public function recoverByEmail(NotificationSetting $setting, AbandonedLog $abandonedLog, Order $order): bool
    {
        // get promotions
        $promo = $setting->getStorePromotions();

        if (is_null($promo)) {
            $coupon = '';
            $promotionTitle = '';
        } else {
            $coupon = $promo['coupon'] ?? '';
            $promotionTitle = $promo['title'] ?? '';
        }

        // get cart url
        $cartKey = generateUUID();
        $cartUrl = $this->getCartUrl($order, $setting, $cartKey, $coupon);

        // get store info
        $storeInfo = StoreService::getStoreInfo($order->store_id);

        if (is_null($storeInfo)) {
            return false;
        }

        $currency = Currency::query()->firstWhere('code', $order->currency_code);

        if (is_null($currency)) {
            $currency = Currency::query()->firstWhere('code', 'USD');
        }

        // parse name
        if (!empty($order->customer_name)) {
            $name = self::parseName($order->customer_name);

            if ($name === null) {
                $abandonedLog->setDescription('Parse name failed (2)');
                return false;
            }

            $firstName = $name->getFirstname();
            $lastName = $name->getLastname();
        } else {
            $firstName = '';
            $lastName = '';
        }

        // add store name to head
        $message = (string)$setting->content;

        if (str_contains($message, 'href="{{URL}}"')) {
            $message = str_replace('{{URL}}', $cartUrl, $message);
        } else {
            $message = str_replace('{{URL}}', '<a href="' . $cartUrl . '" target="_blank">' . $cartUrl . '</a>', $message);
        }
        $message = str_replace(array('{{STORE_NAME}}', '{{FIRST_NAME}}', '{{LAST_NAME}}', '{{PROMO_TITLE}}', '{{COUPON}}'), array($order->store_name, $firstName, $lastName, $promotionTitle, $coupon), $message);
        $order->load('products');
        $sellers = User::query()
            ->selectRaw('id,name,status,custom_payment,tags,sharding_status,db_connection')
            ->whereIn('id', $order->products->pluck('seller_id')->unique()->values()->toArray())
            ->get();
        $order->products->map(function ($orderProduct) use ($sellers) {
            $seller = $sellers->firstWhere('id', $orderProduct->seller_id);
            $product = Product::query()->onSellerConnection($seller)->find($orderProduct->product_id);
            $orderProduct->setRelation('product', $product);
            // check the product has only color white, if has color white, remove color from options
            if ($orderProduct->product && Str::isJson($orderProduct->product->options) && Str::isJson($orderProduct->options)) {
                $productOptions = json_decode($orderProduct->product->options, true, 512, JSON_THROW_ON_ERROR);
                $options = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
                if (isset($options['color'], $productOptions['color']) && count($productOptions['color']) === 1 && data_get($productOptions, 'color.0') === 'white') {
                    unset($options['color']);
                }
                if (isset($options['size'], $productOptions['size']) && count($productOptions['size']) === 1) {
                    unset($options['size']);
                }
                $orderProduct->options = json_encode($options, JSON_THROW_ON_ERROR);
            }
            return $orderProduct;
        });
        $dataSendMailLog = [
            'sellerId' => $order->seller_id ?? null,
            'storeId' => $storeInfo->id ?? null,
            'orderId' => $order->id ?? null
        ];

        $config = [
            'to' => $order->customer_email,
            'template' => 'buyer.abandoned_checkout',
            'data' => [
                'subject' => $setting->subject,
                'name' => $order->customer_name,
                'email' => $order->customer_email,
                'message' => $message,
                'products' => $order->products,
                'order_number' => $order->order_number,
                'total_amount' => $order->total_amount,
                'total_shipping_amount' => $order->total_shipping_amount,
                'total_discount' => $order->total_discount,
                'cart_url' => $cartUrl,
                'promotion_title' => $promotionTitle,
                'coupon' => $coupon,
                'store_name' => $order->store_name,
                'base_url' => config('senprints.base_url_seller'),
                'store_info' => $storeInfo,
                'currency' => $currency
            ],
            'sendMailLog' => $dataSendMailLog,
            'abandonedLogId' => $abandonedLog->id, //https://senprints.atlassian.net/browse/SDV-4885
        ];

        if (!sendEmail($config)) {
            return false;
        }

        $abandonedLog->status = AbandonedLogStatusEnum::SUCCESS;
        $abandonedLog->save();

        return true;
    }

    /**
     * @param Order $order
     * @param NotificationSetting|null $setting
     * @param string $cartKey
     * @param string $coupon
     * @return string
     */
    private function getCartUrl(Order $order, NotificationSetting $setting = null, string $cartKey = '', string $coupon = ''): string
    {
        if (!$setting) {
            return '';
        }

        $domain = self::getDomainByStoreId($order->store_id);
        $accessToken = $order->access_token;
        $baseUrl = 'https://' . $domain;
        $checkOutUrl = $baseUrl . '/checkout/' . $accessToken;
        $utcQuery = "utm_source={$setting->type}&utm_medium={$setting->channel}&utm_campaign=abandoned_{$setting->channel}";
        $couponQuery = (empty($coupon) ? '' : '&discount=' . rawurlencode($coupon));
        $cartKeyQuery = (empty($cartKey) ? '' : '&cart_key=' . $cartKey);
        return $checkOutUrl . "?" . $utcQuery . $couponQuery . $cartKeyQuery;
    }

    private static function parseName(string $name): ?Name
    {
        try {
            return (new Parser())->parse($name);
        } catch (\Throwable $e) {
            return null;
        }
    }
}
