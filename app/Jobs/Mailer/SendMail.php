<?php

namespace App\Jobs\Mailer;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\CacheKeys;
use App\Enums\CacheTime;
use App\Enums\EnvironmentEnum;
use App\Enums\SendMail\LogStatus as SendMailLogStatus;
use App\Enums\SendMail\Template;
use App\Mail\BuyerAbandonedCheckout;
use App\Mail\BuyerConfirmedCancelOrder;
use App\Mail\BuyerCrossShippingNotification;
use App\Mail\BuyerFulfillmentNotification;
use App\Mail\BuyerOrderCancelled;
use App\Mail\BuyerOrderConfirmation;
use App\Mail\BuyerOrderInvalidAddressNotification;
use App\Mail\BuyerOrderLateShippingNotification;
use App\Mail\BuyerOrderShipLateNotification;
use App\Mail\BuyerOrderShippingNotification;
use App\Mail\BuyerOrderUpdate;
use App\Mail\BuyerRequestCancelOrderConfirmation;
use App\Mail\BuyerResumeOrder;
use App\Mail\DefaultCustomTemplate;
use App\Mail\Disable2faForSocial;
use App\Mail\Fulfill3DaysNotify;
use App\Mail\NotifySupplierAboutNewOrder;
use App\Mail\ProductReviewRequest;
use App\Mail\ResetPassword;
use App\Mail\SellerBlockCampaign;
use App\Mail\SellerBlockCampaignTradeMark;
use App\Mail\SellerBlockCauseTrademarkViolation;
use App\Mail\SellerExpireDomain;
use App\Mail\SellerInactiveCampaign;
use App\Mail\SellerInactiveLastLogin;
use App\Mail\SellerInviteMember;
use App\Mail\SellerLawyerAddressNotification;
use App\Mail\SellerOrderNotification;
use App\Mail\SellerOrderNotificationNew;
use App\Mail\SellerPaymentAccountConfirmation;
use App\Mail\SellerPayoutConfirmation;
use App\Mail\SellerTrademarkWarningTime;
use App\Mail\SellerVerifyEmail;
use App\Mail\SendSellerBalanceUpdateNotification;
use App\Mail\StoreContactCustomer;
use App\Mail\StoreContactSeller;
use App\Mail\SupplierCreatePassword;
use App\Mail\TakedownTrademark24h;
use App\Mail\WelcomeEmail;
use App\Models\AbandonedLog;
use App\Models\SendMailLog;
use App\Traits\StripePaymentDescriptors;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\MailManager;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Mail;
use Modules\Marketing\Entities\SubMailerConfig;
use Modules\Marketing\Enums\EmailSettingsStatusEnum;
use Modules\Marketing\Enums\EmailSettingsTypeEnum;
use Modules\Marketing\Events\SendMailEvent;
use Modules\Marketing\Models\EmailSettings;
use Modules\Marketing\Supports\EmailHandler;
use Modules\Marketing\Traits\MailConfigTrait;
use Throwable;

class SendMail implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;
    use StripePaymentDescriptors;
    use MailConfigTrait;

    protected string $from;
    protected string $to;
    protected string $template;
    protected $data;
    protected string $backup_email;
    protected string $support_email;
    protected string $queueName = 'system-email';
    protected bool $is_custom_store;

    protected $sendMailLogId = null;
    protected $sendMailLogCacheKey = null;
    protected $isResend = false;
    protected $testSendFail = false;
    protected $dmca_notification_id = null;
    protected $hash;
    protected $abandonedLogId = null;

    /**
     * Create a new job instance.
     *
     * @param $from
     * @param $to
     * @param $backup_email
     * @param $support_email
     * @param $template
     * @param bool $is_custom_store
     * @param null $data
     * @param null $sendMailLogId
     * @param bool $isResend
     * @param null $dmca_notification_id
     * @param null $hash
     * @param null $abandonedLogId
     */
    public function __construct($from, $to, $backup_email, $support_email, $template, $is_custom_store = false, $data = null, $sendMailLogId = null, $isResend = false, $dmca_notification_id = null, $hash = null, $abandonedLogId = null)
    {
        $this->onQueue($this->queueName);
        try {
            $this->from = $from;
            $this->to = $to;
            $this->backup_email = $backup_email;
            $this->support_email = $support_email;
            $this->template = strtolower($template);
            $this->is_custom_store = $is_custom_store;
            $this->sendMailLogId = $sendMailLogId;
            $this->sendMailLogCacheKey = !empty($sendMailLogId) ? getSendMailCacheKeyById($sendMailLogId) : null;
            $this->isResend = $isResend;
            $this->testSendFail = $data['test_send_fail'] ?? false;
            $this->dmca_notification_id = $dmca_notification_id;
            $this->hash = $hash;
            $this->abandonedLogId = $abandonedLogId;

            $data = json_decode(json_encode($data), true);
            if (!is_null($data)) {
                if (!empty($this->sendMailLogCacheKey) && !$isResend) {
                    $cacheData = [
                        'from' => $this->from ?? null,
                        'to' => $this->to ?? null,
                        'backup_email' => $this->backup_email ?? null,
                        'support_email' => $this->support_email ?? null,
                        'template' => $this->template ?? null,
                        'is_custom_store' => $this->is_custom_store ?? false,
                        'data' => $data ?? null,
                        'sendMailLogId' => $sendMailLogId ?? null
                    ];
                    cache()->tags([CacheKeys::SENDMAIL_FAILED])->put($this->sendMailLogCacheKey, json_encode($cacheData), CacheTime::CACHE_1Y);
                }
                $data = self::processData($data);
                $this->data = $data;
            }
        } catch (Exception $ex) {
            graylogError("Mailer/SendMail Job construct: \n\r {$ex->getMessage()}", [
                'category' => 'job_send_mail_failed',
                'user_type' => 'system',
                'user_id' => null,
                'action' => 'send_mail'
            ]);
        }
    }

    /**
     * @param mixed $data
     *
     * @return mixed
     */
    public static function processData($data)
    {
        if (!isset($data['unsubscribe_key'])) {
            $data['unsubscribe_key'] = '';
        }

        if (isset($data['store_info'])) {
            $store = $data['store_info'];
            $data['base_url'] = 'https://' . $store['domain'];
            $logoUrl = preg_replace('#^/#', '', $store['logo_url']);

            $data['logo_url'] = sendMailImgUrl($logoUrl, 'email_logo');
            $data['store_name'] = $store['name'];

            // social parsing
            if (isset($data['store_info']['social_accounts']) && strlen($data['store_info']['social_accounts']) > 0) {
                $accounts = json_decode($data['store_info']['social_accounts'], true);
                $data['store_info']['social_accounts'] = $accounts;
            }
        }

        return $data;
    }

    private static function formatPrice($value, $currency): string
    {
        return formatCurrency(convertCurrency($value, $currency['rate']), $currency['code'], $currency['locale'], true);
    }

    private static function processOrder(&$data): void
    {
        // process order money
        $currency = Arr::get($data, 'currency');
        $order = Arr::get($data, 'order');

        if ($order) {
            $order['total_product_amount'] = self::formatPrice(Arr::get($order, 'total_product_amount', 0), $currency);
            $order['total_shipping_amount'] = self::formatPrice(
                Arr::get($order, 'total_shipping_amount', 0),
                $currency
            );
            $order['tip_amount'] = self::formatPrice(Arr::get($order, 'tip_amount', 0), $currency);
            $order['total_discount'] = self::formatPrice(Arr::get($order, 'total_discount', 0), $currency);
            $order['total_amount'] = self::formatPrice(Arr::get($order, 'total_amount', 0), $currency);
            $order['total_tax_amount'] = self::formatPrice(Arr::get($order, 'total_tax_amount', 0), $currency);
            $order['payment_discount'] = self::formatPrice(Arr::get($order, 'payment_discount', 0), $currency);
            $order['insurance_fee'] = self::formatPrice(Arr::get($order, 'insurance_fee', 0), $currency);

            if (self::isStripePaymentMethod(Arr::get($order, 'payment_method'))) {
                try {
                    $statementDescriptor = self::getFullStatementDescriptor(
                        Arr::get($order, 'payment_gateway_id'),
                        Arr::get($order, 'order_number')
                    );

                    if ($statementDescriptor) {
                        $order['statement_descriptor'] = $statementDescriptor;
                    }
                } catch (\Exception $e) {
                    graylogError('Create statement descriptor failed', [
                        'category' => 'send_mail',
                        'error' => $e->getMessage()
                    ]);
                }
            }
        }

        $data['order'] = $order;
    }

    private static function processOrderPrice(&$data): void
    {
        // process product price
        $currency = Arr::get($data, 'currency');
        $products = Arr::get($data, 'order.products', []);
        foreach ($products as $index => $product) {
            $products[$index]['raw_price'] = $product['price'];
            $products[$index]['price'] = self::formatPrice($product['price'], $currency);
        }
        $data['order']['products'] = $products;
    }

    private static function processProductPrice(&$data): void
    {
        // process product price
        $currency = Arr::get($data, 'currency');
        $products = Arr::get($data, 'products', []);
        foreach ($products as $index => $product) {
            $products[$index]['raw_price'] = $product['price'];
            $products[$index]['price'] = self::formatPrice($product['price'], $currency);
        }
        $data['products'] = $products;
    }

    public static function processOrderFulfillmentPrice(&$data): void
    {
        $currency = Arr::get($data, 'currency');
        $products = Arr::get($data, 'fulfillments', [
            'unfulfilled' => [],
            'fulfilled' => []
        ]);
        $fulfilleds = $products['fulfilled'];
        $unfulfilled = $products['unfulfilled'];
        foreach ($fulfilleds as $index => $fulfilled) {
            foreach ($fulfilled['items'] as $itemIndex => $item) {
                if(is_string($item['price'])) { // already formatted
                    continue;
                }
                $fulfilleds[$index]['items'][$itemIndex]['price'] = self::formatPrice($item['price'], $currency);
            }
        }
        foreach ($unfulfilled as $index => $unfulfilledItem) {
            if(is_string($unfulfilledItem['price'])) { // already formatted
                continue;
            }
            $unfulfilled[$index]['price'] = self::formatPrice($unfulfilledItem['price'], $currency);
        }

        $data['fulfillments'] = [
            'unfulfilled' => $products['unfulfilled'],
            'fulfilled' => $fulfilleds
        ];
    }

    private static function processSubject(&$data): void
    {
        // process subject
        $subject = Arr::get($data, 'subject');
        $name = Arr::get($data, 'name');
        $name = trim($name);

        if ($name === '') {
            $firstName = 'Hi';
        } else {
            $firstName = $name;
        }

        $cols = explode(" ", $name);

        if (count($cols) >= 2) {
            $firstName = $cols[0];
        }
        // for new update
        $subject = str_replace('{{FIRST_NAME}}', $firstName, $subject);
        $subject = str_replace('{{NAME}}', $name, $subject);

        // for old template
        $subject = str_replace('{name}', $firstName, $subject);
        $subject = str_replace('{first_name}', $firstName, $subject);
        $data['subject'] = $subject;
    }

    public static function productReviewRequestMapData($data): array
    {
        $store = $data['store_info'];

        $templateData = [
            'order_id' => $data['order_id'],
            'logo_url' => sendMailImgUrl(preg_replace('#^/#', '', $store['logo_url']), 'email_logo'),
            'store_name' => $store['name'],
            'store_domain' => $data['review_url'],
            'subject' => $data['subject'],
            'customer_name' => !empty($data['customer_name']) ? $data['customer_name'] : '',
            'store_info' => [
                'address' => $store['address'],
                'phone' => $store['phone'],
                'social_accounts' => $store['social_accounts']
            ],
            'has_review_coupon' => $data['has_review_coupon'],
            'products' => $data['products'],
            'related_products' => $data['related_products'],
            'base_url' => 'https://' . $data['review_url'] ?? null,
            'logHashId' => $data['logHashId'] ?? null
        ];

        $templateData['review_url'] = 'https://' . $data['review_url'] . '/order/status/' . $data['access_token'] . '#review';

        return $templateData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $template = null;
            self::processSubject($this->data);
            $isProduction = app()->isProduction();
            $supportAddress = $this->support_email;
            $backupAddress = $this->backup_email;
            $fromEmail = $this->from;
            $fromName = $this->data['store_info']['name'] ?? 'SenPrints';
            $toEmail = $this->to;
            $toName = $this->data['name'] ?? '';
            $this->data['logHashId'] = $this->sendMailLogId;

            $dmcaNotificationId = $this->dmca_notification_id ?? null;
            $abandonedLogId = $this->abandonedLogId;
            switch ($this->template) {
                case 'default_custom_template':
                    $template = new DefaultCustomTemplate($this->data);
                    break;
                case 'welcome':
                    $template = new WelcomeEmail($this->data);
                    break;
                case 'store.contact_customer':
                    $template = new StoreContactCustomer($this->data);
                    break;
                case 'store.contact_seller':
                    $template = new StoreContactSeller($this->data);
                    $toEmail = $supportAddress;
                    if ($isProduction) {
                        if (isset($this->data['store_info']['cc_email'])) {
                            $sellerEmail = trim($this->data['store_info']['cc_email']);
                        } else {
                            $sellerEmail = $this->data['seller_email'] ?? $this->data['store_info']['support_email'];
                        }
                        $template->cc($sellerEmail);
                    }
                    break;
                case 'auth.reset_password':
                    $template = new ResetPassword($this->data);
                    break;
                case 'seller.order_notification':
                    $template = new SellerOrderNotification($this->data);
                    break;
                case 'seller.lawyer_address_notification':
                    $template = new SellerLawyerAddressNotification($this->data);
                    break;
                case 'seller.new_order':
                    $template = new SellerOrderNotificationNew($this->data);
                    break;
                case 'seller.payment_account_confirmation':
                    $template = new SellerPaymentAccountConfirmation($this->data);
                    break;
                case 'seller.payout_confirmation':
                    $template = new SellerPayoutConfirmation($this->data);
                    break;
                case 'seller.balance-updated-notification':
                    $template = new SendSellerBalanceUpdateNotification($this->data);
                    break;
                case 'seller.inactive_campaign':
                    $template = new SellerInactiveCampaign($this->data);
                    break;
                case 'seller.inactive_last_login':
                    $template = new SellerInactiveLastLogin($this->data);
                    break;
                case 'seller.expire_domain':
                    $template = new SellerExpireDomain($this->data);
                    break;
                case 'buyer.order_confirmation':
                    self::processOrderPrice($this->data);
                    self::processOrder($this->data);
                    $template = new BuyerOrderConfirmation($this->data);
                    break;
                case 'buyer.fulfillment_notification':
                    self::processOrderFulfillmentPrice($this->data);
                    $template = new BuyerFulfillmentNotification($this->data);
                    break;
                case 'buyer.cross_shipping_notification':
                    $template = new BuyerCrossShippingNotification($this->data);
                    break;
                case 'buyer.order_update':
                    $template = new BuyerOrderUpdate($this->data);
                    break;
                case 'buyer.abandoned_checkout':
                    self::processProductPrice($this->data);
                    $template = new BuyerAbandonedCheckout($this->data);
                    break;
                case 'seller.invite_member_email':
                    $this->data['invite_url'] = config('senprints.base_url_seller') . 'team/invitations';
                    $template = new SellerInviteMember($this->data);
                    break;
                case 'buyer.product_review_request':
                    $this->data = self::productReviewRequestMapData($this->data);
                    $template = new ProductReviewRequest($this->data);
                    $toName = $this->data['customer_name'];
                    break;
                case 'buyer.request_cancel_order_confirmation':
                    $template = new BuyerRequestCancelOrderConfirmation($this->data);
                    break;
                case 'buyer.confirmed_cancel_order':
                    $template = new BuyerConfirmedCancelOrder($this->data);
                    break;
                case 'buyer.resume_order':
                    $template = new BuyerResumeOrder($this->data);
                    break;
                case Template::BUYER_ORDER_INVALID_ADDRESS_NOTIFICATION:
                    $template = new BuyerOrderInvalidAddressNotification($this->data);
                    break;
                case 'buyer.order_cancelled':
                    $template = new BuyerOrderCancelled($this->data);
                    if ($isProduction) {
                        if (!empty($this->data['store_info']['cc_email'])) {
                            $sellerEmail = trim($this->data['store_info']['cc_email']);
                        } else {
                            $sellerEmail = $this->data['seller_email'] ?? $this->data['store_info']['support_email'];
                        }
                        $template->cc($sellerEmail);
                    }
                    break;
                case 'seller.block-campaign-trademark-notification':
                    $template = new SellerBlockCampaignTradeMark($this->data);
                    break;
                case Template::SELLER_BLOCK_CAMPAIGN:
                    $template = new SellerBlockCampaign($this->data);
                    break;
                case 'seller.takedown-trademark-24h-notification':
                    $template = new TakedownTrademark24h($this->data);
                    break;
                case 'seller.dmca-violation-time-warning':
                    $template = new SellerTrademarkWarningTime($this->data);
                    break;
                case 'seller.seller-blocked-cause-trademark-violation':
                    $template = new SellerBlockCauseTrademarkViolation($this->data);
                    break;
                case 'supplier.create_password':
                    $template = new SupplierCreatePassword($this->data);
                    break;
                case 'supplier.fulfill_3_days_notify':
                    $template = new Fulfill3DaysNotify($this->data);
                    break;
                case Template::SUPPLIER_NOTIFY_NEW_ORDER:
                    $template = new NotifySupplierAboutNewOrder($this->data);
                    break;
                case 'auth.disable_2fa_for_social_account':
                    $template = new Disable2faForSocial($this->data);
                    break;
                case Template::AUTH_VERIFY_EMAIL:
                    $template = new SellerVerifyEmail($this->data);
                    break;
                case 'buyer.order_shipping_notification':
                    $template = new BuyerOrderShippingNotification($this->data);
                    break;
                case 'buyer.order_ship_late_notification':
                    $template = new BuyerOrderShipLateNotification($this->data);
                    break;
                case 'buyer.order_late_shipping_notification':
                    $template = new BuyerOrderLateShippingNotification($this->data);
                    break;
            }
            if ($template) {
                $template->from($fromEmail, $fromName);
                $template->to($toEmail, $toName);
                $template->bcc($backupAddress);
                // Do not reply to support email for buyer order confirmation
                if ($this->template !== Template::BUYER_ORDER_CONFIRMATION) {
                    $template->replyTo($supportAddress, $fromName);
                }
                $objectTemplate = $template;
                try {
                    $shouldSendEmail = false;
                    $renderTemplate = $template->render();
                    $sendMailLog = null;
                    if (app()->environment(EnvironmentEnum::LOCAL)) {
                        $shouldSendEmail = true;
                    } else if (!empty($this->sendMailLogId)) {
                        $sendMailLog = SendMailLog::query()->where('id', $this->sendMailLogId)->first();
                        if ($renderTemplate === null) {
                            graylogInfo("Mailer/SendMail Job: Render template null", [
                                'category' => 'job_send_mail_failed',
                                'action' => 'renderTemplate',
                            ]);
                            $update = [
                                'logs' => 'Render template null',
                                'status' => SendMailLogStatus::CANCELLED,
                            ];
                            SendMailLog::query()->where('id', $this->sendMailLogId)->update($update);
                        } else {
                            if (!is_null($sendMailLog)) {
                                !empty($this->hash) ? $hash = $this->hash : $hash = $this->createHashMailLog($sendMailLog, $renderTemplate);
                                if (SendMailLog::query()->where('hash', $hash)->exists()) {
                                    if ($sendMailLog->delete()) {
                                        graylogInfo("Mailer/SendMail Job: Delete send mail log because hash is exist", [
                                            'category' => 'job_send_mail_failed',
                                            'seller_id' => (string) $sendMailLog->seller_id,
                                            'deletedSendMailLogId' => (string) $this->sendMailLogId,
                                            'duplicate' => true,
                                            'duplicateHash' => $hash,
                                            'action' => 'deleteSendMailLog'
                                        ]);
                                        if (!empty($abandonedLogId)) {
                                            $abandoned = AbandonedLog::query()->where('id', $abandonedLogId)->first();
                                            if ($abandoned) {
                                                $abandoned->status = AbandonedLogStatusEnum::FAILED;
                                                $abandoned->description = 'Duplicate send mail';
                                                $abandoned->save();
                                            }
                                        }
                                    } else {
                                        graylogInfo("Mailer/SendMail Job: Delete send mail failed", [
                                            'category' => 'job_send_mail_failed',
                                            'seller_id' => $sendMailLog->seller_id,
                                            'deletedSendMailLogId' => (string) $this->sendMailLogId,
                                            'duplicate' => 1,
                                            'duplicateHash' => $hash,
                                            'action' => 'deleteSendMailLogFailed'
                                        ]);
                                    }
                                } else {
                                    $update = [
                                        'subject' => $this->data['subject'] ?? null,
                                        'content' => $renderTemplate,
                                        'dmca_notification_id' => $dmcaNotificationId,
                                        'hash' => $hash,
                                    ];
                                    SendMailLog::query()->where('id', $this->sendMailLogId)->update($update);
                                    $shouldSendEmail = true;
                                }
                            }
                        }
                        // Test send mail fail
                        if (app()->environment(EnvironmentEnum::DEVELOPMENT) && !$this->isResend && $this->testSendFail) {
                            throw new \RuntimeException('Test send mail fail');
                        }
                    }
                    // Send mail
                    if ($shouldSendEmail) {
                        try {
                            $driver = null;
                            if (str_starts_with($this->template, 'buyer.')) {
                                $emailSystemConfig = EmailSettings::query()
                                    ->where([
                                        'type' => EmailSettingsTypeEnum::EMAIL_SYSTEM,
                                        'seller_id' => $sendMailLog?->seller_id,
                                        'is_deleted' => EmailSettingsStatusEnum::IS_NOT_DELETED,
                                        'status' => EmailSettingsStatusEnum::VALID,
                                        'state' => EmailSettingsStatusEnum::ACTIVE,
                                    ])
                                    ->whereRelation('stores', 'store.id', '=', $this->data['store_info']['id'])
                                    ->orderByDesc('id')
                                    ->first();
                                if ($emailSystemConfig) {
                                    $subMailerConfig = new SubMailerConfig();
                                    $subMailerConfig->setSenderEmail($this->data['store_info']['email']);
                                    $subMailerConfig->setSenderName($fromName);
                                    $driver = $this->changeMailConfig($emailSystemConfig->seller_id, $emailSystemConfig->id, $subMailerConfig);
                                    $objectTemplate->from($this->data['store_info']['email'], $fromName);
                                }
                            }
                            Mail::mailer($driver)->send($objectTemplate);
                            if (!app()->environment(EnvironmentEnum::LOCAL)) {
                                $update = [
                                    'status' => SendMailLogStatus::SENT,
                                    'sent_at' => now(),
                                ];
                                SendMailLog::query()->where('id', $this->sendMailLogId)->update($update);
                                return;
                            }
                            SendMailLog::query()->where('id', $this->sendMailLogId)->update(['status' => SendMailLogStatus::FAILED]);
                            cache()->tags([CacheKeys::SENDMAIL_FAILED])->delete($this->sendMailLogCacheKey);
                        } catch (\Exception $e) {
                            if (!empty($this->sendMailLogId)) {
                                SendMailLog::query()->where('id', $this->sendMailLogId)->update(['status' => SendMailLogStatus::FAILED, 'logs' => $e->getMessage()]);
                            }
                            cache()->tags([CacheKeys::SENDMAIL_FAILED])->delete($this->sendMailLogCacheKey);
                            graylogError("Mailer/SendMail Can not send mail. \n\r {$e->getMessage()}", [
                                'category' => 'job_send_mail_failed',
                                'user_type' => 'system',
                                'sendMailLogId' => (string) $this->sendMailLogId,
                                'action' => 'sendEmail'
                            ]);
                        }
                    } else {
                        graylogInfo("Mailer/SendMail Job: shouldSendMail false", [
                            'category' => 'job_send_mail_failed',
                            'shouldSendMail' => false,
                            'sendMailLogId' => (string) $this->sendMailLogId,
                            'action' => 'sendEmail',
                        ]);
                    }
                } catch (\Exception $e) {
                    if (!empty($this->sendMailLogId)) {
                        SendMailLog::query()->where('id', $this->sendMailLogId)->update(['status' => SendMailLogStatus::FAILED, 'logs' => $e->getMessage()]);
                    }
                    graylogError("Mailer/SendMail Job: \n\r {$e->getMessage()}", [
                        'category' => 'job_send_mail_failed',
                        'user_type' => 'system',
                        'sendMailLogId' => (string) $this->sendMailLogId,
                        'action' => 'renderTemplate'
                    ]);
                }
            }
        } catch (Throwable $ex) {
            $this->fail($ex);
        }
    }

    public function failed(Throwable $exception = null)
    {
        try {
            if (!is_null($this->sendMailLogId) && !$this->isResend) {
                SendMailLog::query()->where('id', $this->sendMailLogId)->update(['status' => SendMailLogStatus::FAILED, 'logs' => optional($exception)->getMessage()]);
            }
        } catch (\Exception $e) {
        }
        $message = 'none';
        if (!is_null($exception)) {
            $message = $exception->getMessage();
        }
        graylogError("Mailer/SendMail Job: \n\r {$message}", [
            'category' => 'job_send_mail_failed',
            'user_type' => 'system',
            'sendMailLogId' => $this->sendMailLogId,
            'action' => 'send_mail'
        ]);
    }

    public function createHashMailLog($sendMailLog, $renderTemplate) :string
    {
        $sellerId = $sendMailLog->seller_id;
        $subject = $sendMailLog->subject;
        $storeId = $sendMailLog->store_id;
        $orderId = $sendMailLog->order_id;
        $template = $sendMailLog->template;
        $emailTo = $sendMailLog->email_to;

        $contentNoHTML = utf8_encode(strip_tags($renderTemplate));
        $noCartKey = preg_replace('/cart_key=([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/', '', $contentNoHTML);
        $contentNoNewLine = str_replace(array("\r", "\n"), '', $noCartKey);
        $contentNoSpaces = preg_replace('/\s+/', '', $contentNoNewLine);

        $data = [
            'seller_id' => $sellerId,
            'subject' => $subject,
            'store_id' => $storeId,
            'order_id' => $orderId,
            'template' => $template,
            'email_to' => $emailTo,
            'content' => $contentNoSpaces,
        ];

        return md5_array($data);
    }
}
