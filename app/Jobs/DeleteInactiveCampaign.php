<?php

namespace App\Jobs;

use App\Enums\InactiveEnum;
use App\Models\IndexUserCleanSettings;
use App\Services\InactiveService;
use App\Traits\InactiveTrait;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteInactiveCampaign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    use InactiveTrait;

    public int $userId;

    private bool $shouldDelete;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(string $processId, $userId, $shouldDelete)
    {
        $this->userId = $userId;
        $this->processId = $processId;
        $this->fileType = 'Job';
        $this->fileName = $this->fileType . ':' . basename(__FILE__);
        $this->type = InactiveEnum::CAMPAIGN;
        $this->category = ['category' => $this->type];
        $this->days = isEnvLocalOrDev() ? 8 : 30;
        $this->count = isEnvLocalOrDev() ? 250 : 1000;
        $this->shouldDelete = $shouldDelete;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            InactiveService::logToGraylog($this->processId, $this->fileName, '[userId] : ' . $this->userId . ' Start deleting ' . $this->type . '...', $this->category);

            if (!$this->shouldDelete) {
                InactiveService::logToGraylog($this->processId, $this->fileName, 'Config shouldDelete: FALSE', $this->category);
            }

            $userCleanSetting = InactiveService::getUserCleanSetting($this->userId, $this->type);
            $checkingDay = InactiveService::getCheckingDay($userCleanSetting->clean_at, $this->days);
            $sixMonthAgoDateTime = InactiveService::getMonthAgoDatetime($checkingDay, 6);
            $threeMonthAgoDateTime = InactiveService::getMonthAgoDatetime($checkingDay, 3);

            $startEsTime = round(microtime(true) * 1000);
            $result1 = InactiveService::deleteInactiveCampaignsByCondition1($this->userId, $sixMonthAgoDateTime, $this->count, $this->shouldDelete);
            $endEsTime = round(microtime(true) * 1000);
            $esTime1 = $endEsTime - $startEsTime;

            $startEsTime2 = round(microtime(true) * 1000);
            $result2 = InactiveService::deleteInactiveCampaignsByCondition2($this->userId, $threeMonthAgoDateTime, $sixMonthAgoDateTime, $this->count, $this->shouldDelete);
            $endEsTime2 = round(microtime(true) * 1000);
            $esTime2 = $endEsTime2 - $startEsTime2;

            InactiveService::logToEstimateTimeGraylog($this->processId, $this->fileName, $this->type, $esTime1, $esTime2);

            if ($result1['total'] === 0 && $result2['total'] === 0) {
                InactiveService::logToGraylog($this->processId, $this->fileName, '[userId] : ' . $this->userId . ' - No inactive campaigns found', $this->category, true);
            } else {
                IndexUserCleanSettings::query()->where('seller_id', $this->userId)->where('type', $this->type)->update(['clean_at' => null]);
                $this->checkTotalCampaignsAndSendLog($result1['total'], $result2['total'], $result1['exampleCampaignIds'], $result2['exampleCampaignIds']);
            }

        } catch (\Exception $e) {
            InactiveService::logExceptionToGraylog($this->processId, $this->fileName, $this->type, $e);
        }
    }

    private function checkTotalCampaignsAndSendLog(int $totalCampaigns1, int $totalCampaigns2, array $exampleCampaignIds1 = [], array $exampleCampaignIds2 = []): void
    {
        $context = [
            'category' => $this->type,
            'exampleCampaignIds1' => $exampleCampaignIds1,
            'exampleCampaignIds2' => $exampleCampaignIds2,
        ];
        $totalCampaigns = $totalCampaigns1 + $totalCampaigns2;


        if ($totalCampaigns > 0) {
            $messages = '[userId] :' . $this->userId . ' - Total campaigns: ' . $totalCampaigns;
        } else {
            $messages = '[userId] :' . $this->userId . ' - No inactive campaigns found';
        }

        InactiveService::logToGraylog($this->processId, $this->fileName, $messages, $context, true);
    }

}
