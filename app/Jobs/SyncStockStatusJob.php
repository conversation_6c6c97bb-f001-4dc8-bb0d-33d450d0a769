<?php

namespace App\Jobs;

use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Http\Controllers\Admin\GoogleSheetApiController;
use App\Models\Product;
use App\Traits\ElasticClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncStockStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ElasticClient;

    const QUEUE_NAME = 'sync-stock-status';
    /**
     * Create a new job instance.
     */
    public function __construct(readonly private array $productIds = [])
    {
        $this->onQueue(self::QUEUE_NAME);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $productsElasticParams = [];
            $inactiveProducts = Product::query()
                ->select('id')
                ->where('product_type', ProductType::TEMPLATE)
                ->where('status', '!=', ProductStatus::ACTIVE)
                ->pluck('id')
                ->toArray();
            $products = Product::query()
                ->select('id', 'name', 'options', 'sku')
                ->with(['variants:product_id,variant_key,location_code,out_of_stock'])
                ->where('seller_id', 0)
                ->where('product_type', ProductType::TEMPLATE)
                ->where('status', ProductStatus::ACTIVE)
                ->when($this->productIds, function ($query, $productIds) {
                    $query->whereIn('id', $productIds);
                })
                ->get();
            $products->each(function ($product) use (&$productsElasticParams) {
                $variantKeys = [];
                $headingOptions = [];
                $productOptions = json_decode($product->options, true);
                unset($productOptions['custom_options']);
                foreach ($productOptions as $key => $productOption) {
                    if ($productOption == [""]) {
                        unset($productOptions[$key]);
                    }
                }
                if ($productOptions) {
                    $variantKeys = $this->mapVariantKey(array_values((array)$productOptions));
                    $headingOptions = array_map(fn($item) => str_replace('_', ' ', $item), array_keys($productOptions));
                } else {
                    $headingOptions = ['size'];
                }
                $oosDefault = ['*', '150', 'US', 'CA'];
                $product->variants->groupBy('product_id')->each(function ($variants) use ($product, &$data, &$key, &$productsElasticParams, $headingOptions, $variantKeys, $oosDefault) {
                    $variantKeys = array_diff($variantKeys, str_replace('-', '|', $variants->pluck('variant_key')->toArray()));
                    foreach ($variantKeys as $variantKey) {
                        $variantKeyArray = array_map(fn($item) => ucwords(str_replace('_', ' ', $item ?? '')), explode('|', $variantKey));
                        $location = [
                            'ww' => false,
                            'eu' => false,
                            'us' => false,
                            'ca' => false,
                        ];
                        $this->addProductsElasticParams($productsElasticParams, $product, $location, $headingOptions, $variantKeyArray);
                    }
                    $variants->groupBy('variant_key')->each(function ($variantGroupByKey, $variantKey) use ($product, &$data, &$key, $oosDefault, &$productsElasticParams, $headingOptions) {
                        $variantKeyArray = array_map(fn($item) => ucwords(str_replace('_', ' ', $item ?? '')), explode('-', $variantKey));
                        $oos = $variantGroupByKey->pluck('out_of_stock', 'location_code')->toArray();
                        foreach ($oosDefault as $locationCode) {
                            $oos[$locationCode] = (bool)($oos[$locationCode] ?? true);
                        }
                        $location = [
                            'ww' => $oos['*'],
                            'eu' => $oos['150'],
                            'us' => $oos['US'],
                            'ca' => $oos['CA'],
                        ];
                        $this->addProductsElasticParams($productsElasticParams, $product, $location, $headingOptions, $variantKeyArray);
                    });
                });
            });
            $synced = true;
            $totalChunk = 500;
            $this->deleteStockStatusByProductIds([...$products->pluck('id')->toArray(), ...$inactiveProducts]);
            foreach (array_chunk($productsElasticParams, $totalChunk) as $chunk) {
                $result = $this->elastic('bulk', ['body' => $chunk]);
                if (empty($result) || !empty($result['errors'])) {
                    $synced = false;
                }
            }
            graylogInfo("Sync stock status to elasticsearch.", [
                'category' => 'sync_stock_status_log',
                'user_type' => 'system',
                'action' => 'sync_stock_status',
                'total_data' => count($productsElasticParams),
                'status' => $synced ? 'Success' : 'Fail',
            ]);
            $googleSheetApiController = new GoogleSheetApiController();
            $googleSheetApiController->updateOutOfStockProduct();
        } catch (\Exception $e) {
            logException($e);
        }
    }

    private function mapVariantKey($arrays, $i = 0)
    {
        if (!isset($arrays[$i])) {
            return [];
        }

        if ($i == count($arrays) - 1) {
            return $arrays[$i];
        }

        $next = $this->mapVariantKey($arrays, $i + 1);
        $productVariants = [];
        foreach ($arrays[$i] as $v) {
            foreach ($next as $t) {
                $productVariants[] = str_replace(' ', '_', $v) . '|' . str_replace([' ', '-'], '_', $t);
            }
        }

        return $productVariants;
    }

    private function addProductsElasticParams(array &$productsElasticParams, $product, array $location, array $headingOptions, array $variantKeyArray): void
    {
        $uuid = generateUUID();
        if (count($variantKeyArray) > count($headingOptions) && $headingOptions[0] === 'size') {
            $productsElasticParams[] = [
                'index' => [
                    '_index' => 'stock_status',
                    '_type' => 'oos_product',
                    '_id' => $uuid,
                ]
            ];
            $secondOption = array_pop($variantKeyArray);
            $firstOption = implode('-', $variantKeyArray);
            $productsElasticParams[] = [
                'id' => $uuid,
                'product_id' => $product->id,
                'sku' => $product->sku,
                'name' => $product->name,
                ...array_combine($headingOptions, [$firstOption, $secondOption]),
                ...($location),
            ];
        } else if (count($variantKeyArray) === count($headingOptions)) {
            $productsElasticParams[] = [
                'index' => [
                    '_index' => 'stock_status',
                    '_type' => 'oos_product',
                    '_id' => $uuid,
                ]
            ];
            $productsElasticParams[] = [
                'id' => $uuid,
                'product_id' => $product->id,
                'sku' => $product->sku,
                'name' => $product->name,
                ...array_combine($headingOptions, $variantKeyArray),
                ...($location),
            ];
        }
    }
}
