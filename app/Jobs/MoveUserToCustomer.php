<?php

namespace App\Jobs;

use App\Data\User\CustomerData;
use App\Enums\SystemRole;
use App\Models\Customer;
use App\Models\Order;
use App\Models\ProductReview;
use App\Models\SellerCustomer;
use App\Models\StripeCustomer;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Enums\SyncToCustomerStatus;
class MoveUserToCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public array $userIds = [])
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $users = User::query()
                ->where('role', SystemRole::CUSTOMER)
                ->when(!empty($this->userIds), function ($q) {
                    $q->where('id', $this->userIds);
                })
                ->where('sync_to_customer', SyncToCustomerStatus::PENDING)
                ->orderBy('id', 'desc')
                ->limit(2000)
                ->get();
            $userIds = $users->pluck('id');
            $existingCustomerIds = Customer::query()->select('id')->whereIn('id', $userIds->toArray())->pluck('id')->toArray();
            $userIdNotExistedInCustomer = array_diff($userIds->toArray(), $existingCustomerIds);

            //Insert non overlap id
            $usersInsert = $users->whereIn('id', $userIdNotExistedInCustomer)->toArray();
            $insertChunks = array_chunk(CustomerData::collection($usersInsert)->toArray(), 100);
            foreach ($insertChunks as $chunk) {
                $insertEmails = array_column($chunk, 'email');
                $emailExistedInCustomer = Customer::query()->select('email')->whereIn('email', $insertEmails)->pluck('email')->toArray();
                $emailExistedInCustomer = array_map('strtolower', $emailExistedInCustomer);
                $chunk = array_filter($chunk, function ($item) use ($emailExistedInCustomer) {
                    return !in_array(strtolower($item['email']), $emailExistedInCustomer);
                });
                if (!empty($emailExistedInCustomer)) {
                    User::query()->whereIn('email', $emailExistedInCustomer)->update([
                        'sync_to_customer' => SyncToCustomerStatus::NOT_ALLOWED,
                    ]);
                    graylogInfo('migrate user to customer data', [
                        'category' => 'migrate_user_to_customer',
                        'case' => 1,
                        'not_allow' => true,
                        'message' => 'email existed in customer',
                        'emails' => $emailExistedInCustomer,
                    ]);
                }
                graylogInfo('migrate user to customer data', [
                    'category' => 'migrate_user_to_customer',
                    'case' => 1,
                    'emails_insert' => array_column($chunk, 'email'),
                    'ids_insert' =>  array_column($chunk, 'id'),
                ]);
                Customer::insert($chunk);
                User::query()->whereIn('id', array_column($chunk, 'id'))->update([
                    'sync_to_customer' => SyncToCustomerStatus::DONE,
                ]);
            }

            //Insert overlap id
            $usersInsert = $users->whereIn('id', $existingCustomerIds);
            $currentUserIds = $usersInsert->pluck('id')->toArray();
            $currentUserEmails = $usersInsert->pluck('email')->toArray();
            $totalOrders = Order::query()->whereIn('customer_id', $currentUserIds)->whereIn('customer_email', $currentUserEmails)->get();
            foreach ($usersInsert as $user) {
                $orders = $totalOrders->where('customer_id', $user->id)->where('customer_email', $user->email);
                if ($orders->isEmpty()) {
                    $user->sync_to_customer = SyncToCustomerStatus::NOT_ALLOWED;
                    $user->save();
                    graylogInfo('migrate user to customer data', [
                        'category' => 'migrate_user_to_customer',
                        'case' => 2,
                        'not_allow' => true,
                        'message' => 'not found any orders',
                        'email_insert' => $user->email,
                        'id_insert' => $user->id,
                    ]);
                    continue;
                }
                $orderIds = $orders->pluck('id')->toArray();
                $sellerIds = $orders->pluck('seller_id')->toArray();
                $storeIds = $orders->pluck('store_id')->toArray();
                $gatewayIds = $orders->pluck('payment_gateway_id')->toArray();
                $customerInsert = CustomerData::from($user->makeHidden(['id'])->toArray())->toArray();
                $customerExisted = Customer::query()->select('email')->where('email', $user->email)->exists();
                if ($customerExisted) {
                    $user->sync_to_customer = SyncToCustomerStatus::NOT_ALLOWED;
                    $user->save();
                    graylogInfo('migrate user to customer data', [
                        'category' => 'migrate_user_to_customer',
                        'case' => 2,
                        'not_allow' => true,
                        'message' => 'email existed in customer',
                        'email_insert' => $user->email,
                        'id_insert' => $user->id,
                    ]);
                    continue;
                }
                $userData = Customer::create($customerInsert);

                graylogInfo('migrate user to customer data', [
                    'category' => 'migrate_user_to_customer',
                    'case' => 2,
                    'email_insert' => $userData->email,
                    'id_insert' => $userData->id,
                ]);
                foreach ($orders as $order) {
                    $sellerId = $order->seller_id;
                    $storeId = $order->store_id;
                    $gatewayId = $order->payment_gateway_id;
                    $orderId = $order->id;

                    ProductReview::query()->where('order_id', $orderId)
                        ->where('customer_id', $user->id)
                        ->update([
                            'customer_id' => $userData->id
                        ]);

                    SellerCustomer::query()->where('customer_id', $user->id)
                        ->where('seller_id', $sellerId)
                        ->where('store_id', $storeId)
                        ->update([
                            'customer_id' => $userData->id
                        ]);

                    StripeCustomer::query()->where('gateway_id', $gatewayId)
                        ->where('customer_id', $user->id)
                        ->update([
                            'customer_id' => $userData->id
                        ]);

                    $order->customer_id = $userData->id;
                    $order->save();
                }
                $user->sync_to_customer = SyncToCustomerStatus::DONE;
                $user->save();
            }
        } catch (\Exception $e) {
            graylogError('Error in migrate user to customer', [
                'category' => 'migrate_user_to_customer_error',
                'error' => $e->getMessage(),
            ]);
            logToDiscord('Migrate user to customer error : ' . $e->getMessage());
        }
    }
}
