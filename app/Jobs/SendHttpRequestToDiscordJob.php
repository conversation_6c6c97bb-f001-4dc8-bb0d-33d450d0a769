<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SendHttpRequestToDiscordJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $url;
    protected $message;
    protected $embeds = [];
    protected $long_message;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($url, $message, $embeds = [], $long_message = null)
    {
        $this->url = $url;
        $this->message = $message;
        $this->long_message = $long_message;

        if (is_array($embeds) && !empty($embeds)) {
            $this->embeds = $embeds;
        }
        $this->onQueue('log-discord');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        if (!$this->url) {
            return;
        }

        $payload = [
            'content' => $this->message ?: ''
        ];

        if (!empty($this->embeds)) {
            $payload['embeds'] = $this->embeds;
        }
        try {
            $http = Http::retry(3, 100)->timeout(15);
            if (!empty($this->long_message)) {
                $http->attach('file', $this->long_message, 'log_' . date('YmdHis') . '.txt');
                $res = $http->post($this->url, [
                    'payload_json' => json_encode($payload, JSON_THROW_ON_ERROR),
                ]);
            } else {
                $res = $http->asJson()->post($this->url, $payload);
            }
        } catch (\Throwable $e) {
            return;
        }

        if ($res->successful()) {
            return;
        }

        // https://discord.com/developers/docs/topics/rate-limits#exceeding-a-rate-limit
        if ($res->failed() && $res->status() === 429) {
            // {
            //  "global": false,
            //  "message": "You are being rate limited.",
            //  "retry_after": 890
            //}
            $retryAfter = $res->json('retry_after');

            if ($retryAfter) {
                self::dispatch($this->url, $this->message, $this->embeds)
                    ->delay(now()->addSeconds($retryAfter + 1));
            }
        }
    }
}
