<?php

namespace App\Jobs;

use App\Enums\CacheTime;
use App\Enums\DiscordChannel;
use App\Enums\DiscordUserIdEnum;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\QueueName;
use App\Http\Controllers\Admin\FulfillController;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use RuntimeException;
use Throwable;

class CrawlAndUpdateOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const SUCCESS = 'success';

    public const FAILED = 'failed';

    private array $result = [];

    public function __construct(
        private readonly ObjectFulfill $objectProvider,
        private readonly string|array  $fulfillOrderId,
        private readonly int           $orderId,
        private readonly bool          $justCreated,
        private readonly bool          $isManually,
        private readonly bool          $skipException = false
    ) {
        $this->onQueue(QueueName::CRAWL_FULFILL);
    }

    /**
     * @return string
     * @throws \Throwable
     */
    public function handle(): string
    {
        try {
            if (!is_array($this->fulfillOrderId)) {
                $this->result = $this->crawlFulfillOrder();

                if (empty($this->result) && $this->isManually) {
                    return $this->manuallyActionButEmptyResult();
                }
                $this->updateOrderProduct();
            } else {
                $this->result = $this->crawlFulfillOrders();

                if (empty($this->result) && $this->isManually) {
                    return $this->manuallyActionButEmptyResults();
                }
                $this->updateOrderProducts();
            }
            return self::SUCCESS;
        } catch (Throwable $e) {
            $this->handleException($e);
        }

        return self::FAILED;
    }

    /**
     * @throws \Throwable
     */
    public function crawlFulfillOrder(): array
    {
        return $this->objectProvider->crawlOrder($this->fulfillOrderId, $this->orderId, $this->justCreated);
    }

     /**
     * @throws \Throwable
     */
    public function crawlFulfillOrders(): array
    {
        return $this->objectProvider->crawlOrders($this->fulfillOrderId);
    }

    /**
     * @return string
     */
    public function manuallyActionButEmptyResult(): string
    {
        throw new RuntimeException("Empty response.\n Order Id: $this->orderId\n Fulfill Order Id: $this->fulfillOrderId\n Supplier Id: " . $this->objectProvider->getSupplierId());
    }

    /**
     * @return string
     */
    public function manuallyActionButEmptyResults(): string
    {
        $fulfillOrderIdsString = implode(',', $this->fulfillOrderId);
        throw new RuntimeException("Empty response.\n Fulfill Order Id: $fulfillOrderIdsString\n Supplier Id: " . $this->objectProvider->getSupplierId());
    }


    /**
     * @param     \Throwable     $e
     *
     * @return void
     *
     * @throws \Throwable
     */
    public function handleException(Throwable $e): void
    {
        if (preg_match('#^cURL error#i', $e->getMessage())) {
            logToDiscord(__CLASS__ . ': ' . $e->getMessage(), DiscordChannel::FULFILL_ORDER, threadId: 1310564038354403328);
            return;
        }

        logToDiscord(
            'Method: ' . __FUNCTION__
            . "\r\nOrder id: " . $this->orderId
            . "\r\nFulfill order id: " . $this->fulfillOrderId
            . "\r\nError: " . $e->getMessage()
            . "\r\nResponse: " . json_encode($this->result, JSON_THROW_ON_ERROR)
            , DiscordChannel::FULFILL_ORDER
            , true
        );

        if ($this->isManually) {
            throw $e;
        }

        OrderProduct::query()
            ->where('fulfill_order_id', $this->fulfillOrderId)
            ->where('order_id', $this->orderId)
            ->update([
                'fulfill_status' => OrderProductFulfillStatus::EXCEPTION,
                'fulfill_exception_log' => $e->getMessage(),
            ]);
        $embedDesc = [
            [
                'description' => "Order Id: #" . $this->orderId . "\r\nSupplier Name: " . $this->objectProvider->getSupplierName() . "\r\nException: " . $e->getMessage(),
                'color' => 15548997
            ]
        ];
        logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::MIMICS) . mentionDiscord(DiscordUserIdEnum::QUANCS) : '') . " Đơn này đẩy sang supplier bị EXCEPTION. Mọi người check nha.\r\nhttps://admin.senprints.com/order/detail/" . $this->orderId, channel: DiscordChannel::TECH_SUPPORT, embeds: $embedDesc, threadId: 1077531233623478292);
    }

    /**
     * @return void
     * @throws \Throwable
     */
    public function updateOrderProduct(): void
    {
        if (!empty(data_get($this->result, 'order_id')) && data_get($this->result, 'fulfill_status') !== OrderProductFulfillStatus::EXCEPTION) {
            $cacheKey = 'need_cs_check_manually_' . $this->orderId;
            try {
                $cache = cache()->has($cacheKey);
            } catch (Throwable $e) {
                $cache = false;
            }
            $exists = OrderProduct::query()
                ->where('order_id', $this->orderId)
                ->where('supplier_id', $this->objectProvider->getSupplierId())
                ->whereNotIn('fulfill_status', [OrderProductFulfillStatus::CANCELLED, OrderProductFulfillStatus::FULFILLED])
                ->whereNull('fulfill_order_id')
                ->whereNotNull('supplier_id')
                ->exists();
            if ($exists) {
                OrderProduct::query()
                    ->where('order_id', $this->orderId)
                    ->where('supplier_id', $this->objectProvider->getSupplierId())
                    ->whereNotIn('fulfill_status', [OrderProductFulfillStatus::CANCELLED, OrderProductFulfillStatus::FULFILLED])
                    ->whereNull('fulfill_order_id')
                    ->whereNotNull('supplier_id')
                    ->update([
                        'fulfill_order_id' => data_get($this->result, 'order_id'),
                        'fulfill_exception_log' => null,
                        'fulfilled_at' => DB::raw('CURRENT_TIMESTAMP')
                    ]);
                $order = Order::query()->find($this->orderId);
                OrderHistory::insertLog($order, OrderHistoryActionEnum::FULFILL_UPDATED, 'The system has updated the fulfill order id for the order product');
                if (!$cache) {
                    cache()->put($cacheKey, $this->orderId, CacheTime::CACHE_5m);
                    logToDiscord(
                        'Method: ' . __FUNCTION__
                        . "\r\nOrder id: " . $this->orderId
                        . "\r\nCurrent fulfill order id: " . $this->fulfillOrderId
                        . "\r\nSupplier id: " . $this->objectProvider->getSupplierId()
                        . "\r\nFulfill order id: " . data_get($this->result, 'order_id')
                        . "\r\nResponse: " . json_encode($this->result, JSON_THROW_ON_ERROR)
                        , DiscordChannel::FULFILL_ORDER
                    );
                    $embedDesc = [
                        [
                            'description' => "Order Id: #" . $this->orderId . "\r\nSupplier Name: " . $this->objectProvider->getSupplierName(),
                            'color' => 15548997
                        ]
                    ];
                    logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::CS) : '') . " Đơn này bị lỗi, mọi người check đơn này nha.", channel: DiscordChannel::TECH_SUPPORT, embeds: $embedDesc, threadId: 1077531233623478292);
                }
            }
        }
        if ($this->skipException && data_get($this->result, 'fulfill_status') === OrderProductFulfillStatus::EXCEPTION) {
            return;
        }
        FulfillController::updateByResponse($this->result, $this->isManually);
    }

    /**
     * @return void
     * @throws \Throwable
     */
    public function updateOrderProducts(): void
    {
        if (!empty($this->result)) {
            $cacheKey = 'need_cs_check_manually_' . $this->orderId;
            try {
                $cache = cache()->has($cacheKey);
            } catch (Throwable $e) {
                $cache = false;
            }
            foreach ($this->result as $orderProduct) {
                if (!empty(data_get($orderProduct, 'order_id')) && data_get($orderProduct, 'fulfill_status') !== OrderProductFulfillStatus::EXCEPTION) {
                    $exists = OrderProduct::query()
                        ->where('order_id', $this->orderId)
                        ->where('supplier_id', $this->objectProvider->getSupplierId())
                        ->whereNotIn('fulfill_status', [OrderProductFulfillStatus::CANCELLED, OrderProductFulfillStatus::FULFILLED])
                        ->whereNull('fulfill_order_id')
                        ->whereNotNull('supplier_id')
                        ->exists();
                    if ($exists) {
                        OrderProduct::query()
                            ->where('order_id', $this->orderId)
                            ->where('supplier_id', $this->objectProvider->getSupplierId())
                            ->whereNotIn('fulfill_status', [OrderProductFulfillStatus::CANCELLED, OrderProductFulfillStatus::FULFILLED])
                            ->whereNull('fulfill_order_id')
                            ->whereNotNull('supplier_id')
                            ->update([
                                'fulfill_order_id' => data_get($orderProduct, 'order_id'),
                                'fulfill_exception_log' => null,
                                'fulfilled_at' => DB::raw('CURRENT_TIMESTAMP')
                            ]);
                        $order = Order::query()->find($this->orderId);
                        OrderHistory::insertLog($order, OrderHistoryActionEnum::FULFILL_UPDATED, 'The system has updated the fulfill order id for the order product');
                        if (!$cache) {
                            cache()->put($cacheKey, $this->orderId, CacheTime::CACHE_5m);
                            logToDiscord(
                                'Method: ' . __FUNCTION__
                                . "\r\nOrder id: " . $this->orderId
                                . "\r\nCurrent fulfill order id: " . $this->fulfillOrderId
                                . "\r\nSupplier id: " . $this->objectProvider->getSupplierId()
                                . "\r\nFulfill order id: " . data_get($orderProduct, 'order_id')
                                . "\r\nResponse: " . json_encode($orderProduct, JSON_THROW_ON_ERROR)
                                ,DiscordChannel::FULFILL_ORDER
                            );
                            $embedDesc = [
                                [
                                    'description' => "Order Id: #" . $this->orderId . "\r\nSupplier Name: " . $this->objectProvider->getSupplierName(),
                                    'color' => 15548997
                                ]
                            ];
                            logToDiscord((app()->isProduction() ? mentionDiscord(DiscordUserIdEnum::CS) : '') . " Đơn này bị lỗi, mọi người check đơn này nha.",  channel: DiscordChannel::TECH_SUPPORT, embeds: $embedDesc, threadId: 1077531233623478292);
                        }
                    }
                }
                if ($this->skipException && data_get($orderProduct, 'fulfill_status') === OrderProductFulfillStatus::EXCEPTION) {
                    continue;
                }
                FulfillController::updateByResponse($orderProduct, $this->isManually);
            }
        }
    }
}
