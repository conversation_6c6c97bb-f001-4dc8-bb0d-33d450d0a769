<?php

namespace App\Jobs;

use App\Enums\OrderTypeEnum;
use App\Enums\TradeMarkStatusEnum;
use App\Enums\UserStatusEnum;
use App\Models\Order;
use App\Models\OrderProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ReviewOrderTrademark implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $orderId;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($orderId)
    {
        $this->orderId = $orderId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $order = Order::findOrFail($this->orderId);

        if ($order->type !== OrderTypeEnum::CUSTOM) {
            $order->load('seller');

            if ($order->seller && ($order->seller->status === UserStatusEnum::TRUSTED || $order->seller->custom_payment)) {
                return;
            }

            $order->load('products');

            $verifiedCampaignIds = OrderProduct::query()
                ->where('order_id', '!=', $order->id)
                ->whereIn('campaign_id', $order->products->pluck('campaign_id'))
                ->where('tm_status', TradeMarkStatusEnum::VERIFIED)
                ->where('updated_at', '>=', now()->subDays(30))
                ->groupBy('campaign_id')
                ->pluck('campaign_id');

            $flagProducts = $order->products->whereNotIn('campaign_id', $verifiedCampaignIds);

            if ($flagProducts->isNotEmpty()) {
                $order->fill(['tm_status' => TradeMarkStatusEnum::FLAGGED])->update();
                $order->products()->whereIn('id', $flagProducts->pluck('id'))->update(['tm_status' => TradeMarkStatusEnum::FLAGGED]);
            }
        }
    }
}
