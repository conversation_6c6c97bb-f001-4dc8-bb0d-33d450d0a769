<?php

namespace App\Jobs;

use App\Models\TelegramNotification;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendTradeMarkNotificationToSeller implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $campaignId;
    private User $seller;

    /**
     * Create a new job instance.
     *
     * @param int $campaignId
     * @return void
     */
    public function __construct(int $sellerId, int $campaignId)
    {
        $this->campaignId = $campaignId;
        $this->seller = User::query()->find($sellerId);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        //1. Send message to Telegram of seller
        $chatId = TelegramNotification::query()
            ->where([
                'seller_id' => $this->seller->id,
                'enable' => 1
            ])
            ->value('chat_id');
        if (!empty($chatId)) {
            try {
                $br = "\n\n";
                $message = "❌ Your campaign #{$this->campaignId} has been blocked due to trademark infringement." . $br .
                    "Please review your campaign keywords and ad content to ensure compliance with our policies and no trademark infringement." . $br .
                    "Once resolved, let us know and we'll review your campaign again." . $br;
                logToTelegram($chatId, $message);
            } catch (\Throwable $e) {
                logToDiscord("Cannot send Telegram notification trademark issue to Seller ID #{$this->seller->id}, Campaign ID: #{$this->campaignId} - Error: {$e->getMessage()}");
            }
        }

        //2. Send email to seller
        try {
            if ($this->seller) {
                $dataSendMailLog = [
                    'sellerId' => $this->seller->id
                ];
                $config = [
                    'to' => $this->seller->email,
                    'template' => 'seller.block-campaign-trademark-notification',
                    'data' => [
                        'subject' => "Campaign #{$this->campaignId} was blocked due to trademark issue",
                        'name' => $this->seller->name ?? 'Seller',
                        'campaign_id' => $this->campaignId
                    ],
                    'sendMailLog' => $dataSendMailLog
                ];

                if (!sendEmail($config)) {
                    logToDiscord([
                        'email' => 'block-campaign-trademark-notification',
                        'order_id' => $this->campaignId
                    ], 'email');
                }
            }
        } catch (\Throwable $e) {
            logToDiscord("Cannot send email notification trademark issue to Seller ID #{$this->seller->id}, Campaign ID: #{$this->campaignId} - Error: {$e->getMessage()}");
        }
    }
}
