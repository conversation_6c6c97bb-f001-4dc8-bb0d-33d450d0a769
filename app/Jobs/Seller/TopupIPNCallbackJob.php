<?php

namespace App\Jobs\Seller;

use App\Enums\SellerBillingType;
use App\Enums\UserTopupTransactionStatus;
use App\Enums\UserTopupTransactionType;
use App\Models\SellerBilling;
use App\Models\TopupWebhookLogs;
use App\Models\User;
use App\Models\UserTopupTransaction;
use App\Services\UserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class TopupIPNCallbackJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $callbackType;
    protected $callbackMessage = [];
    protected $reValidate = 'no';
    protected $webhookLogId;
    protected $isDev = false;
    protected $topupPayoneerVnFee = 3;
    protected $minimumTopupPayoneerVnAmount = 1000;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($callbackType, $callbackMessage, $reValidate = 'no', $webhookLogId = null)
    {
        $this->callbackType = $callbackType;
        $this->callbackMessage = $callbackMessage;
        $this->reValidate = $reValidate;
        $this->webhookLogId = $webhookLogId;

        if (!app()->isProduction()) {
            $this->isDev = true;
        }
        $this->onQueue('topup');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $transactionType = UserTopupTransactionType::BANK;
            if ($this->callbackType === 'email') {
                $transactionType = $this->callbackMessage['paymentSite'];
            }

            $isPingPong = false;

            $msgIcon = '<:vnd:999694561146306560>';
            if ($transactionType === UserTopupTransactionType::PAYONEER) {
                $msgIcon = '<:payoneer:999693136639037512>';
            } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                $msgIcon = '<:pingpong:999693952536039426>';
                $isPingPong = true;
            } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                $msgIcon = '<:lianlian:1105799408257343488>';
            }
            $topupMessage = '';
            $sentBy = 'UNKNOWN_REF_NAME';
            $paymentId = 'UNKNOWN_TRANSACTION_ID';
            $paymentNote = 'UNKNOWN_PAYMENT_NOTE';
            if (isset($this->callbackMessage['sentBy'])) {
                $sentBy = $this->callbackMessage['sentBy'];
            }
            if (isset($this->callbackMessage['paymentId'])) {
                $paymentId = $this->callbackMessage['paymentId'];
            }
            if (isset($this->callbackMessage['paymentNote'])) {
                $paymentNote = $this->callbackMessage['paymentNote'];
            }
            $errorAmountPrefix = '$';
            $errorAmount = $this->callbackMessage['amount'] ?? 0;
            if (is_null($this->callbackMessage['code'])) {
                $this->callbackMessage['code'] = 'UNKNOWN_CODE';
            }

            $topupTransaction = null;
            // sleep random with microsec
            usleep(random_int(1000000, 4999999));
            if ($isPingPong) {
                $topupIsExist = UserTopupTransaction::pingPongCheckTransactionId($paymentId);
                if (!$topupIsExist) {
                    $findAmount = str_replace(",", "", $this->callbackMessage['amount']);
                    $topupTransaction = UserTopupTransaction::getTransactionByEmailAndAmount($this->callbackMessage['email'], $findAmount, $paymentId);
                }
            } else {
                if ($this->reValidate === 'yes') {
                    $topupTransactionCheck = UserTopupTransaction::getTransactionBy($this->callbackMessage['code'], $transactionType);
                    if ($topupTransactionCheck && $topupTransactionCheck->status === UserTopupTransactionStatus::PROCESSING) {
                        $topupTransactionCheck->update([
                            'status' => UserTopupTransactionStatus::PENDING
                        ]);
                    }
                }
                $checkExistAmount = number_format((int) $this->callbackMessage['amount']);
                $checkExistAmount = str_replace(",", "", $checkExistAmount);
                $topupIsExist = UserTopupTransaction::getTransactionByCode($this->callbackMessage['code'], $transactionType, $checkExistAmount,UserTopupTransactionStatus::COMPLETED, true);
                if (!$topupIsExist) {
                    $topupTransaction = UserTopupTransaction::getTransactionByCode($this->callbackMessage['code'], $transactionType);
                }
            }

            if (!$topupTransaction || (!$isPingPong && $this->callbackMessage['code'] === 'UNKNOWN_CODE')) {
                $this->callbackType = strtoupper($this->callbackType);
                if ($transactionType === UserTopupTransactionType::PAYONEER) {
                    if (isset($this->callbackMessage['paymentNote'])) {
                        $topupMessage .= "UNMATCHED $msgIcon #{$this->callbackMessage['code']} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$this->callbackMessage['paymentNote']} - {$sentBy} - {$paymentId})";
                    } else {
                        $topupMessage .= "UNMATCHED $msgIcon #{$this->callbackMessage['code']} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - {$paymentId})";
                    }
                } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                    $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - {$this->callbackMessage['email']} - {$paymentId})";
                } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                    $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - $paymentNote - {$paymentId})";
                } elseif ($transactionType === UserTopupTransactionType::BANK) {
                    $errorAmount = str_replace(".", "", (string) $errorAmount);
                    $vndFormat = number_format((int) $errorAmount);
                    $topupMessage .= "UNMATCHED $msgIcon #{$this->callbackMessage['code']} : {$errorAmountPrefix}0 : Unknown : #0 : {$vndFormat}đ";
                } else {
                    $topupMessage .= "UNMATCHED $msgIcon #{$this->callbackMessage['code']} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 : 0đ";
                }
                $embedDesc = [
                    [
                        'description' => $topupMessage,
                        'color' => ********
                    ]
                ];
                if (!$this->isDev) {
                    logToDiscord('', 'topup', false, true, 7, $embedDesc);
                } else {
                    logToDiscordNowWithEmbeds('', 'topup_dev', false, $embedDesc);
                }
                TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'UNMATCHED_UNKNOWN_CODE');
                $errorMsg = "TopupIPNCallbackJob: IPN Callback {$this->callbackType} Error! - Not found topup transaction code";
                $errorMsg .= "\n" . json_encode($this->callbackMessage);
                graylogInfo($errorMsg, [
                    'category' => 'topup_errors',
                    'webhook_id' => $this->webhookLogId,
                    'user_type' => 'system',
                    'transaction_type' => $transactionType,
                    'version' => '202301208',
                    'action'  => 'job'
                ]);
                return;
            }

            if (!$topupIsExist) {
                if ($topupTransaction->status === UserTopupTransactionStatus::PENDING) {
                    $topupTransaction->update([
                        'status' => UserTopupTransactionStatus::PROCESSING
                    ]);
                    $topupTransactionAmount = null;
                    if ($this->callbackType === 'sms') {
                        $topupTransactionAmount = $topupTransaction->amount_vnd;
                    } elseif ($this->callbackType === 'email') {
                        $topupTransactionAmount = $topupTransaction->amount_usd;
                        if ($transactionType === UserTopupTransactionType::BANK) {
                            $topupTransactionAmount = $topupTransaction->amount_vnd;
                            $this->callbackMessage['amount'] = str_replace(".", "", $this->callbackMessage['amount']);
                        }

                        if ($isPingPong) {
                            $this->callbackMessage['amount'] = str_replace(",", "", $this->callbackMessage['amount']);
                        }
                        $this->callbackMessage['amount'] = number_format((int) $this->callbackMessage['amount']);
                        $this->callbackMessage['amount'] = str_replace(",", "", $this->callbackMessage['amount']);
                    }
                    if (!is_null($topupTransactionAmount)) {
                        $callbackType = strtoupper($this->callbackType);
                        $needRefundFee = false;
                        $sellerTopup = (float)$this->callbackMessage['amount'];
                        if ($topupTransaction->type === UserTopupTransactionType::PAYONEER_VN &&
                            $topupTransaction->amount_usd >= $this->minimumTopupPayoneerVnAmount &&
                            (float)$topupTransaction->amount_usd === (float)($this->topupPayoneerVnFee + $this->callbackMessage['amount'])
                        ) {
                            $this->callbackMessage['amount'] += $this->topupPayoneerVnFee;
                            $needRefundFee = true;
                        }
                        if ((float)$topupTransactionAmount === (float)$this->callbackMessage['amount']) {
                            $seller = User::query()->firstWhere('id', $topupTransaction->seller_id);
                            if (!is_null($seller)) {
                                // sleep random with microsec
                                usleep(random_int(1000000, 4999999));
                                $topupTransactionData = [
                                    'status' => UserTopupTransactionStatus::COMPLETED
                                ];
                                if ($transactionType === UserTopupTransactionType::PINGPONG) {
                                    $topupTransactionData['detail'] = json_encode(['transactionId' => $paymentId]);
                                }
                                $topupTransaction->update($topupTransactionData);
                                unset($topupTransactionData);
                                $topupDesc = 'Topup VND #' . $topupTransaction->code;
                                if ($transactionType === UserTopupTransactionType::PAYONEER) {
                                    $topupDesc = str_replace('Vn', 'VN', Str::headline($transactionType)) . ' Topup #' . $topupTransaction->code;
                                } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                                    $topupDesc = 'PingPong Topup #' . $topupTransaction->code  . ' - Email: ' . $topupTransaction->payment_email;
                                } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                                    $topupDesc = 'LianLian Topup #' . $topupTransaction->code  . ' - Email: ' . $topupTransaction->payment_email;
                                }
                                $balanceUpdated = $seller->updateBalance($topupTransaction->amount_usd, SellerBillingType::TOPUP, $topupDesc);
                                $topupAmountUsdToStr = '$' . $topupTransaction->amount_usd;
                                if ($needRefundFee) {
                                    $topupAmountUsdToStr .= ' : Seller Topup $' . $sellerTopup;
                                }
                                $topupAmountVndToStr = number_format($topupTransaction->amount_vnd, 0, '', ',');
                                if (!is_null($balanceUpdated)) {
                                    if ($transactionType === UserTopupTransactionType::BANK) {
                                        $topupMessage .= "OK $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : {$topupAmountVndToStr}đ";
                                    } elseif ($transactionType === UserTopupTransactionType::PAYONEER) {
                                        $topupMessage .= "OK $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : {$paymentId}";
                                    } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                                        $topupMessage .= "OK $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : {$this->callbackMessage['email']} : {$paymentId}";
                                    } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                                        $topupMessage .= "OK $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : {$paymentId}";
                                    }
                                    if ($transactionType !== UserTopupTransactionType::BANK && $paymentId !== 'UNKNOWN_TRANSACTION_ID') {
                                        SellerBilling::updateLog($balanceUpdated->id, $paymentId);
                                    }
                                    $embedDesc = [
                                        [
                                            'description' => $topupMessage,
                                            'color' => 5763719
                                        ]
                                    ];
                                    if (!$this->isDev) {
                                        logToDiscord('', 'topup', false, true, 7, $embedDesc);
                                    } else {
                                        logToDiscordNowWithEmbeds('', 'topup_dev', false, $embedDesc);
                                    }
                                    TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'OK');
                                    graylogInfo($topupMessage, [
                                        'category' => 'topup_info',
                                        'webhook_id' => $this->webhookLogId,
                                        'user_type' => 'system',
                                        'transaction_type' => $transactionType,
                                        'seller_id' => $seller->id,
                                        'version' => '202301208',
                                        'action'  => 'job'
                                    ]);
                                } else {
                                    if ($transactionType === UserTopupTransactionType::PAYONEER) {
                                        if (isset($this->callbackMessage['paymentNote'])) {
                                            $topupMessage .= "CANNOT_UPDATE_BALANCE $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : (Ref: {$this->callbackMessage['paymentNote']} - {$sentBy} - {$paymentId})";
                                        } else {
                                            $topupMessage .= "CANNOT_UPDATE_BALANCE $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : (Ref: {$sentBy} - {$paymentId})";
                                        }
                                    } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                                        $topupMessage .= "CANNOT_UPDATE_BALANCE $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : (Ref: {$sentBy} - {$this->callbackMessage['email']} - {$paymentId})";
                                    } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                                        $topupMessage .= "CANNOT_UPDATE_BALANCE $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : (Ref: {$sentBy} - $paymentNote - {$paymentId})";
                                    } else {
                                        $topupMessage .= "CANNOT_UPDATE_BALANCE $msgIcon #{$topupTransaction->code} : {$topupAmountUsdToStr} : {$seller->name} : #{$seller->id} : {$topupAmountVndToStr}đ";
                                    }
                                    $embedDesc = [
                                        [
                                            'description' => $topupMessage,
                                            'color' => ********
                                        ]
                                    ];
                                    if (!$this->isDev) {
                                        logToDiscord('', 'topup', false, true, 7, $embedDesc);
                                    } else {
                                        logToDiscordNowWithEmbeds('', 'topup_dev', false, $embedDesc);
                                    }
                                    TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'CANNOT_UPDATE_BALANCE');
                                    graylogInfo("TopupIPNCallbackJob: IPN Callback {$callbackType} Error!" . "\n\n" . json_encode($this->callbackMessage) . "\n\n" . json_encode($topupTransaction), [
                                        'category' => 'topup_errors',
                                        'webhook_id' => $this->webhookLogId,
                                        'user_type' => 'system',
                                        'version' => '202301208',
                                        'user_id' => null,
                                        'action'  => 'job'
                                    ]);
                                }
                            } else {
                                if ($transactionType === UserTopupTransactionType::PAYONEER) {
                                    if (isset($this->callbackMessage['paymentNote'])) {
                                        $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$this->callbackMessage['paymentNote']} - {$sentBy} - {$paymentId})";
                                    } else {
                                        $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - {$paymentId})";
                                    }
                                } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                                    $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - {$this->callbackMessage['email']} - {$paymentId})";
                                } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                                    $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : Unknown : #0 - (Ref: {$sentBy} - $paymentNote - {$paymentId})";
                                } elseif ($transactionType === UserTopupTransactionType::BANK) {
                                    $vndFormat = number_format((int) $topupTransaction->amount_vnd);
                                    $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$topupTransaction->amount_usd} : Unknown : #0 : {$vndFormat}đ";
                                } else {
                                    $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : Unknown : #0";
                                }
                                UserService::createTopupUnmatchTransaction($topupTransaction->id, $paymentId, $this->callbackMessage);
                                $embedDesc = [
                                    [
                                        'description' => $topupMessage,
                                        'color' => ********
                                    ]
                                ];
                                if (!$this->isDev) {
                                    logToDiscord('', 'topup', false, true, 7, $embedDesc);
                                } else {
                                    logToDiscordNowWithEmbeds('', 'topup_dev', false, $embedDesc);
                                }
                                TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'UNMATCHED_SELLER');
                                $errorMsg = "TopupIPNCallbackJob: IPN Callback {$callbackType} Error! Seller ID: {$topupTransaction->seller_id} not found";
                                $errorMsg .= "\n\n" . json_encode($this->callbackMessage) . "\n\n" . json_encode($topupTransaction);
                                graylogInfo($errorMsg, [
                                    'category' => 'topup_errors',
                                    'webhook_id' => $this->webhookLogId,
                                    'user_type' => 'system',
                                    'transaction_type' => $transactionType,
                                    'version' => '202301208',
                                    'action'  => 'job'
                                ]);
                            }
                        } else {
                            if ($transactionType === UserTopupTransactionType::PAYONEER) {
                                if (isset($this->callbackMessage['paymentNote'])) {
                                    $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : {$errorAmountPrefix}{$sellerTopup} : #0 - (Ref: {$this->callbackMessage['paymentNote']} - {$sentBy} - {$paymentId})";
                                } else {
                                    $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : {$errorAmountPrefix}{$sellerTopup} : #0 - (Ref: {$sentBy} - {$paymentId})";
                                }
                            } elseif ($transactionType === UserTopupTransactionType::PINGPONG) {
                                $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : {$errorAmountPrefix}{$sellerTopup} : #0 - (Ref: {$sentBy} - {$this->callbackMessage['email']} - {$paymentId})";
                            } elseif ($transactionType === UserTopupTransactionType::LIANLIAN) {
                                $topupMessage .= "UNMATCHED $msgIcon UNKNOWN_CODE : {$errorAmountPrefix}{$errorAmount} : {$errorAmountPrefix}{$sellerTopup} : #0 - (Ref: {$sentBy} - $paymentNote - {$paymentId})";
                            } elseif ($transactionType === UserTopupTransactionType::BANK) {
                                $vndFormat = number_format((int) $topupTransaction->amount_vnd);
                                $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$topupTransaction->amount_usd} : {$errorAmountPrefix}{$sellerTopup} : #0 : {$vndFormat}đ";
                            } else {
                                $topupMessage .= "UNMATCHED $msgIcon #{$topupTransaction->code} : {$errorAmountPrefix}{$errorAmount} : {$errorAmountPrefix}{$sellerTopup} : #0";
                            }
                            UserService::createTopupUnmatchTransaction($topupTransaction->id, $paymentId, $this->callbackMessage);
                            $embedDesc = [
                                [
                                    'description' => $topupMessage,
                                    'color' => ********
                                ]
                            ];
                            if (!$this->isDev) {
                                logToDiscord('', 'topup', false, true, 7, $embedDesc);
                            } else {
                                logToDiscordNowWithEmbeds('', 'topup_dev', false, $embedDesc);
                            }
                            TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'UNMATCHED_AMOUNT');
                            $errorMsg = "TopupIPNCallbackJob: IPN Callback {$callbackType} Error! - Not match amount {$topupTransactionAmount}";
                            $errorMsg .= "\n\n" . json_encode($this->callbackMessage);
                            graylogInfo($errorMsg, [
                                'category' => 'topup_errors',
                                'webhook_id' => $this->webhookLogId,
                                'user_type' => 'system',
                                'transaction_type' => $transactionType,
                                'version' => '202301208',
                                'action'  => 'job'
                            ]);
                        }
                    }
                } else if ($topupTransaction->status === UserTopupTransactionStatus::PROCESSING) {
                    TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'DUPLICATE');
                    $errorMsg = "TopupIPNCallbackJob: IPN Callback {$this->callbackType} - Has check status - DUPLICATE!";
                    $errorMsg .= "\n" . json_encode($this->callbackMessage);
                    $errorMsg .= "\n" . json_encode($topupTransaction->toArray());
                    graylogInfo($errorMsg, [
                        'category' => 'topup_errors',
                        'webhook_id' => $this->webhookLogId,
                        'user_type' => 'system',
                        'transaction_type' => $transactionType,
                        'version' => '202301208',
                        'action'  => 'job'
                    ]);
                }
            } else {
                TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'DUPLICATE');
                $errorMsg = "TopupIPNCallbackJob: IPN Callback {$this->callbackType} DUPLICATE!";
                $errorMsg .= "\n" . json_encode($this->callbackMessage);
                graylogInfo($errorMsg, [
                    'category' => 'topup_errors',
                    'webhook_id' => $this->webhookLogId,
                    'user_type' => 'system',
                    'transaction_type' => $transactionType,
                    'version' => '202301208',
                    'action'  => 'job'
                ]);
            }
        } catch (\Exception $e) {
            TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'ERROR_EXCEPTION');
            $this->callbackType = strtoupper($this->callbackType);
            graylogError("TopupIPNCallbackJob: IPN Callback {$this->callbackType} Error! - Message: {$e->getMessage()}", [
                'category' => 'topup_errors',
                'webhook_id' => $this->webhookLogId,
                'user_type' => 'system',
                'version' => '202301208',
                'user_id' => null,
                'action'  => 'job'
            ]);
        }
    }

    public function failed($exception): void
    {
        TopupWebhookLogs::updateStatusRow($this->webhookLogId, 'JOB_RUN_FAILED');
        $this->callbackType = strtoupper($this->callbackType);
        graylogError("TopupIPNCallbackJob: IPN Callback {$this->callbackType} Error! - Message: {$exception->getMessage()}", [
            'category' => 'topup_errors',
            'webhook_id' => $this->webhookLogId,
            'user_type' => 'system',
            'version' => '202301208',
            'user_id' => null,
            'action'  => 'job'
        ]);
    }
}
