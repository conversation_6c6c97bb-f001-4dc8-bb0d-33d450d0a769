<?php

namespace App\Jobs;

use App\Services\PsdRenderWorker;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendPsdRenderMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private array $params;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach ($this->params as $param) {
            $campaign_id = $param['campaign_id'] ?: 0;
            graylogInfo('[Render] Start campaign id: ' . $campaign_id . ', Mockup url: ' . $param['result_path'], [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'campaign_id' => $campaign_id,
            ]);
            $service = new PsdRenderWorker();
            $service->setDefaultLayer($param['layer_name']);
            $service->setPsdUrl($param['psd_url']);
            $service->setArtworkUrl($param['artwork_url']);
            $service->setWebhookUrl($param['webhook_url']);
            $service->setResultPath($param['result_path']);
            $service->run();
            graylogInfo('[Render] End campaign id: ' . $campaign_id . ', Mockup url: ' . $param['result_path'], [
                'category' => 'campaign_aop',
                'type' => 'webhook',
                'campaign_id' => $campaign_id,
            ]);
        }
    }
}
