<?php

namespace App\Jobs\Suppliers;

use App\Enums\QueueName;
use App\Providers\FulfillAPI\AbstractSyncVariantOOS;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;

class SyncVariantOOS implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;

    /**
     * @var Collection
     */
    private Collection $variants;
    private string $handler;
    private array $supplierConfig;

    public function __construct(Collection $variants, string $handler, array $supplierConfig)
    {
        $this->onQueue(QueueName::SYNC_SUPPLIER_OOS);
        $this->variants = $variants;
        $this->handler  = $handler;
        $this->supplierConfig = $supplierConfig;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $params = ['supplierConfig' => $this->supplierConfig, 'variants' => $this->variants];
            /** @var AbstractSyncVariantOOS $handler */
            $handler = app($this->handler, $params);
            $handler->handle();
        } catch (\Throwable $e) {
            // Skip this exception
        }
    }
}
