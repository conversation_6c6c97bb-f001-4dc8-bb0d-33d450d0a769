<?php

namespace App\Jobs;

use App\Enums\PaymentGatewayRefundStatusEnums;
use App\Enums\PaymentMethodEnum;
use App\Http\Controllers\Storefront\PaypalController;
use App\Http\Controllers\Storefront\StripeController;
use App\Http\Controllers\Storefront\TazapayController;
use App\Models\PaymentGatewayRefund;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use Throwable;

class ProcessRefundJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 1;

    /**
     * Delete the job if its models no longer exist.
     *
     * @var bool
     */
    public bool $deleteWhenMissingModels = true;

    /**
     * Refund instance
     *
     * @var PaymentGatewayRefund
     */
    protected PaymentGatewayRefund $refund;

    /**
     * Create a new job instance.
     *
     * @param PaymentGatewayRefund $refund
     * @return void
     */
    public function __construct(PaymentGatewayRefund $refund)
    {
        $this->refund = $refund;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $refundId = $this->refund->id;

        if (empty($this->refund->order) || $this->refund->status !== PaymentGatewayRefundStatusEnums::PROCESSING) {
            return;
        }

        $order = $this->refund->order;
        $paymentMethod = $order->payment_method;
        $fullRefund = $this->refund->is_full_refund;

        if ($paymentMethod === PaymentMethodEnum::PAYPAL || Str::contains($paymentMethod, PaymentMethodEnum::PAYPAL)) {
            $this->refundByPaypal($order, $refundId, $fullRefund);
        } else if ($paymentMethod === PaymentMethodEnum::STRIPE || Str::contains($paymentMethod, PaymentMethodEnum::STRIPE)) {
            $this->refundByStripe($order, $refundId, $fullRefund);
        } else if ($paymentMethod === PaymentMethodEnum::TAZAPAY || Str::contains($paymentMethod, PaymentMethodEnum::TAZAPAY)) {
            $this->refundByTazapay($order, $refundId, $fullRefund);
        } else {
            $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, "Unknown payment method");
        }
    }

    private function refundByPaypal($order, $refundId, $fullRefund = 0): void
    {
        $refundResponse = (new PaypalController())->refundOrderToGateway($order, $this->refund->refund_amount, $fullRefund);

        if (!empty($refundResponse) && $refundResponse['success']) {
            $this->updateRefund(PaymentGatewayRefundStatusEnums::COMPLETED);
        } else {
            $this->refund->status = PaymentGatewayRefundStatusEnums::ERROR;

            if (!empty($refundResponse) && $refundResponse['message']) {
                $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, "Error refund #$refundId: {$refundResponse['message']}");
                graylogError($refundResponse['message'], ['category' => 'process_refund']);
            } else {
                $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, 'Empty response from PayPal API');
                graylogError('Empty response from PayPal API', ['category' => 'process_refund']);
            }
        }
    }

    /**
     * @param $order
     * @param $refundId
     * @param $fullRefund
     * @return void
     */
    private function refundByStripe($order, $refundId, $fullRefund = 0): void
    {
        $refundResponse = (new StripeController())->refundOrderToGateway($order, $this->refund->refund_amount, $fullRefund);

        if (count($refundResponse) !== 0 && ($refundResponse['status'] === 'succeeded' || $refundResponse['status'] === 'pending')) {
            $log = '';
            if ($refundResponse['status'] === 'pending') {
                $log = 'Refund is received, but not yet completed, please check the actual status on Stripe dashboard';
            }
            $this->updateRefund(PaymentGatewayRefundStatusEnums::COMPLETED, $log);
        } else if (count($refundResponse) !== 0 && data_get($refundResponse, 'reason')) {
            $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, "Error refund #$refundId: {$refundResponse['reason']}");
            graylogError($refundResponse['reason'], ['category' => 'process_refund']);
        } else {
            $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, 'Empty response from Stripe API');
            graylogError('Empty response from Stripe API', ['category' => 'process_refund']);
        }
    }

    /**
     * @param $order
     * @param $refundId
     * @param $fullRefund
     * @return void
     */
    private function refundByTazapay($order, $refundId, $fullRefund = 0) {
        $refundResponse = (new TazapayController())->initRefund($order->id, $this->refund->refund_amount, $this->refund->reason, $fullRefund);
        if(!empty($refundResponse) && empty($refundResponse['errors'])) {
            $this->updateRefund(PaymentGatewayRefundStatusEnums::COMPLETED);
        } else if(!empty($refundResponse) && !empty($refundResponse['errors'])) {
            $errors = json_encode($refundResponse['errors']);
            $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, "Error refund #$refundId: {$errors}");
            graylogError("Error refund #$refundId: {$errors}", ['category' => 'process_refund']);
        } else {
            $error = json_encode($refundResponse['errors']);
            $this->updateRefund(PaymentGatewayRefundStatusEnums::ERROR, $error);
            graylogError($error, ['category' => 'process_refund']);
        }
    }

    /**
     * @param string $status
     * @param string $log
     * @return bool
     */
    private function updateRefund(string $status, string $log = ''): bool
    {
        return $this->refund->update([
            'status' => $status,
            'log' => $log
        ]);
    }

    /**
     * @param Throwable $exception
     */
    public function fail(Throwable $exception): void
    {
        $this->refund->update([
            'status' => PaymentGatewayRefundStatusEnums::ERROR,
            'log' => $exception->getMessage()
        ]);
        $refundId = $this->refund->id;
        graylogError("Error refund #$refundId: {$exception->getMessage()}", ['category' => 'process_refund']);

    }
}
