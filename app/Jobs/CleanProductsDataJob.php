<?php

namespace App\Jobs;

use App\Enums\FileTypeEnum;
use App\Enums\ProductStatus;
use App\Enums\UserRoleEnum;
use App\Models\File;
use App\Models\IndexProduct;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\ProductVariant;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Jobs\SyncSlugJob;

class CleanProductsDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $limit = 2000;
            // soft delete draft product after 7 days
            $baseProducts = IndexProduct::query()
                ->select(['id', 'slug', 'seller_id'])
                ->where('status', ProductStatus::DRAFT)
                ->where('updated_at', '<', now()->subDays(7))
                ->limit($limit)
                ->get();
            $draftIds = $baseProducts->pluck('id')->toArray();
            $products = Product::query()->where(function ($query) use ($draftIds) {
                $query->whereIn('id', $draftIds)->orWhereIn('campaign_id', $draftIds);
            })->where('status', ProductStatus::DRAFT);
            $products->update(['slug' => null, 'sync_status' => 0]);
            $products->delete();
            $indexProducts = IndexProduct::query()->where(function ($query) use ($draftIds) {
                $query->whereIn('id', $draftIds)->orWhereIn('campaign_id', $draftIds);
            });
            $indexProducts->update(['slug' => null, 'sync_status' => 0]);
            $indexProducts->delete();
            $baseProducts->whereNotNull('slug')->groupBy('seller_id')->each(function ($collection, $sellerId) {
                $elasticSearchIndex = get_env('ELATICSEARCH_INDEX', 'products');
                $seller = User::query()->find($sellerId);
                if ($seller) {
                    $elasticSearchIndex = $seller->getElasticSearchIndex();
                }
                SyncSlugJob::dispatchSync(ids: $collection->pluck('id')->toArray(), seller: $seller, isUpsert: false);
                (new Product())->elasticDeleteProductsByProductIds($collection->pluck('id')->toArray(), $elasticSearchIndex);
            });
            unset($baseProducts, $products, $indexProducts, $draftIds); // free ram
            $connections = User::query()->select('db_connection')->where('is_deleted', 0)->where('role', '!=', UserRoleEnum::CUSTOMER)->groupBy('db_connection')->get()->pluck('db_connection')->toArray();
            foreach ($connections as $connection) {
                $deletedProductIdsInOver20Mins = Product::query()
                    ->withTrashed()
                    ->on($connection)
                    ->select('id')
                    ->whereRaw('is_deleted = 1')
                    ->where('deleted_at', '<=', now()->subMinutes(5))
                    ->where('deleted_at', '>=', now()->subMinutes(25))
                    ->pluck('id')
                    ->toArray();
                $elasticSearchIndex = str_replace('mysql', 'products', $connection);
                foreach (array_chunk($deletedProductIdsInOver20Mins, 1000) as $campaignIds) {
                    (new Product())->elasticDeleteProductsByProductIds($campaignIds, $elasticSearchIndex);
                }
                unset($deletedProductIdsInOver20Mins); // free ram
                // delete all products set deleted_at < 30 days
                $deletedProductIds = Product::query()
                    ->withTrashed()
                    ->on($connection)
                    ->where('deleted_at', '<', now()->subDays(30))
                    ->limit($limit)
                    ->pluck('id')
                    ->toArray();
                if (count($deletedProductIds) === 0) {
                    continue;
                }
                Product::query()
                    ->withTrashed()
                    ->on($connection)
                    ->whereIn('id', $deletedProductIds)
                    ->forceDelete();
                ProductVariant::query()
                    ->on($connection)
                    ->whereIn('product_id', $deletedProductIds)
                    ->orWhereIn('campaign_id', $deletedProductIds)
                    ->delete();
                $filesNeedToDelete = File::query()
                    ->on($connection)
                    ->select(['file_url', 'file_url_2'])
                    ->whereIn('campaign_id', $deletedProductIds)
                    ->where('type', FileTypeEnum::DESIGN)
                    ->get();
                if ($filesNeedToDelete->isNotEmpty()) {
                    $fileUrls = $filesNeedToDelete->pluck('file_url')->unique()->filter(fn($url) => !empty($url))->values();
                    $fileUrls2 = $filesNeedToDelete->pluck('file_url_2')->unique()->filter(fn($url) => !empty($url))->values();
                    $fileUrls = $fileUrls->merge($fileUrls2);
                    DeleteProductFileOnStorageJob::dispatch($fileUrls->toArray());
                }
                foreach (array_chunk($deletedProductIds, 100) as $deletedProductIdsChunk) {
                    File::query()
                        ->on($connection)
                        ->withoutGlobalScope('getActive')
                        ->where(function ($query) use ($deletedProductIdsChunk) {
                            $query->whereIn('campaign_id', $deletedProductIdsChunk)
                                ->orWhereIn('product_id', $deletedProductIdsChunk);
                        })
                        ->delete();
                }
                if (count($deletedProductIds) > 0) {
                    logToDiscord("[{$connection}]: Deleted total " . count($deletedProductIds) . " products.", 'job');
                    graylogInfo("[{$connection}]: Deleted total " . count($deletedProductIds) . " products.", [
                        'category' => 'cleanup',
                        'connection' => $connection,
                        'deleted_product_ids' => $deletedProductIds,
                    ]);
                }
                if ($connection !== 'mysql') {
                    unset($deletedProductIds); // free ram
                    // soft delete draft product after 7 days
                    $baseProducts = Product::query()
                        ->on($connection)
                        ->select(['id', 'slug', 'seller_id'])
                        ->where('status', ProductStatus::DRAFT)
                        ->where('updated_at', '<', now()->subDays(7))
                        ->limit($limit)
                        ->get();

                    $draftIds = $baseProducts->pluck('id')->toArray();
                    $products = Product::query()
                        ->on($connection)
                        ->whereIn('id', $draftIds)
                        ->orWhereIn('campaign_id', $draftIds);
                    $products->update(['slug' => null, 'sync_status' => 0]);
                    $products->delete();
                    $baseProducts->whereNotNull('slug')->groupBy('seller_id')->each(function ($collection, $sellerId) {
                        $elasticSearchIndex = get_env('ELATICSEARCH_INDEX', 'products');
                        $seller = User::query()->find($sellerId);
                        if ($seller) {
                            $elasticSearchIndex = $seller->getElasticSearchIndex();
                        }
                        SyncSlugJob::dispatchSync(ids: $collection->pluck('id')->toArray(), seller: $seller, isUpsert: false);
                        (new Product())->elasticDeleteProductsByProductIds($collection->pluck('id')->toArray(), $elasticSearchIndex);
                    });
                    unset($baseProducts, $products, $draftIds); // free ram
                    continue;
                }
                // Delete on master connection
                ProductCollection::query()
                    ->whereIn('product_id', $deletedProductIds)
                    ->delete();
                IndexProduct::query()
                    ->withTrashed()
                    ->whereIn('id', $deletedProductIds)
                    ->forceDelete();
                unset($deletedProductIds); // free ram
            }
        } catch (\Throwable $e) {
            logException($e);
        }
    }
}
