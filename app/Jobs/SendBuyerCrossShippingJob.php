<?php

namespace App\Jobs;

use App\Actions\Commons\SendBuyerCrossShippingAction;
use App\Enums\QueueName;
use App\Models\Order;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrders;
use Throwable;

class SendBuyerCrossShippingJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public const MAIL_NOTIFICATION_KIND = 'mail';

    public const SMS_NOTIFICATION_KIND = 'sms';

    private string $kind;

    private Order|RegionOrders $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order|RegionOrders $order, string $kind = self::MAIL_NOTIFICATION_KIND)
    {
        $this->onQueue(QueueName::ORDER_EVENTS);
        $this->kind = $kind;
        $this->order = $order;
    }

    /**
     * @return int
     */
    public function uniqueId(): int
    {
        return $this->order->id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        /** @var SendBuyerCrossShippingAction $action */
        $action = app(SendBuyerCrossShippingAction::class);
        $action->handle($this->order,$this->kind);
    }

    /**
     * The job failed to process.
     * @param \Exception $exception
     * @return void
     */
    public function failed(?Throwable $exception)
    {
        $orderId = is_null($this->order) ? 'null' : $this->order->id;
        graylogError('SendBuyerCrossShippingJob failed', [
            'order_id' => $orderId,
            'kind' => $this->kind,
        ]);
    }
}
