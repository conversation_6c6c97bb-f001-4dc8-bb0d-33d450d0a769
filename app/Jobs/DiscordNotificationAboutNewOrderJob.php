<?php

namespace App\Jobs;

use App\Enums\CurrencyEnum;
use App\Enums\PaymentMethodEnum;
use App\Models\Order;
use App\Services\UserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class DiscordNotificationAboutNewOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $order = $this->order;
        $totalPaid = formatCurrency($order->total_paid, 'USD', 'en-US');
        $totalPaidInOtherCurrency = '';
        if ($order->currency_code !== CurrencyEnum::USD) {
            $totalPaidInOtherCurrency = UserService::formatPrice($order->total_paid, $order->getCurrencyCode(), $order->getCurrencyRate());
        }

        if ($totalPaid === '0.00') {
            return;
        }

        $prefixEmoji = ':tada:';

        // :pp: will not work because it is a custom emoji
        // we need to get formatted code of custom emoji:
        // send \:pp: to any discord channel and get formatted code
        // https://github.com/Rapptz/discord.py/issues/390#issuecomment-625929794
        if ($order->payment_method === PaymentMethodEnum::PAYPAL) {
            $prefixEmoji = '<:pp:879061884383408180>';
        } elseif (Str::startsWith($order->payment_method, PaymentMethodEnum::STRIPE)) {
            $prefixEmoji = '<:stripe:999696140679262270>';
        }

        $message = $prefixEmoji . ' ' . "[$order->order_number](https://admin.senprints.com/order/detail/{$order->id})";
        $message .= ' : **$' . $totalPaid . '**';
        if (!empty($totalPaidInOtherCurrency)) {
            $message .= ' : **' . $totalPaidInOtherCurrency . '**';
        }
        $message .= ' : ' . ucfirst($order->payment_method);
        $message .= ' : ||' . $order->customer_name;
        $message .= ' : ' . $order->customer_email;
        $message .= ' : ' . self::cleanPhone($order->customer_phone) . '||';
        $message .= ' : ' . $order->country . ' :flag_' . strtolower($order->country) . ':';
        logToDiscord($message, 'order');
    }

    private static function cleanPhone($phone)
    {
        if (empty($phone)) {
            return 'None';
        }

        // (*************
        // +84 012 456 789
        return str_replace(['(', ')', '-', ' ', '+'], '', $phone);
    }
}
