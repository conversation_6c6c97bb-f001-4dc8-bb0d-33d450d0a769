<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Models\SellerCollection;
use App\Models\Slug;
use App\Models\SystemConfig;
use App\Models\Upsell;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class UpdateSellerIdUpsellJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $seller;

    private $type;

    public function __construct($type = 'seller_sharding', $seller = null)
    {
        $this->type = $type;
        $this->seller = $seller;
        $this->onQueue('default');
    }

    /**s
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            SystemConfig::setConfig(CacheKeys::UPDATE_SELLER_ID_UPSELL, array(
                'value' => Carbon::now()->toDateTimeString(),
            ));
            if ($this->type === 'seller_sharding') {
                $this->updateSellerSharding();
            }
            if ($this->type === 'seller_master') {
                $this->updateSellerMaster();
            }
            SystemConfig::setConfig(CacheKeys::UPDATE_SELLER_ID_UPSELL, array(
                'value' => null,
            ));
        }
        catch (\Exception $e) {
            logException($e);
        }
    }

    private function updateSellerSharding()
    {
        $sellerCollections = SellerCollection::query()
            ->select('collection_id')
            ->where('seller_id', $this->seller->id)
            ->get()
            ->pluck('collection_id')
            ->toArray();
        foreach (array_chunk($sellerCollections, 1000) as $collection) {
            Upsell::query()
                ->select('id', 'product_id')
                ->when($this->seller->id === 103001, function ($query) {
                    $query->where('product_id', '>=', '374382016');
                },
                    function ($query) {
                        $query->where('product_id', '>=', '420600199');
                    })
                ->whereIn('upsell_collection_id', $collection)
                ->chunkById(1000, function ($chunk) {
                    $arrChunkId = array_unique($chunk->pluck('product_id')->toArray());
                    $oldIds = Slug::query()
                        ->select(['campaign_id', DB::raw('COUNT(seller_id) as count_seller')])
                        ->whereIn('campaign_id', $arrChunkId)
                        ->groupBy('campaign_id')
                        ->having('count_seller', '=', 1)
                        ->pluck('campaign_id')
                        ->toArray();
                    $updateIds = Slug::query()
                        ->select(['campaign_id as product_id', 'seller_id'])
                        ->whereIn('campaign_id', $oldIds)
                        ->get()
                        ->toArray();
                    batch()->update(new Upsell([], true), $updateIds, 'product_id');
                });
        }
    }

    private function updateSellerMaster()
    {
        Upsell::query()
            ->whereNull('seller_id')
            ->where('upsell_collection_id', '>', 0)
            ->chunkById(1000, function ($upsells) {
                $updateIds = Slug::query()->select(['campaign_id as product_id', 'seller_id'])->whereIn('campaign_id', $upsells->pluck('product_id'))
                    ->whereNotIn('seller_id', ['103001', '1054640', '131658'])
                    ->when(!empty($this->seller), fn ($query) =>$query->whereIn('seller_id', $this->seller))
                    ->get()
                    ->toArray();
                batch()->update(new Upsell([], true), $updateIds, 'product_id');
            });
    }
}
