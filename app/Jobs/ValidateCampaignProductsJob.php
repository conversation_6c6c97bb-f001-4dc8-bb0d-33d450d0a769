<?php

namespace App\Jobs;

use App\Enums\CacheKeys;
use App\Enums\ProductStatus;
use App\Enums\ProductType;
use App\Enums\QueueName;
use App\Models\Product;
use App\Services\UserService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

class ValidateCampaignProductsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public $campaignId, public $sellerId)
    {
        $this->onQueue(QueueName::BULK_CAMPAIGN);
    }

    /**
     * @return void
     * @throws \Throwable
     */
    public function handle(): void
    {
        $seller = UserService::getSellerSharding($this->sellerId);
        if (!$seller) {
            return;
        }
        $campaign = Product::query()
            ->onSellerConnection($seller)
            ->whereKey($this->campaignId)
            ->where('product_type', ProductType::CAMPAIGN)
            ->where('status', ProductStatus::ACTIVE)
            ->whereIn('system_type', [ProductSystemTypeEnum::REGULAR, ProductSystemTypeEnum::MOCKUP, ProductSystemTypeEnum::CUSTOM, ProductSystemTypeEnum::AOP, ProductSystemTypeEnum::AI_MOCKUP])
            ->first();
        if (!$campaign) {
            return;
        }
        $products = Product::query()
            ->withTrashed()
            ->onSellerConnection($seller)
            ->select(['id', 'campaign_id', 'template_id', 'deleted_at', 'status', 'sync_status'])
            ->where('campaign_id', $this->campaignId)
            ->orderBy('deleted_at')
            ->groupBy('template_id')
            ->get();
        if ($products->isEmpty()) {
            return;
        }
        $activeProducts = $products->filter(function (Product $product) {
            return empty($product->deleted_at) && $product->status === ProductStatus::ACTIVE;
        });
        if ($activeProducts->count() > 0) {
            return;
        }
        $deletedProducts = $products->filter(function (Product $product) {
            return !empty($product->deleted_at) && $product->status === ProductStatus::ACTIVE;
        });
        if ($deletedProducts->count() === 0) {
            return;
        }
        $productIds = $deletedProducts->pluck('id')->toArray();
        $updated = Product::query()
            ->withTrashed()
            ->onSellerConnection($seller)
            ->whereIn('id', $productIds)
            ->update([
                'is_deleted' => 0,
                'deleted_at' => null
            ]);
        if ($updated > 0) {
            try {
                (new SyncProductsToElasticSearchJob())->syncProductsToElasticSearch($this->campaignId, sellerId: $seller->id);
            } catch (\Throwable $e) {
                Product::query()->onSellerConnection($seller)->whereIn('id', $productIds)->update([
                    'sync_status' => Product::SYNC_DATA_STATS_ENABLED,
                ]);
            }
            // Clear cache
            $cacheKey = CacheKeys::getProductCacheKey($campaign->slug);
            cache()->forget($cacheKey);
            cacheAlt()->forget($cacheKey);
            $cacheKeys = [];
            $cacheKeys[] = $cacheKey;
            syncClearCache($cacheKeys);
            syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
        }
    }
}
