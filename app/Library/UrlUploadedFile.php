<?php

namespace App\Library;

use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class UrlUploadedFile extends UploadedFile
{
    /**
     * @throws Exception
     */
    public static function createFileFromUrl(string $url, $fileType = 'image', $exception = false)
    {
        try {
            if (empty($url)) {
                return null;
            }
            $mimeType = null;
            $host = parse_url($url, PHP_URL_HOST);
            $tempFile = tempnam(sys_get_temp_dir(), 'url-file-');
            if (str_contains($host, 'google.com')) {
                $client = new \Google_Client();
                $client->setAuthConfig(storage_path('google-service-account-credentials.json'));
                $client->addScope(\Google_Service_Drive::DRIVE);
                $service = new \Google_Service_Drive($client);
                $fileId = self::extractFileIdFromGoogleDriveUrl($url);
                if (empty($fileId)) {
                    return null;
                }
                $content = $service->files->get($fileId, array("alt" => "media"));
                $stream = fopen($tempFile, "w+");
                while (!$content->getBody()->eof()) {
                    fwrite($stream, $content->getBody()->read(1024));
                }
            } else {
                $header = "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/50.0.2661.102 Safari/537.36\r\n";
                if (str_contains($url, '.cloudimgs.net')) {
                    $header = "User-Agent: SP-Crawler/1.0\r\n";
                    $header .= "Referer: https://crawl.senprints.com/\r\n";
                }
                $context  = stream_context_create(array(
                    "http" => array(
                        "method" => "GET",
                        "header" => $header
                    )
                ));
                if (!$stream = fopen($url, 'rb', false, $context)) {
                    return null;
                }
                file_put_contents($tempFile, $stream);
                if (str_contains($header, '.senprints.com') && preg_match('/\.(jpe?g|png|gif|webp)\b/i', $url, $matches)) {
                    $fileExt = Str::lower($matches[1]);
                    $mimeType = 'image/' . $fileExt;
                }
            }
            if (empty($fileExt)) {
                $mimeType = mime_content_type($tempFile);
                $fileExt = mime2ext($mimeType);
            }
            fclose($stream);
            $allowFileExt = ['jpg', 'png', 'jpeg'];
            if ($fileType === 'design' && !in_array($fileExt, $allowFileExt, true)) {
                self::logToDiscord('[' . currentTime() . '] - UrlUploadedFile->createFileFromUrl -> Url: ' . $url . ' -> File extension is not valid: ' . $fileExt . ', File type: ' . $fileType);
                return null;
            }
            // get hash of file to generate file name
            $name = md5($url . time());
            $fileName = $name . '.' . $fileExt;
            if ($fileExt === 'webp') {
                $mimeType = 'jpg';
                $tempDestinationFile = tempnam(sys_get_temp_dir(), 'url-file-from-webp-');
                $tempFile = self::convertWebpToNewFormat($tempFile, $tempDestinationFile, $mimeType);
                if ($tempFile === null) {
                    self::logToDiscord('[' . currentTime() . '] - UrlUploadedFile->createFileFromUrl -> Url: ' . $url . ' -> Can not created file webp to ' . $mimeType . ', File type: ' . $fileType);
                    return null;
                }
            }
            return new static($tempFile, $fileName, $mimeType);
        } catch (\Throwable $e) {
            self::logToDiscord('Code: ' - $e->getCode() . ' - UrlUploadedFile->createFileFromUrl -> Url: ' . $url . ', File type: ' . $fileType . '. Message: ' . $e->getMessage() . ', File: ' . $e->getFile() . ', Line: ' . $e->getLine(), true);
            return $exception ? new Exception($e->getMessage()) : null;
        }
    }

    /**
     * @param $source
     * @param string $destination
     * @param string $format
     * @param int $quality
     * @return string|null
     */
    public static function convertWebpToNewFormat($source, string $destination, string $format = 'jpg', $quality = 100)
    {
        if (!file_exists($source)) {
            return null;
        }
        if (file_exists($destination)) {
            return $destination;
        }
        $format = strtolower($format);
        try {
            if (function_exists('imagecreatefromwebp')) {
                $image = imagecreatefromwebp($source);
                if (function_exists('imagejpeg') && in_array($format, ['jpg', 'jpeg'], true)) {
                    $result = imagejpeg($image, $destination, $quality);
                }
                if (function_exists('imagepng') && $format === 'png') {
                    $result = imagepng($image, $destination, $quality);
                }
                if (empty($result)) {
                    return null;
                }
                imagedestroy($image);
                return $destination;
            }
            if (class_exists('Imagick')) {
                $imagick = new \Imagick();
                $imagick->readImage($source);
                $imagick->setImageFormat($format);
                $imagick->writeImage($destination);
                $imagick->destroy();
                return $destination;
            }
        } catch (\Throwable | \ImagickException $e) {
            self::logToDiscord($e->getCode() . 'UrlUploadedFile->convertWebpToNewFormat -> Source: ' . $source . ', Format: ' . $format . '. Message: ' . $e->getMessage() . ', File: ' . $e->getFile() . ', Line: ' . $e->getLine(), true);
            return null;
        }
        return null;
    }

    /**
     * @throws \Exception
     */
    public static function extractFileIdFromGoogleDriveUrl($url): string
    {
        if (preg_match('/\?id=([^&]+)/', $url, $matches)) {
            return $matches[1];
        }
        if (preg_match('%drive\.google\.com/file/[a-z]/([^/]+)/(edit|view|preview)%', $url, $matches)) {
            return $matches[1];
        }
        throw new \RuntimeException('Invalid Google Drive file URL');
    }

    /**
     * @param $message
     * @param bool $debug
     * @return void
     */
    private static function logToDiscord($message, bool $debug = false): void
    {
        logToDiscord($message, 'upload_file_failed', $debug);
    }
}
