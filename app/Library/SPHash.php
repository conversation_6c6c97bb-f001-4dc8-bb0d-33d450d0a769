<?php

namespace App\Library;

/**
 * Link: https://gist.github.com/LogIN-/e451ab0e8738138bc60b
 * Class SPHash
 * @package App\Library
 */
class SPHash
{

    protected $secret = '';
    protected $hash = '';
    protected $result = '';

    function __construct($secret = '')
    {
        $this->secret = $secret;
    }

    public function encrypt($string, $secret = ''): ?string
    {
        [$secretHash, $secretLength] = $this->parseKey($secret);
        $strLen = strlen($string);
        $j = 0;
        try {
            for ($i = 0; $i < $strLen; $i++) {
                $ordStr = ord(substr($string, $i, 1));
                if ($j == $secretLength) {
                    $j = 0;
                }
                $ordKey = ord(substr($secretHash, $j, 1));
                $j++;
                $this->hash .= strrev(base_convert(dechex($ordStr + $ordKey), 16, 36));
            }
            return $this->hash;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function decrypt($string, $secret = ''): ?string
    {
        $string = strtolower($string);
        [$secretHash, $secretLength] = $this->parseKey($secret);
        $strLen = strlen($string);
        $j = 0;
        try {
            for ($i = 0; $i < $strLen; $i += 2) {
                $ordStr = hexdec(base_convert(strrev(substr($string, $i, 2)), 36, 16));
                if ($j == $secretLength) {
                    $j = 0;
                }
                $ordKey = ord(substr($secretHash, $j, 1));
                $j++;
                $this->result .= chr($ordStr - $ordKey);
            }
            return $this->result;
        } catch (\Exception $e) {
            return null;
        }
    }

    private function parseKey($secret = ''): array
    {
        if (empty($secret)) {
            $secret = $this->secret;
        }

        $secret = sha1('senprints' . md5($secret));
        return [$secret, strlen($secret)];
    }
}
