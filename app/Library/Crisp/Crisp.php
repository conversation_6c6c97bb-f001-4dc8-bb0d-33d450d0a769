<?php

namespace App\Library\Crisp;

use App\Models\Order;
use App\Services\FreshDesk;
use <PERSON>risp\CrispClient;
use <PERSON>risp\CrispException;
use GuzzleHttp\Client as GuzzleClient;
use Http\Adapter\Guzzle7\Client as GuzzleAdapter;
use Psr\Http\Client\ClientExceptionInterface;

class Crisp
{
    // Should we create a backup ticket in FreshDesk?
    private const CREATE_BACKUP_TICKET = false;

    /**
     *
     * @var CrispClient
     */
    public $crispClient;

    /**
     *
     * @var string
     */
    public $websiteId;
    public $name, $email, $subject, $content, $cc, $segments, $data, $ip, $links, $sessionId;
    public $domain;
    public $storeName;
    public $storeId;
    public $sellerEmail;
    public $orderNumber;
    public $orderStatusUrl;
    public $isCustomStore = false;

    /**
     * Initialize Crisp client with authentication credentials and HTTP configuration
     * Sets up Guzzle HTTP client with timeout, proxy settings, and custom headers
     * Authenticates with Crisp API using provided identifier and key
     *
     * @param string $websiteId The Crisp website identifier
     * @param string $identifier The API authentication identifier
     * @param string $key The API authentication key
     */
    public function __construct($websiteId, $identifier, $key)
    {
        $guzzleClient = new GuzzleClient([
            'timeout' => 15,
            'proxy' => null,
            'headers' => [
                'X-Sender' => 'SenPrints',
            ]
        ]);
        $client = new GuzzleAdapter($guzzleClient);
        $this->websiteId = $websiteId;
        $this->crispClient = new CrispClient($client);
        $this->crispClient->setTier("plugin");
        $this->crispClient->authenticate($identifier, $key);
    }

    /**
     * Send an automated response message to the customer in the chat conversation
     * Creates a text message from operator with auto-reply content including order tracking link
     * and store policy links
     *
     * @return mixed The result of sending the message through Crisp API
     * @throws CrispException|ClientExceptionInterface If the message fails to send
     */
    public function sendAutoResponse()
    {
        $message = [
            "type" => "text",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => self::convertBinaryToString($this->getAutoResponseMessage()),
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    /**
     * Convert binary data to UTF-8 encoded string for safe transmission
     * Ensures proper encoding for international characters and special symbols
     *
     * @param mixed $binary The binary data to convert
     * @return string The UTF-8 encoded string
     */
    private static function convertBinaryToString($binary)
    {
        return json_decode(json_encode(mb_convert_encoding($binary, 'UTF-8', 'UTF-8')));
    }

    /**
     * Generate the automated response message content for customer support
     * Creates a personalized message with order tracking link and store policy links
     * Includes customer name, order status URL, and various policy page links
     *
     * @return string The formatted auto-response message content
     */
    public function getAutoResponseMessage()
    {
        $message = <<<EOF
        Hi {$this->name},

        Thanks so much for reaching out!
        You can track your order status via this link below:
        [$this->orderStatusUrl]({$this->orderStatusUrl})

        Please read the articles below if you need any additional information.

        [Return Policy]({$this->createLink("/page/return-policy")})
        [Shipping Policy]({$this->createLink("/page/shipping-policy")})
        [Terms & Conditions]({$this->createLink("/page/terms-of-service")})
        [Privacy Policy]({$this->createLink("/page/privacy")})
        [DMCA]({$this->createLink("/page/dmca")})

        If you have any additional information that you think will help us to assist you, please feel free to reply to this email. We look forward to chatting soon, our response within 24-48 business hours.

        Regards,

        The {$this->storeName} Team
        EOF;
        return $message;
    }

    /**
     * Create a full URL by combining the store domain with a given path
     * Generates absolute URLs for store pages like policies, terms, etc.
     *
     * @param string $path The relative path to append to the domain
     * @return string The complete URL with HTTPS protocol
     */
    public function createLink($path): string
    {
        return "https://{$this->domain}$path";
    }

    /**
     * Send a text message from user to the Crisp conversation
     * Creates and sends a user-originated message with the current content
     *
     * @return mixed The result of sending the message through Crisp API
     * @throws CrispException|ClientExceptionInterface If the message fails to send
     */
    public function sendMessage()
    {
        $message = [
            "type" => "text",
            "from" => "user",
            "origin" => "chat",
            "content" => self::convertBinaryToString($this->content),
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    /**
     * Send an internal note with store and order information to the conversation
     * Creates an operator note containing seller email, order number, store details,
     * and custom store status for support team reference
     *
     * @return mixed The result of sending the note through Crisp API
     * @throws CrispException|ClientExceptionInterface If the note fails to send
     */
    public function sendNote()
    {
        $isCustomStore = "no";

        if ($this->isCustomStore) {
            $isCustomStore = "yes";
        }

        $note = <<<EOF
        Seller Email: {$this->sellerEmail}
        Order Number: {$this->orderNumber}
        Store Domain: {$this->domain}
        Store Name: {$this->storeName}
        Store ID: {$this->storeId}
        Is Custom Store: {$isCustomStore}
        EOF;

        $message = [
            "type" => "note",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => $note,
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    /**
     * Send a custom internal note with specified content to a specific conversation
     * Allows sending arbitrary note content to any conversation session
     *
     * @param string $sessionId The conversation session ID to send the note to
     * @param string $content The note content to send
     * @return mixed The result of sending the note through Crisp API
     * @throws CrispException|ClientExceptionInterface If the note fails to send
     */
    public function sendCustomNote(string $sessionId, string $content)
    {
        $message = [
            "type" => "note",
            "from" => "operator",
            "origin" => "chat",
            "user" => [
                "type" => "website",
                "nickname" => "Auto-reply",
                "avatar" => "https://image.crisp.chat/avatar/website/c17a453b-7e4b-4914-9cb6-747a900f787f/144"
            ],
            "content" => $content,
        ];

        return $this->crispClient->websiteConversations->sendMessage($this->websiteId, $sessionId, $message);
    }

    /**
     * Retrieve conversation segments (tags/categories) for a specific session
     * Gets metadata segments used for conversation categorization and routing
     *
     * @param string $sessionId The conversation session ID to get segments for
     * @return array Array of segment strings, empty array if none found or on error
     * @throws ClientExceptionInterface
     */
    public function getSegments(string $sessionId): array
    {
        try {
            $result = $this->crispClient
                ->websiteConversations
                ->getMeta($this->websiteId, $sessionId);

            return data_get($result, 'segments', []);
        } catch (CrispException $e) {
            logException($e);
            return [];
        }
    }

    /**
     * Update conversation segments (tags/categories) for a specific session
     * Sets new segments for conversation categorization and routing purposes
     *
     * @param string $sessionId The conversation session ID to update segments for
     * @param array $segment Array of segment strings to set for the conversation
     * @return void
     * @throws ClientExceptionInterface
     */
    public function updateSegment(string $sessionId, array $segment): void
    {
        try {
            $this->crispClient
                ->websiteConversations
                ->updateMeta($this->websiteId, $sessionId, [
                    'segments' => $segment
                ]);
        } catch (CrispException $e) {
            logException($e);
        }
    }

    /**
     * Send conversation transcript via email to specified recipient
     * Handles platform-specific client selection and sends transcript to operator email
     *
     * @param string|null $to Email address to send transcript to (optional, uses $this->cc if not provided)
     * @param string|null $sessionId Conversation session ID (optional, uses $this->sessionId if not provided)
     * @return array|null Response from transcript API or empty array if required parameters missing
     * @throws \Throwable|ClientExceptionInterface If JSON encoding fails
     */
    public function sendTranscriptConversation($to = null, $sessionId = null): ?array
    {
        if ($to) {
            $this->cc = $to;
        }
        if ($sessionId) {
            $this->sessionId = $sessionId;
        }
        if (empty($this->cc) || empty($this->sessionId)) {
            return [];
        }
        if (stripos(PHP_OS_FAMILY, 'WIN') === 0) {
            $client = $this->crispClient->_rest;
        } else {
            $client = $this->crispClient;
        }
        $result = $client->post("website/$this->websiteId/conversation/{$this->sessionId}/transcript", json_encode([
            "to" => "operator",
            "email" => $this->cc
        ], JSON_THROW_ON_ERROR));
        return json_decode($result->getBody(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * Process and send contact form data to create a new Crisp conversation
     * Validates store information, formats message content, creates segments,
     * optionally creates backup FreshDesk ticket, and starts conversation
     *
     * @param array $data Contact form data including customer info, message, store details, and order information
     * @return array Result containing session ID and send results
     * @throws \Exception|ClientExceptionInterface If store validation fails
     */
    public function sendContactForm(array $data): array
    {
        try {
            // Validate store information based on order number or customer email
            $data = $this->validateAndCorrectStoreInfo($data);
        } catch (\Exception $e) {
            logException($e);
        }

        $this->email = $data['customer_email'];
        $this->name = $data['customer_name'] ?: $this->email;
        $this->content .= $data['message'];
        $this->segments = self::createSegment($data['subject']);

        if (!empty($data['segments'])) {
            $this->segments = array_merge($this->segments, $data['segments']);
        }

        $prefixSubject = $data['subject'] ?: "Contact Form";
        $this->subject = $prefixSubject . " - " . substr($this->content, 0, 64);

        $this->subject = str_replace(PHP_EOL, '', $this->subject);

        if (strlen($this->content) > 64) {
            $this->subject .= "...";
        }

        $this->domain = $data['store_domain'];

        if (isset($data['attached_files']) && is_array($data['attached_files'])) {
            $this->links = array_filter($data['attached_files']);
        }

        $this->cc = $data['cc'];
        $this->storeName = $data['store_name'];
        $this->storeId = $data['store_id'];
        $this->sellerEmail = $data['seller_email'];
        $this->orderNumber = $data['order_number'];
        $this->orderStatusUrl = !empty($data['order_status_url']) ? $data['order_status_url'] : $this->createLink('/order/track');
        $this->isCustomStore = (int)$data['is_custom_store'] === 1;

        $this->content = <<<EOF
        **{$this->subject}**

        {$this->content}

        ===============================
        Name: {$this->name}
        Email: {$this->email}
        Order Number: {$data['order_number']}
        Store Domain: {$data['store_domain']}
        EOF;

        $this->data = array_filter([
            "order_id" => $data['order_number'],
            "seller_id" => $data['seller_id'],
            "domain" => $data['store_domain'],
            "store_id" => $data['store_id'],
            "custom_store" => $data['is_custom_store'],
            "seller_email" => $data['seller_email']
        ]);

        if (self::CREATE_BACKUP_TICKET) {
            // disable cc_emails because Crisp already has cc_emails
            FreshDesk::createTicket([
                "description" => $this->content,
                "subject" => $this->subject,
                "email" => $this->email,
                "priority" => 1,
                "status" => 2,
                "name" => $this->name,
                "tags" => $this->segments,
            ]);
        }

        return $this->startConversation();
    }

    /**
     * Validates and corrects store information based on order number or customer email
     * If the customer or order doesn't belong to the specified store/seller,
     * update the information to the correct store/seller
     *
     * @param array $data
     * @return array
     */
    private function validateAndCorrectStoreInfo(array $data): array
    {
        // Skip validation if order number or customer email is not provided
        if (empty($data['order_number']) && empty($data['customer_email'])) {
            return $data;
        }

        try {
            $orderInfo = null;

            // First try to validate by order number if available
            if (!empty($data['order_number'])) {
                $orderInfo = Order::query()->where('order_number', $data['order_number'])->first();
            }

            // If order not found by order number, try by customer email
            if (!$orderInfo && !empty($data['customer_email'])) {
                $orderInfo = Order::query()->where('customer_email', $data['customer_email'])
                    ->when(!empty($data['store_domain']), function ($query) use ($data) {
                        return $query->where('store_domain', $data['store_domain']);
                    })
                    ->orderBy('created_at', 'desc')
                    ->first();
            }

            // If order found and store info doesn't match, update store data
            if ($orderInfo && (int)$orderInfo->store_id !== (int)$data['store_id']) {
                // Get correct store information using relationship
                $storeInfo = $orderInfo->store;

                if ($storeInfo) {
                    // Get seller information for the correct store using relationship
                    $sellerInfo = $storeInfo->user;

                    if ($sellerInfo) {
                        // Update data with the correct store and seller information
                        $data['store_id'] = $storeInfo->id;
                        $data['store_domain'] = $storeInfo->domain;
                        $data['store_name'] = $storeInfo->name;
                        $data['is_custom_store'] = $storeInfo->is_custom_store;
                        $data['seller_id'] = $sellerInfo->id;
                        $data['seller_email'] = $sellerInfo->email;
                        $data['cc'] = $storeInfo->cc_email ?: $sellerInfo->email;

                        // If order status URL was provided, update it with the correct domain
                        if (!empty($data['order_status_url'])) {
                            $path = parse_url($data['order_status_url'], PHP_URL_PATH);
                            $data['order_status_url'] = "https://{$storeInfo->domain}{$path}";
                        }

                        logToDiscord('Crisp correction: ' . json_encode([
                            'customer_email' => $data['customer_email'],
                            'order_number' => $data['order_number'],
                            'original_store_domain' => $data['store_domain'],
                            'corrected_store_domain' => $storeInfo->domain,
                            'original_seller_email' => $data['seller_email'],
                            'corrected_seller_email' => $sellerInfo->email
                        ], JSON_THROW_ON_ERROR));
                    }
                }
            }
        } catch (\Exception $e) {
            logException($e);
        }

        return $data;
    }

    /**
     * Create conversation segments (tags) based on the contact form subject
     * Converts subject text to lowercase, replaces special characters, and creates
     * standardized segment tags for conversation categorization
     *
     * @param string $subject The contact form subject line
     * @return array Array of segment strings including 'contact_form' and processed subject
     */
    private static function createSegment($subject): array
    {
        $subject = self::convertBinaryToString($subject);

        $segments = ["contact_form"];
        if ($subject) {
            $newSegment = strtolower($subject);
            $newSegment = str_replace([" ", "?", "/"], ["_", "", "_"], $newSegment);
            $newSegment = trim($newSegment);
            $segments[] = $newSegment;
        }
        return $segments;
    }

    /**
     *  Initialize and start a new Crisp conversation with all necessary setup
     *  Creates session, updates metadata, adds CC emails, sends initial message, and attaches files
     *
     * @return array Result containing session ID, message send result, and CC add result
     * @throws CrispException|ClientExceptionInterface If any of the conversation setup steps fail
     */
    public function startConversation(): array
    {
        $this->initSession();
        if ($this->sessionId) {
            $this->updateConversationMeta();
            $addCCResult = $this->addCcEmail();
            $messageResult = $this->sendMessage();
            $this->sendLinks();
            return ["sessionId" => $this->sessionId, "sendResult" => $messageResult, "addCCResult" => $addCCResult];
        }
        return [];
    }

    /**
     * Initialize a new Crisp conversation session
     * Creates a new conversation and initiates it for the website
     * Sets the session ID for subsequent operations
     *
     * @return void
     */
    public function initSession(): void
    {
        try {
            $websiteConversation = $this->crispClient->websiteConversations->create($this->websiteId);
            $this->sessionId = $websiteConversation['session_id'];
            $this->crispClient->websiteConversations->initiateOne($this->websiteId, $this->sessionId);
        } catch (\Throwable $e) {
        }
    }

    /**
     * Update conversation metadata with customer information and context
     * Sets nickname, email, segments, custom data, IP address, and subject
     * for the current conversation session
     *
     * @return mixed The result of updating conversation metadata
     * @throws CrispException|ClientExceptionInterface If metadata update fails
     */
    public function updateConversationMeta()
    {
        $params = [
            "nickname" => self::convertBinaryToString($this->name),
            "email" => $this->email,
            "segments" => $this->segments,
            "data" => $this->data,
            "ip" => $_SERVER["HTTP_CF_CONNECTING_IP"] ?? "0.0.0.0",
            "subject" => self::convertBinaryToString($this->subject)
        ];

        return $this->crispClient
            ->websiteConversations
            ->updateMeta($this->websiteId, $this->sessionId, $params);
    }

    /**
     * Add CC email participants to the conversation
     * Attempts to add CC email twice with a delay to ensure successful addition
     * due to potential API timing issues
     *
     * @return array Array of results from participant update attempts
     * @throws \Throwable
     */
    public function addCCEmail(): array
    {
        $addCCResults = [];
        if ($this->cc) {
            $addCCResults[] = $this->updateParticipants($this->cc);
            sleep(1);
            $addCCResults[] = $this->updateParticipants($this->cc);
        }
        return $addCCResults;
    }


    /**
     * Update conversation participants by adding an email participant
     * Handles platform-specific client selection for Windows compatibility
     * Adds email participant to the conversation for notifications
     *
     * @param string $email The email address to add as a participant
     * @return array|null The response from the participants update API
     * @throws \Throwable If JSON encoding fails
     */
    public function updateParticipants($email): ?array
    {
        if (stripos(PHP_OS_FAMILY, 'WIN') === 0) {
            $client = $this->crispClient->_rest;
        } else {
            $client = $this->crispClient;
        }
        $result = $client->put(
            "website/$this->websiteId/conversation/{$this->sessionId}/participants",
            json_encode(["participants" => [[
                "type" => "email",
                "target" => $email
            ]]], JSON_THROW_ON_ERROR)
        );
        return json_decode($result->getBody(), true, 512, JSON_THROW_ON_ERROR);
    }

    /**
     * Send all attached file links to the conversation
     * Iterates through the links array and sends each file as a message
     * with proper filename extraction
     *
     * @return void
     * @throws CrispException
     */
    public function sendLinks(): void
    {
        if (!is_array($this->links) || empty($this->links)) {
            return;
        }

        foreach ($this->links as $link) {
            $imageName = pathinfo($link, PATHINFO_BASENAME);
            $this->sendLink($link, $imageName);
        }
    }

    /**
     * Send a single file link as a message to the conversation
     * Determines file MIME type and creates a file message with proper metadata
     *
     * @param string $url The URL of the file to send
     * @param string $name The display name for the file
     * @return mixed The result of sending the file message
     * @throws CrispException|ClientExceptionInterface If the file message fails to send
     */
    public function sendLink($url, $name)
    {
        $type = $this->getImageMimeType($url);
        $message = [
            "type" => "file",
            "from" => "user",
            "origin" => "urn:backend-api",
            "content" => [
                "url" => $url,
                "type" => $type,
                "name" => $name
            ]
        ];

        return $this->crispClient
            ->websiteConversations
            ->sendMessage($this->websiteId, $this->sessionId, $message);
    }

    /**
     * Determine the MIME type of a file based on its extension
     * Uses a JSON mapping file to resolve file extensions to proper MIME types
     * Falls back to generic binary type if extension not found
     *
     * @param string $imagePath The file path or URL to determine MIME type for
     * @return string The MIME type string, defaults to 'application/octet-stream'
     * @throws \Throwable
     */
    public function getImageMimeType($imagePath)
    {
        $ext = strtolower(pathinfo($imagePath, PATHINFO_EXTENSION));
        $mimeList = json_decode(file_get_contents(__DIR__ . DIRECTORY_SEPARATOR . "mime.json"), false, 512, JSON_THROW_ON_ERROR);
        return $mimeList->$ext ?? "application/octet-stream";
    }

    /**
     * Get the CC email address for the conversation
     * Returns the email address that should receive conversation notifications
     *
     * @return string|null The CC email address
     */
    public function getCC()
    {
        return $this->cc;
    }

    /**
     * Get the current conversation session ID
     * Returns the unique identifier for the active Crisp conversation
     *
     * @return string|null The conversation session ID
     */
    public function getSessionId()
    {
        return $this->sessionId;
    }
}
