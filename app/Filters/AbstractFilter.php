<?php

namespace App\Filters;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

abstract class AbstractFilter implements IFilter
{
    /**
     * @var array
     */
    protected array $params;

    /**
     * @param     array     $params
     */
    public function __construct(array $params)
    {
        $this->params = $params;
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     *
     * @return Builder
     */
    public function apply(Builder $q): Builder
    {
        foreach($this->params as $key => $value) {
            if (method_exists($this, $filter = Str::camel($key))) {
                $this->$filter($q, $value, $key);
            }
        }

        return $q;
    }

    /**
     * @param     \Illuminate\Database\Eloquent\Builder     $q
     * @param                                               $value
     * @param     mixed                                     $column
     *
     * @return void
     */
    public function timeFilter(Builder $q, $value, mixed $column = 'created_at'): void
    {
        if (! $value = to_list($value)) {
            return;
        }

        if (count($value) === 1) {
            $q->where($column, $value);
        } else {
            $q->whereBetween($column, [Carbon::parse($value[0])->startOfDay(), Carbon::parse($value[1])->endOfDay()]);
        }
    }
}
