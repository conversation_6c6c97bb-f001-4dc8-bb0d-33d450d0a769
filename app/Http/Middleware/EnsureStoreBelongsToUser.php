<?php

namespace App\Http\Middleware;

use App\Models\Store;
use Closure;
use Illuminate\Http\Request;

class EnsureStoreBelongsToUser
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @param $storeId
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $storeId)
    {
        // $storeId is parameter from route
        // Route::get('/{storeId}/collection', ...)
        // Usage: middleware('ensure_store_belongs_to_user:storeId')
        abort_if(!Store::belongsToCurrentSeller($request->route($storeId)), 403);

        return $next($request);
    }
}
