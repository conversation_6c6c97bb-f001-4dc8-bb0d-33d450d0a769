<?php


namespace App\Http\Middleware;

use App\Enums\CacheKeys;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Throwable;

class SyncMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        try {
            $token = request()->bearerToken();

            if (Hash::check(CacheKeys::SYNC_CLEAR_CACHE, $token)) {
                return $next($request);
            }

            throw new \RuntimeException();
        } catch (Throwable $e) {
            return response()->json(
                [
                    'success' => false,
                    'error'   => 'token invalid'
                ],
                401
            );
        }
    }
}
