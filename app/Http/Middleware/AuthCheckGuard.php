<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class AuthCheckGuard
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $serviceName = $request->service_name;

        if (!isset($serviceName) || !$serviceName) {
            return \response()->json([
                'success' => false,
                'message' => 'Missing service name'
            ], 200);
        }

        if (!check_access_app_service($serviceName)) {
            return \response()->json([
                'success' => false,
                'message' => 'Services not found'
            ], 200);
        }
        return $next($request);
    }
}
