<?php

namespace App\Http\Middleware;

use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\Request;

class EnsureUserHasPermissionUpdateSystem
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $currentUser = currentUser();
        if($currentUser->hasPermission('update_system')) {
           return $next($request);
        }

        return $this->errorResponse('Access denied', 403);
    }
}
