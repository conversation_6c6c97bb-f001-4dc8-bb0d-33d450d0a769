<?php

namespace App\Http\Middleware;

use App\Enums\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\User;
use App\Traits\ApiResponse;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CampaignActionPermission
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure(Request): (Response|RedirectResponse) $next
     * @return JsonResponse|RedirectResponse|Response|mixed
     */
    public function handle(Request $request, Closure $next): mixed
    {
        try {
            $products = [];
            if ($sellerCampaignIds = $request->json('seller_campaign_ids')) {
                $sellerCampaignIds = collect($sellerCampaignIds)
                    ->groupBy('seller_id')
                    ->map(fn($items) => $items->pluck('campaign_id'))
                    ->toArray();
                $sellerIds = array_keys($sellerCampaignIds);
                if (empty($sellerIds)) {
                    return $this->errorResponse('Action Denied', 403);
                }
                User::query()
                    ->whereIn('id', $sellerIds)
                    ->get()
                    ->each(function ($seller) use (&$products, $sellerCampaignIds) {
                    $campaignIds = data_get($sellerCampaignIds, $seller->id);
                        $products = array_merge(...$products, ...(Campaign::query()
                        ->onSellerConnection($seller)
                        ->whereIn('id', $campaignIds)
                        ->where('status', CampaignStatusEnum::BLOCKED)
                        ->pluck('id')
                        ->toArray()));

                });
            } else {
                $campaignIds = $request->json('campaign_ids') ?? $request->json('ids');
                if (empty($campaignIds)) {
                    return $this->errorResponse('Action Denied', 403);
                }
                if (!is_array($campaignIds)) {
                    $campaignIds = explode(',', $campaignIds);
                }
                $seller = currentUser()->getInfoAccess();
                $products = Campaign::query()
                    ->onSellerConnection($seller)
                    ->whereIn('id', $campaignIds)
                    ->where('status', CampaignStatusEnum::BLOCKED)
                    ->pluck('id')
                    ->toArray();
            }
            if (!empty($products)) {
                return $this->errorResponse('Action Denied', 403);
            }

        } catch (\Exception $e) {
            return $this->errorResponse('Action Denied', 403);
        }
        return $next($request);
    }
}
