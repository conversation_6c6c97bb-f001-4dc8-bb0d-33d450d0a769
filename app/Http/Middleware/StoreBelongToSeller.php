<?php

namespace App\Http\Middleware;

use App\Models\Store;
use App\Traits\ApiResponse;
use Closure;
use Exception;
use Illuminate\Http\Request;

class StoreBelongToSeller
{
    use ApiResponse;

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = currentUser();

        try {
            $userId = $user->getUserId();
        } catch (Exception $e) {
            logException($e, 'StoreBelongToSeller');
            return $this->errorResponse('Unauthorized', 401);
        }

        $storeId = !empty($request->route('store_id'))
            ? $request->route('store_id')
            : $request->route('storeId');

        $exists = Store::query()
            ->where([
                'id' => $storeId,
                'seller_id' => $userId
            ])
            ->exists();

        if ($exists === false) {
            return $this->errorResponse('Unauthorized', 401);
        }

        return $next($request);
    }
}
