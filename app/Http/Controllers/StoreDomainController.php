<?php

namespace App\Http\Controllers;

use App\Enums\DomainStatusEnum;
use App\Enums\StoreDomainStatusEnum;
use App\Http\Requests\Seller\Store\CustomDomainRequest;
use App\Http\Requests\Seller\Store\VerifyCustomDomainRequest;
use App\Jobs\Storefront\UpdateStoreDomainExpirationDate;
use App\Library\DomainManagement\DomainClient;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Services\CloudFlareCustomHostname;
use App\Traits\ApiResponse;
use Cloudflare\API\Adapter\ResponseException;
use Illuminate\Http\JsonResponse;

class StoreDomainController extends Controller
{
    use ApiResponse;

    public function index(): JsonResponse
    {
        $storeDomains = StoreDomain::getStoreDomainsByCurrentSeller();
        return $this->successResponse($storeDomains);
    }

    public function store(CustomDomainRequest $request, $storeId): JsonResponse
    {
        $currentUser = currentUser();
        $userId = $currentUser->getUserId();
        $domain = $request->input('domain');
        $domain = parseValidateDomain($domain);

        if (is_null($domain)) {
            return $this->errorResponse('Invalid domain.');
        }

        $strictValidateTM = true;

        if ($currentUser->isLoggedAsByAdmin()) {
            $strictValidateTM = false;
        }

        $domainValid = isDomainCanNotDetectTradeMark(
            $domain,
            $strictValidateTM,
            $currentUser->hasCustomPayment()
        );

        if (!$domainValid) {
            return $this->errorResponse('Domain name trademark infringement');
        }

        $storeDomain = StoreDomain::query()->create([
            'seller_id' => $userId,
            'store_id' => $storeId,
            'domain' => $domain,
            'status' => StoreDomainStatusEnum::PENDING
        ]);

        if ($storeDomain->wasRecentlyCreated) {
            try {
                $logData = [
                    "Start adding custom hostname for domain: {$domain}"
                ];

                $res = (new CloudFlareCustomHostname())->addCustomHostname($domain);
                $customHostnameId = data_get($res, 'id');

                if ($customHostnameId) {
                    $storeDomain->update([
                        'cloudflare_custom_hostname_id' => $customHostnameId
                    ]);

                    $logData[] = "Custom hostname added successfully for domain: {$domain}, hostname_id: {$customHostnameId}";

                    $verifyStatus = (new CloudFlareCustomHostname())->verifyCustomHostname($customHostnameId);
                    $logData[] = "Custom hostname verification status for domain {$domain}: " . json_encode([
                            'status' => $verifyStatus['status'] ?? 'unknown',
                            'ssl' => $verifyStatus['ssl'] ?? 'unknown',
                            'dns_records' => $verifyStatus['dns_records'] ?? []
                        ]);
                } else {
                    $logData[] = "Failed to get custom hostname ID for domain: {$domain}, Response: " . json_encode($res);
                }

                graylogInfo('Custom hostname setup process', [
                    'category' => 'store_domain',
                    'domain' => $domain,
                    'log_data' => $logData
                ]);
            } catch (ResponseException $e) {
                // Code 1406: Duplicate custom hostname found.
                // We don't need to add custom hostname again.
                // Just ignore this exception.
                $logData = [];
                if ($e->getCode() === 1406) {
                    graylogInfo('Duplicate custom hostname found', [
                        'category' => 'store_domain',
                        'domain' => $domain
                    ]);
                } else {
                    graylogError('Failed to add custom hostname', [
                        'category' => 'store_domain',
                        'domain' => $domain,
                        'error' => $e->getMessage(),
                        'code' => $e->getCode()
                    ]);
                    logException($e);
                }
            } catch (\Exception $e) {
                graylogError('Failed to add custom hostname', [
                    'category' => 'store_domain',
                    'domain' => $domain,
                    'error' => $e->getMessage()
                ]);
                logException($e);
            }

            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function verify(VerifyCustomDomainRequest $request, $storeId, $storeDomainId): JsonResponse
    {
        $domain = $request->post('domain');
        try {
            $domainManager = DomainClient::instance();
            if ($domainManager && $domainManager->register($domain)) {
                $currentStore = Store::getDomainById($storeId);
                $isDefault = 0;
                if (is_null($currentStore)) {
                    $isDefault = 1;
                }
                StoreDomain::updateStoreDomainById($storeDomainId, [
                    'status' => StoreDomainStatusEnum::ACTIVATED,
                    'is_default' => $isDefault
                ]);

                if (is_null($currentStore)) {
                    $updated = Store::query()
                        ->where('id', $storeId)
                        ->update([
                            'domain' => $domain,
                            'domain_status' => DomainStatusEnum::COMPLETE
                        ]);

                    if ($updated) {
                        UpdateStoreDomainExpirationDate::dispatchAfterResponse($storeId, $domain);
                    }
                }

                return $this->successResponse();
            }
        } catch (\Exception $e) {
            return $this->errorResponse('Cannot verify domain');
        }
        return $this->errorResponse('Cannot verify domain');
    }

    public function destroy($storeId, $storeDomainId): ?JsonResponse
    {
        try {
            $storeDomain = StoreDomain::getCurrentStoreDomainByIdAndStoreId($storeDomainId, $storeId);

            if (is_null($storeDomain)) {
                return $this->errorResponse();
            }

            $domainIsDefault = $storeDomain->is_default;
//            if ($storeDomainStatus->is_default === 1) {
//                return $this->errorResponse('Cannot delete default domain');
//            }
            $status = $storeDomain->status;

            if ($status === StoreDomainStatusEnum::ACTIVATED) {
                DomainClient::instance()->destroy($storeDomain->domain);
                clearStoreCache($storeId);
            }

            $domainDeleted = $storeDomain->delete();

            if ($domainDeleted && $domainIsDefault === 1) {
                Store::query()
                    ->where('id', $storeId)
                    ->update([
                        'domain' => null,
                        'domain_status' => null
                    ]);

                // delete custom hostname on Cloudflare
                $customHostnameId = $storeDomain->cloudflare_custom_hostname_id;

                if ($customHostnameId) {
                    (new CloudFlareCustomHostname())->deleteCustomHostname($customHostnameId);
                }
            }

            return $this->successResponse();
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }

    public function setDefault($storeId, $storeDomainId): ?JsonResponse
    {
        try {
            $storeDomainStatus = StoreDomain::getCurrentStoreDomainByIdAndStoreId($storeDomainId, $storeId);
            if (is_null($storeDomainStatus)) {
                return $this->errorResponse();
            }
            if ($storeDomainStatus->status === StoreDomainStatusEnum::ACTIVATED) {
                StoreDomain::resetDefaultByStoreId($storeId);
                $storeDomainUpdated = StoreDomain::updateStoreDomainById($storeDomainId, [
                    'is_default' => 1
                ]);
                if (!$storeDomainUpdated) {
                    return $this->errorResponse();
                }
                $storeUpdated = Store::query()
                    ->where('id', $storeId)
                    ->update([
                        'domain' => $storeDomainStatus->domain,
                        'domain_status' => DomainStatusEnum::COMPLETE
                    ]);
                if ($storeUpdated) {
                    return $this->successResponse();
                }
            }
            return $this->errorResponse();
        } catch (\Exception $e) {
            return $this->errorResponse();
        }
    }
}
