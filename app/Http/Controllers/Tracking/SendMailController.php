<?php

namespace App\Http\Controllers\Tracking;

use App\Enums\CacheTime;
use App\Enums\StoreStatusEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Email\Tracking\ClickedRequest;
use App\Models\SendMailLog;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Models\SystemConfig;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Response;

class SendMailController extends Controller
{
    public function opened(Request $request): \Illuminate\Http\Response
    {
        try {
            $hashId = $request->get('hashId');
            if ($hashId) {
                SendMailLog::query()
                    ->where('id', $hashId)
                    ->where('opened', false)
                    ->update([
                        'opened' => true
                    ]);
            }
        } catch (\Exception $e) {}
        $im = @imagecreatetruecolor(1, 1);
        $color = @imagecolorallocate($im, 0, 0, 0);
        @imagefill($im, 0, 0, $color);
        @imagegif($im);
        @imagedestroy($im);
        $content = ob_get_contents();
        ob_end_clean();
        $response = Response::make($content)->header('Content-Type', 'image/gif');
        unset($content);
        return $response;
    }

    public function clicked(ClickedRequest $request)
    {
        $redirectUrl = $request->get('redirectUrl');
        $hashId = $request->get('hashId');
        $userId = $request->get('userId');
        try {
            if ($hashId) {
                SendMailLog::query()
                    ->where('id', $hashId)
                    ->where('clicked', false)
                    ->update([
                        'clicked' => true
                    ]);
            }
        } catch (\Exception $e) {}
        if (isset($userId)){
            if (str_contains($redirectUrl, '?')) {
                $paramPrefix = '&';
            } else {
                $paramPrefix = '?';
            }
            $redirectUrl .= $paramPrefix . 'user_id=' . $userId;
        }

        if ($redirectUrl) {
            $domain = parse_url($redirectUrl, PHP_URL_HOST);
            if ($this->isStoreInactive($domain)) {
                $redirectUrl = $this->replaceDomainToMarketplace($redirectUrl);
            }
            return redirect($redirectUrl);

        }
        return redirect('https://google.com');
    }

    private function isStoreInactive(String $domain): bool
    {
        try {
            return Cache::remember('store-inactive-' . $domain, CacheTime::CACHE_1H, function () use ($domain) {
                $domainQuery = StoreDomain::query()->select('store_id')->where('domain', $domain);
                return Store::query()
                    ->whereExists(function ($query) use ($domainQuery) {
                        $query->select('store_id')->fromSub($domainQuery, 'sub')
                            ->whereColumn('store.id', 'sub.store_id');
                    })
                    ->where('status', StoreStatusEnum::INACTIVE)
                    ->exists();
            });
        } catch (\Exception $e) {
            logToDiscord("key:isStoreInactive " . $e->getMessage(), 'thiennt_log');
        }
        return false;
    }
    private function replaceDomainToMarketplace(String $url): string
    {
        try {
            $parsedUrl = parse_url($url);
            $path = $parsedUrl['path'] ?? '';
            $domain = SystemConfig::getConfig('preview_domain', 'senstores.com');
            $query = isset($parsedUrl['query']) ? '?' . $parsedUrl['query'] : '';
            $fragment = isset($parsedUrl['fragment']) ? '#' . $parsedUrl['fragment'] : '';
            return 'https://' . $domain . $path . $query . $fragment;
        } catch (\Exception $e) {
            logToDiscord("key:replaceDomain " . $e->getMessage(), 'thiennt_log');
        }
        return $url;
    }
}
