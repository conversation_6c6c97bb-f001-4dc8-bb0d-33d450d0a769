<?php

namespace App\Http\Controllers\External;

use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Enums\OrderStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductType;
use App\Enums\ShippingMethodEnum;
use App\Http\Controllers\Controller;
use App\Jobs\ValidateSellerFulfillOrder;
use App\Models\File;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\OrderService;
use App\Traits\ApiResponse;
use App\Traits\ExternalRequestHelper;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Campaign\Enums\ProductSystemTypeEnum;

class OrderController extends Controller
{
    use ApiResponse, ExternalRequestHelper;

    /**
     * Create a new order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $seller = currentUser()->getInfoAccess();
        $sellerId = self::getSellerIdByRequest($request);

        if (!$sellerId) {
            return $this->errorResponse('Seller not found.');
        }

        $requestData = $request->all();
        $errorMessages = [];
        $productTemplates = [];
        $disabledCountry = SystemConfig::getConfig('disable_country', '');

        $orderNumber = $requestData['order_number'] ?? null;
        if (empty($orderNumber)) {
            return $this->errorResponse('Order #' . $orderNumber . ' is invalid');
        }
        if (empty($requestData['country_code']) || str_contains($disabledCountry, $requestData['country_code'])) {
            return $this->errorResponse('Order #' . $requestData['order_number'] . ': Country code is invalid');
        }

        DB::beginTransaction();
        try {
            $invalid = false;
            // check if order number is already exist
            $order = Order::query()->firstWhere([
                'seller_id' => $sellerId,
                'order_number_2' => $requestData['order_number'],
            ]);

            if ($order) {
                if (in_array($order->status, [OrderStatus::DRAFT, OrderStatus::CANCELLED])) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->where('order_id', $order->id)->delete();
                    OrderProduct::query()->where('order_id', $order->id)->delete();
                    Order::query()->whereId($order->id)->delete();
                } else {
                    return $this->errorResponse('Order #' . $orderNumber . ' is duplicated');
                }
            }

            if (empty($requestData['customer_name']) || empty($requestData['address']) || empty($requestData['city']) || empty($requestData['postcode'])) {
                $errorMessages[] = 'Order #' . $orderNumber . ': Shipping address is invalid';
                $invalid = true;
            }

            if (empty($requestData['customer_name'])) {
                $errorMessages[] = 'Customer name required';
                $invalid = true;
            }

            $locationByCode = getLocationByCode($requestData['country_code']);

            if (empty($locationByCode)) {
                $locationByName = getLocationByName($requestData['country_code']);

                if (!empty($locationByName)) {
                    $requestData['country_code'] = $locationByName->code;
                } else {
                    $errorMessages[] = 'Order #' . $orderNumber . ': Country code is invalid';
                    $invalid = true;
                }
            }

            if (!isset($requestData['products'])) {
                return $this->errorResponse('Order #' . $orderNumber . ': Products is invalid');
            }

            ### create order ###
            $refId = User::query()
                ->where('id', $sellerId)
                ->value('ref_id');

            // get order data
            $orderData = [
                'seller_id' => $sellerId,
                'ref_id' => $refId,
                'type' => OrderTypeEnum::FULFILLMENT,
                'order_number_2' => $requestData['order_number'],
                'customer_name' => $requestData['customer_name'],
                'customer_email' => $requestData['customer_email'] ?? null,
                'customer_phone' => $requestData['customer_phone'] ?? null,
                'address' => $requestData['address'],
                'address_2' => $requestData['address_2'] ?? null,
                'city' => $requestData['city'],
                'state' => $requestData['state'],
                'postcode' => $requestData['postcode'],
                'country' => $requestData['country_code'],
                'order_note' => $requestData['order_note'] ?? null,
                'store_domain' => $requestData['store_domain'] ?? null,
                'store_name' => $requestData['store_name'] ?? null,
                'status' => OrderStatus::DRAFT,
                'fulfill_status' => OrderFulfillStatus::UNFULFILLED,
                'shipping_method' => ShippingMethodEnum::STANDARD,
                'address_verified' => OrderAddressVerifiedEnum::UNVERIFIED
            ];

            // update if order already exists
            $newOrder = Order::query()->updateOrCreate([
                'seller_id' => $sellerId,
                'order_number_2' => $orderNumber,
            ], $orderData);
            $newOrder->order_number = 'FF-' . $newOrder->id;
            $newOrder->save();

            ### create products of new order ###
            $products = $requestData['products'];

            foreach ($products as $product) {
                $productQty = $product['quantity'];

                if (empty($productQty) || (int)$productQty < 1) {
                    return $this->errorResponse('Order #' . $orderNumber . ': Product quantity is invalid');
                }

                // get template product from sku
                if (isset($productTemplates[$product['product_sku']])) {
                    $productTemplate = $productTemplates[$product['product_sku']];
                } else {
                    $productTemplate = Product::query()->firstWhere([
                        'product_type' => ProductType::TEMPLATE,
                        'sku' => $product['product_sku']
                    ]);

                    if ($productTemplate) {
                        $productTemplates[$product['product_sku']] = $productTemplate;
                    }
                }

                if (!$productTemplate) {
                    return $this->errorResponse('Order #' . $orderNumber . ': Product sku ' . $product['product_sku'] . ' is invalid');
                }

                // search artworks in file
                $artworks = array_filter($product, function ($value, $key) {
                    return str_starts_with($key, 'artwork_') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);

                if (!count($artworks)) {
                    return $this->errorResponse('Order #' . $orderNumber . ' - Product sku ' . $product['product_sku'] . ': No artworks found');
                }

                $mockups = array_filter($product, function ($value, $key) {
                    return str_starts_with($key, 'mockup_') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);

                $optionsFields = array_filter($product, function ($value, $key) {
                    return str_starts_with($key, 'option_') && !empty($value);
                }, ARRAY_FILTER_USE_BOTH);

                $productOptions = [];

                foreach ($optionsFields as $key => $value) {
                    $keyExplode = explode('_', $key);
                    $validKey = strtolower(trim($keyExplode[1]));
                    $productOptions[$validKey] = strtolower($value);
                }

                $optionLogs = '';
                $productOptions = OrderService::correctOptions($productOptions, $productTemplate, $optionLogs);

                if ($productOptions === false) {
                    $errorMessages[] = 'Order #' . $orderNumber . ': ' . $optionLogs;
                    $invalid = true;
                }

                if ($invalid) {
                    if (count($errorMessages) > 0) {
                        DB::rollBack();
                        throw new \ErrorException(implode(';', $errorMessages), 403);
                    }

                    return $this->errorResponse('Invalid data');
                }

                // get order product data
                $orderProductData = [
                    'order_id' => $newOrder->id,
                    'sku' => $productTemplate->sku, // sku of the template product
                    'template_id' => $productTemplate->id, // id of the template product
                    'seller_id' => $sellerId,
                    'ref_id' => $refId,
                    'campaign_title' => !empty($requestData['product_name']) ? $requestData['product_name'] : null,
                    'product_name' => $productTemplate->name,
                    'quantity' => $productQty,
                    'full_printed' => $productTemplate->full_printed,
                    'campaign_type' => ProductSystemTypeEnum::FULFILL,
                ];

                $printSpaces = json_decode($productTemplate->print_spaces);
                $printSpaceNames = array_column($printSpaces, 'name');
                $printSpaceNames[] = PrintSpaceEnum::DEFAULT;
                $optionSize = $productOptions['size'] ?? null;
                $defaultPrintSpace = self::defaultPrintSpace($printSpaceNames, $optionSize, $productTemplate->full_printed);

                $orderProductData['options'] = json_encode($productOptions) ?? null;

                // each line is unique product
                $orderProduct = OrderProduct::query()->create($orderProductData);

                $artworkData = [];
                foreach ($artworks as $key => $artwork) {
                    $keyExplode = explode('_', $key);
                    $printSpace = strtolower(trim($keyExplode[1]));

                    if ($printSpace === PrintSpaceEnum::DEFAULT) {
                        $printSpace = $defaultPrintSpace;
                    }

                    if (!in_array($printSpace, $printSpaceNames)) {
                        $errorMessages[] = 'Order #' . $orderNumber . ': Artwork printspace ' . $printSpace . ' invalid';
                        $invalid = true;
                        continue;
                    }

                    //only validate product full printed
                    if ($productTemplate->isFullPrintedType() && $optionSize && ($printSpace !== PrintSpaceEnum::DEFAULT) && (!str_contains($optionSize, $printSpace))) {
                        $errorMessages[] = 'Order #' . $orderNumber . ': Artwork printspace ' . $printSpace . " doesn't match product size " . $optionSize;
                        $invalid = true;
                        continue;
                    }

                    $data = [
                        'order_id' => $newOrder->id,
                        'type' => FileTypeEnum::DESIGN,
                        'file_url' => $artwork,
                        'order_product_id' => $orderProduct->id,
                        'seller_id' => $sellerId,
                        'option' => FileRenderType::PRINT,
                        'print_space' => $printSpace
                    ];

                    $artworkData[] = $data;
                }

                $mockupData = [];
                foreach ($mockups as $key => $mockup) {
                    $keyExplode = explode('_', $key);
                    $printSpace = strtolower(trim($keyExplode[1]));

                    if ($printSpace === PrintSpaceEnum::DEFAULT) {
                        $printSpace = $defaultPrintSpace;
                    }

                    if (!in_array($printSpace, $printSpaceNames)) {
                        $errorMessages[] = 'Order #' . $orderNumber . ': Mockup printspace ' . $printSpace . ' invalid';
                        $invalid = true;
                        continue;
                    }

                    if ($productTemplate->isFullPrintedType() && $optionSize && ($printSpace !== PrintSpaceEnum::DEFAULT) && (!str_contains($optionSize, $printSpace))) {
                        $errorMessages[] = 'Order #' . $orderNumber . ': Mockup printspace ' . $printSpace . " doesn't match product size " . $optionSize;
                        $invalid = true;
                        continue;
                    }

                    $data = [
                        'order_id' => $newOrder->id,
                        'type' => FileTypeEnum::IMAGE,
                        'file_url' => $mockup,
                        'order_product_id' => $orderProduct->id,
                        'seller_id' => $sellerId,
                        'option' => null,
                        'print_space' => $printSpace
                    ];

                    $mockupData[] = $data;
                }

                if (!$invalid) {
                    File::query()
                        ->onSellerConnection($seller)
                        ->insert(array_merge($mockupData, $artworkData));
                }
            }

            if (count($errorMessages) > 0) {
                DB::rollBack();
                return $this->errorResponse(implode(';', $errorMessages));
            }

            // everything is ok, commit the changes
            DB::commit();

            // logToDiscord('Queue validating order #' . $newOrder->order_number);
            ValidateSellerFulfillOrder::dispatch($newOrder);

            return $this->successResponse([
                'order_number' => $newOrder->order_number_2,
                'sen_order_number' => $newOrder->order_number
            ]);
        } catch (\Exception $exception) {
            DB::rollBack();
            logToDiscord('API Create Order Error - Order number #' . $orderNumber . ': ' . $exception->getMessage());
            return $this->errorResponse('Order not created');
        }
    }

    /**
     * Show order details
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function show(Request $request): JsonResponse
    {
        $sellerId = self::getSellerIdByRequest($request);

        if (!$sellerId) {
            return $this->errorResponse('Seller not found.');
        }

        $orderNumber = $request->query('order_number');
        $orderId = $request->query('order_id');
        $isIncludeAllStatus = $request->boolean('include_all_status');

        if (!$orderNumber && !$orderId) {
            return $this->errorResponse('Order number or order ID is required.');
        }

        $fields = [
            'id',
            'access_token',
            'discount_code',
            'order_number',
            'payment_gateway_id',
            'promotion_rule_id',
            'shipping_method',
            'total_amount',
            'total_discount',
            'payment_discount',
            'tip_amount',
            'total_product_amount',
            'total_quantity',
            'total_shipping_amount',
            'total_tax_amount',
            'country',
            'paid_at',
            'updated_at',
            'fulfilled_at',
            'delivered_at',
            'status',
            'fulfill_status',
            'customer_name',
            'customer_email',
            'customer_phone',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'currency_code',
            'currency_rate',
        ];

        // add order_number_2 if query by order_id
        if ($orderId) {
            $fields[] = 'order_number_2';
        }

        $order = Order::query()
            ->select($fields)
            ->where('seller_id', $sellerId)
            ->when($orderNumber, function ($query, $orderNumber) {
                return $query->where('order_number_2', $orderNumber);
            })
            ->when($orderId, function ($query, $orderId) {
                return $query->where('id', $orderId);
            })
            ->when(!$isIncludeAllStatus, function ($query) {
                $query->whereNotIn('status', [
                    OrderStatus::PENDING,
                    OrderStatus::DRAFT
                ]);
            })
            ->with(['products' => function ($query) {
                $query->with([
                    'productReview' => function ($review) {
                        $review->select(['order_product_id', 'average_rating']);
                    }
                ])->select([
                    'id',
                    'campaign_id',
                    'campaign_title',
                    'options',
                    'custom_options',
                    'color',
                    'order_id', // must-have key for relationship
                    'price',
                    'product_id',
                    'product_name',
                    'product_url',
                    'quantity',
                    'thumb_url',
                    'total_amount',
                    'discount_amount',
                    'fulfill_status',
                    'tracking_code',
                    'shipping_carrier',
                    'tracking_url',
                    'tracking_status',
                ])
                    ->orderBy('updated_at', 'desc');
            }])
            ->first();

        if (is_null($order)) {
            return $this->errorResponse('Order not found.');
        }

        $fulfillments = $order->getFulfillments(isSeller: true);
        $order->setRelation('products', null);

        // override order_number with order_number_2
        if ($orderNumber) {
            $order->order_number = $orderNumber;
        } elseif (isset($order->order_number_2)) {
            $order->order_number = $order->order_number_2;
            unset($order->order_number_2);
        }

        return $this->successResponse([
            'order' => $order,
            'fulfillments' => $fulfillments
        ]);
    }

    /**
     * Cancel an order
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function cancel(Request $request): JsonResponse
    {
        $sellerId = self::getSellerIdByRequest($request);

        if (!$sellerId) {
            return $this->errorResponse();
        }

        $orderNumber = $request->query('order_number');
        $ref = $request->query('ref');

        if (!$orderNumber) {
            return $this->errorResponse('Order number is required.');
        }

        $updated = Order::query()
            ->where([
                'seller_id' => $sellerId,
                'order_number_2' => $orderNumber
            ])
            ->whereIn('status', [
                OrderStatus::PENDING,
                OrderStatus::DRAFT,
                OrderStatus::ON_HOLD
            ])
            ->update(['status' => OrderStatus::CANCELLED]);

        if ($updated && $ref === 'woocommerce') {
            $order = Order::query()
                ->where([
                    'seller_id' => $sellerId,
                    'order_number_2' => $orderNumber
                ])
                ->first();


            if (is_null($order)) {
                return $this->errorResponse('Order not found.');
            }

            OrderHistory::insertLog(
                $order,
                OrderHistoryActionEnum::WOOCOMMERCE_STATUS_SYNC,
                "Synchronized order status from WooCommerce: cancelled",
                OrderHistoryDisplayLevelEnum::CUSTOMER
            );
        }

        return $updated ? $this->successResponse() : $this->errorResponse();
    }

    private static function defaultPrintSpace($printSpaces, $optionSize, $fullPrinted): string
    {
        if ($fullPrinted) {
            foreach ($printSpaces as $printSpace) {
                if (str_contains($optionSize, $printSpace)) {
                    return $printSpace;
                }
            }
        } else if (!empty($printSpaces)) {
            return $printSpaces[0];
        }

        return PrintSpaceEnum::DEFAULT;
    }

    private function updateOrderStatusAndLog(Order $order, string $newStatus, string $oldStatus): void
    {
        $order->update(['status' => $newStatus]);

        OrderHistory::insertLog(
            $order,
            OrderHistoryActionEnum::WOOCOMMERCE_STATUS_SYNC,
            "Synchronized order status from WooCommerce: {$oldStatus} -> {$newStatus}",
            OrderHistoryDisplayLevelEnum::CUSTOMER
        );
    }

    public function updateStatus(Request $request): JsonResponse
    {
        try {
            $sellerId = self::getSellerIdByRequest($request);

            if (!$sellerId) {
                return $this->errorResponse('Seller not found.');
            }

            $orderNumber = $request->query('order_number');
            $status = $request->query('status');
            $ref = $request->query('ref');

            if ($ref !== 'woocommerce') {
                return $this->errorResponse('Invalid ref');
            }

            if (!$orderNumber) {
                return $this->errorResponse('Order number is required.');
            }

            if (!in_array($status, ['on_hold', 'complete', 'pending'])) {
                return $this->errorResponse('Invalid status.');
            }

            $order = Order::query()
                ->where([
                    'seller_id' => $sellerId,
                    'order_number_2' => $orderNumber
                ])
                ->first();

            if (!$order) {
                return $this->errorResponse('Order not found');
            }

            if ($order->type !== OrderTypeEnum::FULFILLMENT) {
                return $this->errorResponse('Invalid order type');
            }

            switch ($status) {
                case 'on_hold':
                    if ($order->fulfill_status !== OrderFulfillStatus::UNFULFILLED) {
                        return $this->errorResponse('Order status cannot be updated to on-hold');
                    }

                    $this->updateOrderStatusAndLog($order, OrderStatus::ON_HOLD, 'pending');
                    break;

                case 'complete':
                    if ($order->fulfill_status !== OrderFulfillStatus::ON_DELIVERY) {
                        return $this->errorResponse('Order status cannot be updated to complete');
                    }

                    $this->updateOrderStatusAndLog($order, OrderStatus::COMPLETED, 'on_delivery');
                    break;

                case 'pending':
                    if ($order->fulfill_status === OrderFulfillStatus::FULFILLED) {
                        return $this->errorResponse('Order status cannot be updated to pending');
                    }

                    $this->updateOrderStatusAndLog($order, OrderStatus::PENDING, 'on_hold');
                    break;

                default:
                    return $this->errorResponse('Invalid status');
            }

            return $this->successResponse();

        } catch (\Exception $e) {
            logException($e);
            return $this->errorResponse();
        }
    }
}
