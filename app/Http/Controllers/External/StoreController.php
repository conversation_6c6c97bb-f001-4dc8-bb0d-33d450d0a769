<?php

namespace App\Http\Controllers\External;

use App\Http\Controllers\Controller;
use App\Models\Store;
use App\Traits\ApiResponse;
use App\Traits\ExternalRequestHelper;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class StoreController extends Controller
{
    use ApiResponse, ExternalRequestHelper;

    /**
     * Disconnect WooCommerce integration by removing webhook URL
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function disconnectWooCommerce(Request $request): JsonResponse
    {
        $sellerId = self::getSellerIdByRequest($request);

        if (!$sellerId) {
            return $this->errorResponse('Seller not found.');
        }

        try {
            $data = $request->validate(['store_id' => 'required|integer']);
            $storeId = $data['store_id'];

            $updated = Store::query()
                ->where([
                    'id' => $storeId,
                    'seller_id' => $sellerId,
                ])
                ->update(['woocommerce_webhook_url' => null]);

            if ($updated) {
                clearStoreCache($storeId);
                return $this->successResponse();
            }

            return $this->errorResponse();
        } catch (Exception $e) {
            logException($e);
            return $this->errorResponse('Something went wrong');
        }
    }
}
