<?php

namespace App\Http\Controllers\Analytic3;

use App\Actions\Admin\Analytic3\GetSaleReportDetailAction;
use App\Enums\CacheKeys;
use App\Enums\DateRangeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserInfoKeyEnum;
use App\Http\Requests\Analytic\Admin\GetSaleReportsRequest;
use App\Http\Requests\Analytic\Admin\GetSellersStatsRequest;
use App\Models\IndexOrder;
use App\Models\IndexOrderProduct;
use App\Models\SellerBilling;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\StoreService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;

class AdminController extends SellerController
{
    private const ARR_SALE_REPORTS_NEED_RECALCULATED = [
        'cr',
        'upsell',
        'active_sellers',
        'fulfill_sellers',
    ];

    public function __construct()
    {
        $excludeCountries = SystemConfig::getConfig(UserInfoKeyEnum::EXCLUDE_COUNTRIES);
        if (!empty($excludeCountries)) {
            $this->arrExclude[UserInfoKeyEnum::EXCLUDE_COUNTRIES] = explode(',', $excludeCountries);
        }
    }

    protected function addAttributeToOverview(&$overview, $user): void
    {
        parent::addAttributeToOverview($overview, $user);
        $payout = SellerBilling::query()
            ->selectRaw(
                "SUM(CASE WHEN `status` in ('" . SellerBillingStatus::PENDING . "','" . SellerBillingStatus::PROCESSING . "') THEN `amount` END) as payout_pending"
            )
            ->selectRaw(
                "SUM(CASE WHEN `status` = '" . SellerBillingStatus::COMPLETED . "' THEN `amount` END) as seller_profit_paid"
            )
            ->where('type', SellerBillingType::PAYOUT)
            ->first();

        $overview->payout_pending     = $payout->payout_pending ?? 0;
        $overview->seller_profit_paid = $payout->seller_profit_paid ?? 0;

        $builderFulfillOrders = $this->getBuilderFulfillOrders();
        $overview->fulfill_sellers    = $this->getCountFulfillSellers($builderFulfillOrders);
        $overview->total_fulfill_items_quantity = $this->getTotalFulfillItems($builderFulfillOrders) ?? 0;
    }

    public function getBuilderFulfillOrders(): object
    {
        return IndexOrder::query()
            ->select('id', 'seller_id')
            ->filterFulfillOrders()
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges);
    }

    public function getCountFulfillSellers($builderFulfillOrders): int
    {
        return $this->getFulfillSellers($builderFulfillOrders->pluck('seller_id')->unique())->count();
    }

    public function getTotalFulfillItems($builderFulfillOrders): ?int
    {
        $fulfillOrderIds = $builderFulfillOrders->pluck('id');
        $fulfillSellerIds = $this->getFulfillSellers($builderFulfillOrders->pluck('seller_id')->unique())->pluck('id');

        return IndexOrderProduct::query()
            ->selectRaw("SUM(quantity) as total_fulfill_items_quantity")
            ->whereIn('order_id', $fulfillOrderIds)
            ->whereIn('seller_id', $fulfillSellerIds)
            ->whereNull('deleted_at')
            ->value('total_fulfill_items_quantity');
    }

    public function getFulfillSellers($fulfillSellersIds)
    {
        return User::query()
            ->select('id')
            ->whereIn('id', $fulfillSellersIds)
            ->filterFulfillSeller()
            ->get();
    }

    public function getSellersStats(GetSellersStatsRequest $request): object
    {
        $this->setCommonFilter($request);
        $activeIds = IndexOrder::query()
            ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
            ->addExcludeAnalytic($this->arrExclude)
            ->distinct()
            ->whereNotNull('seller_id')
            ->pluck('seller_id')
            ->toArray();

        $options               = $request->except('sort');
        $options['date_range'] = $request->get('seller_date_range', $request->get('date_range'));
        $options['start_date'] = $request->get('seller_start_date', $request->get('start_date'));
        $options['end_date']   = $request->get('seller_end_date', $request->get('end_date'));
        $options['ids']        = $activeIds;
        $options['date_ranges'] = $this->dateRanges;

        $sellerIds = User::getSellers($options, 0, false)->pluck('id')->toArray();

        $request->merge(['seller_ids' => $sellerIds]);
        $this->setCommonFilter($request);
        $overview = $this->getCommonQuery()->first();

        if ($overview) {
            $overview->active_sellers = count($activeIds);
        }
        return model_map($overview, 'floatval');
    }

    public function getSaleReports(GetSaleReportsRequest $request): JsonResponse
    {
        try {
            $rangeType  = $request->input('range_type');
            $year       = $request->input('year', date('Y'));
            $customMode = $request->filled('year');
            $data       = [];
            $arr        = [];
            $now        = now();
            $count      = 0;
            $title      = '';

            if ($customMode) {
                $start      = Carbon::createFromDate($year, 1, 1);
                $numberLoop = 11;
            }

            switch ($rangeType) {
                default:
                case 'month':
                    $start      ??= $now->copy()->subYearNoOverflow()->subMonth()->startOfMonth();
                    $numberLoop ??= 13;
                    $step       = 1;
                    break;
                case 'quarter':
                    $start      ??= $now->copy()->subYearNoOverflow()->subQuarter()->firstOfQuarter();
                    $numberLoop ??= $start->diffInMonths($now);
                    $step       = 3;
                    break;
                case 'year':
                    // launched year: 2021
                    $start      ??= Carbon::createFromDate(2021, 1, 1);
                    $numberLoop ??= $start->diffInMonths($now);
                    $step       = 12;
                    break;
            }

            for ($i = 0; $i <= $numberLoop; $i++) {
                $endDate = $start->copy()->addMonthsNoOverflow($i);
                $year    = $endDate->year;
                $month   = $endDate->month;
                $compare = self::compareTimeToCurrent($year, $month);
                if ($compare > 0) {
                    break;
                }
                $obj = (new GetSaleReportDetailAction($this, $year, $month))->handle($compare);
                foreach ($obj as $key => $value) {
                    if (!isset($arr[$key])) {
                        $arr[$key] = 0;
                    }
                    $arr[$key] += $value;
                }

                switch ($rangeType) {
                    case 'month':
                        $title = 'M' . $endDate->format('m-Y');
                        break;
                    case 'quarter':
                        $title = 'Q' . $endDate->quarter . '-' . $endDate->year;
                        $this->setDateRanges($endDate->year, $endDate->quarter);
                        $this->reCalculate($arr);
                        break;
                    case 'year':
                        $title = $endDate->year;
                        $this->setDateRanges($endDate->year);
                        $this->reCalculate($arr);
                        break;
                }
                $count++;
                if ($count === $step) {
                    $data[$title] = $arr;
                    $arr          = [];
                    $count        = 0;
                }
            }
            if (!empty($arr)) {
                $data[$title] = $arr;
            }
            return $this->successResponse($data);
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    private function reCalculate(&$arr): void
    {
        foreach (self::ARR_SALE_REPORTS_NEED_RECALCULATED as $key) {
            switch ($key) {
                case 'cr':
                    if (!empty($arr['orders']) && !empty($arr['visit'])) {
                        $arr[$key] = $arr['orders'] / $arr['visit'];
                    }
                    break;
                case 'upsell':
                    if (!empty($arr['orders']) && !empty($arr['items'])) {
                        $arr[$key] = $arr['items'] / $arr['orders'];
                    }
                    break;
                case 'active_sellers':
                    $arr[$key] = IndexOrder::query()
                        ->countActiveSellers()
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->value('active_sellers');
                    break;
                case 'fulfill_sellers':
                    $arr[$key] = IndexOrder::query()
                        ->selectRaw('COUNT(DISTINCT `seller_id`) as fulfill_sellers')
                        ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                        ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                        ->addFilterAnalytic($this->arrFilter, $this->dateRanges)
                        ->addExcludeAnalytic($this->arrExclude)
                        ->value('fulfill_sellers');
                    break;
            }
        }
    }

    public static function compareTimeToCurrent($year, $month): int
    {
        $month   = ($month < 10) ? '0' . $month : $month;
        $current = date('Y-m');

        if ($year . '-' . $month < $current) {
            return -1;
        }

        if ($year . '-' . $month > $current) {
            return 1;
        }

        return 0;
    }

    private function setDateRanges($year, $quarter = null): void
    {
        if ($quarter) {
            $startDate = Carbon::createFromDate($year, ($quarter - 1) * 3 + 1, 1);
            $endMethod = 'lastOfQuarter';
        } else {
            $startDate = Carbon::createFromDate($year, 1, 1);
            $endMethod = 'lastOfYear';
        }

        $dateRanges['range'][0] = $startDate->format('Y-m-d');
        $dateRanges['range'][1] = $startDate->copy()->$endMethod()->format('Y-m-d');
        $dateRanges['type']     = DateRangeEnum::CUSTOM;

        $this->dateRanges = $dateRanges;
    }

    /**
     * @return mixed
     * @throws \Exception
     */
    public function getPaymentMethods()
    {
        return cache()->remember(CacheKeys::ANALYTIC_PAYMENT_METHOD . '_v3', CacheKeys::CACHE_1H,
            function () {
                $methods = IndexOrder::query()
                    ->select('payment_method')
                    ->selectRaw('max(paid_at) AS last_paid')
                    ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM, OrderTypeEnum::SERVICE])
                    ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                    ->where('payment_method', '>', '')
                    ->groupBy('payment_method')
                    ->orderByDesc('last_paid')
                    ->get();

                $orders = IndexOrder::query()
                    ->select('payment_method')
                    ->selectRaw('count(*) as count')
                    ->selectRaw('sum(total_amount) as sales')
                    ->where('created_at', '>=', now()->subDays(30))
                    ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM, OrderTypeEnum::SERVICE])
                    ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                    ->groupBy('payment_method')
                    ->get();

                foreach ($methods as &$method) {
                    $method->last_30_count = 0;
                    $method->last_30_sales = 0;
                    foreach ($orders as $index => $order) {
                        if ($order->payment_method === $method->payment_method) {
                            $method->last_30_count = $order->count;
                            $method->last_30_sales = $order->sales;
                            unset($orders[$index]);
                            break;
                        }
                    }
                }
                return $methods;
            }
        );
    }

    /**
     * @return mixed
     */
    public function getCurrencies()
    {
        return cache()->remember(CacheKeys::ANALYTIC_CURRENCIES . '_v3', CacheKeys::CACHE_1H, function () {
            $currencies = IndexOrder::query()
                ->select('currency_code')
                ->selectRaw('max(paid_at) AS last_paid')
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM, OrderTypeEnum::SERVICE])
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->groupBy('currency_code')
                ->orderByDesc('last_paid')
                ->get();

            $orders = IndexOrder::query()
                ->select('currency_code')
                ->selectRaw('count(*) as count')
                ->selectRaw('sum(total_amount) as sales')
                ->selectRaw('sum(total_amount * currency_rate) as sales_in_currency')
                ->whereIn('type', [OrderTypeEnum::REGULAR, OrderTypeEnum::CUSTOM, OrderTypeEnum::SERVICE])
                ->where('created_at', '>=', now()->subDays(30))
                ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                ->groupBy('currency_code')
                ->get();

            foreach ($currencies as $currency) {
                $currencyObject = StoreService::systemCurrencies()?->firstWhere('code', strtoupper($currency->currency_code));
                $currency->last_30_count = 0;
                $currency->last_30_sales = formatCurrency(0, 'USD', 'en-US', true);
                $currency->last_30_sales_in_currency = formatCurrency(0, $currency->currency_code, $currencyObject ? $currencyObject->locale : 'en-US', true);
                foreach ($orders as $index => $order) {
                    if ($order->currency_code === $currency->currency_code) {
                        $currency->last_30_count = $order->count;
                        $currency->last_30_sales = formatCurrency($order->sales, 'USD', 'en-US', true);
                        $currency->last_30_sales_in_currency = formatCurrency($order->sales_in_currency, $currency->currency_code, $currencyObject ? $currencyObject->locale : 'en-US', true);
                        unset($orders[$index]);
                        break;
                    }
                }
            }
            return $currencies;
        });
    }
}
