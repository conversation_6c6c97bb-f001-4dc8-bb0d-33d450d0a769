<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TrademarkList;
use App\Traits\ApiResponse;
use Illuminate\Database\QueryException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class TrademarkListController extends Controller
{
    use ApiResponse;

    public function index(Request $request): JsonResponse
    {
        $limit = $request->post('per_page', 15);
        $keyword = $request->get('q');
        $result = TrademarkList::query()
            ->where('supplier_id', 0)
            ->when(!empty($keyword), function ($q) use ($keyword) {
                $q->where('text', 'like', "%{$keyword}%");
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->successResponse($result);
    }

    public function store(Request $request): JsonResponse
    {
        $data = $request->validate([
            'text' => [
                'required',
                'string',
                'max:255',
                Rule::unique('trademark_list', 'text')
            ],
        ]);

        $text = $data['text'];

        try {
            $result = TrademarkList::query()->create(['text' => $text]);
        } catch (QueryException $e) {
            return $this->errorResponse('Duplicated items.');
        }

        if ($result->wasRecentlyCreated) {
            try {
                syncClearCache('trademarks');
            } catch (\Throwable $e) {
                return $this->errorResponse($e->getMessage());
            }
            return $this->successResponse(['id' => $result->id]);
        }

        return $this->errorResponse();
    }

    public function update(Request $request, $id): JsonResponse
    {
        $data = $request->validate([
            'text' => [
                'required',
                'string',
                'max:255',
                Rule::unique('trademark_list', 'text')->ignore($id)
            ],
        ]);

        $text = $data['text'];
        $tm = TrademarkList::query()->firstWhere('id', $id);

        if (!$tm) {
            return $this->errorResponse('Trademark text not exists.');
        }

        $tm->update(['text' => $text]);
        try {
            syncClearCache('trademarks');
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse(['id' => $id]);
    }

    public function destroy($id): JsonResponse
    {
        $deleted = TrademarkList::query()
            ->where('id', $id)
            ->delete();

        if ($deleted) {
            try {
                syncClearCache('trademarks');
            } catch (\Throwable $e) {
                return $this->errorResponse($e->getMessage());
            }
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function updateFlag(Request $request, $id): JsonResponse
    {
        $data = $request->validate([
            'type' => [
                'required',
                'string',
            ],
            'state' => [
                'required',
                'bool',
            ],
        ]);
        $type = $data['type'];
        $state = (int) $data['state'];
        $tm = TrademarkList::query()->firstWhere('id', $id);
        if (!$tm) {
            return $this->errorResponse('Trademark text not exists.');
        }
        if ($type === 'accept_logo' && $state) {
            $tm->block_text = 0;
            $tm->block_logo = 0;
        } else if (($type === 'block_logo' || $type === 'block_text') && $state) {
            $tm->accept_logo = 0;
        }
        $tm->{$type} = $state;
        $tm->save();
        try {
            syncClearCache('trademarks');
        } catch (\Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse(['id' => $id]);
    }
}
