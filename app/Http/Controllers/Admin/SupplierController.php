<?php

namespace App\Http\Controllers\Admin;

use App\Enums\AccessExternalApiType;
use App\Enums\CacheKeys;
use App\Enums\EnvironmentEnum;
use App\Enums\ProductType;
use App\Events\SupplierResetPasswordRequested;
use App\Http\Controllers\ApiKeyController;
use App\Http\Controllers\Controller;
use App\Http\Requests\ApiKey\GenerateRequest;
use App\Http\Requests\Auth\ResetPasswordRequest;
use App\Http\Requests\Supplier\CreateSupplierRequest;
use App\Http\Requests\Supplier\CreateSupplierShippingLateRuleRequest;
use App\Http\Requests\Supplier\DeleteSupplierShippingLateRuleRequest;
use App\Http\Requests\Supplier\UpdateSupplierRequest;
use App\Http\Requests\Supplier\UpdateSupplierShippingLateRuleRequest;
use App\Http\Requests\User\UpdatePasswordRequest;
use App\Models\FulfillProduct;
use App\Models\PasswordReset;
use App\Models\Product;
use App\Models\ProductFulfillMapping;
use App\Models\Supplier;
use App\Models\SupplierShippingLateRule;
use App\Models\TrademarkList;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Services\SupplierService;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Throwable;

class SupplierController extends Controller
{
    use ApiResponse;

    public function index(Request $request)
    {
        // admin > /fulfill/products
        // this page use same API as /supplier
        // we need to check current page
        $ref = $request->query('ref');
        $permission = $ref === 'order_page' ? 'get_orders' : 'get_products';

        currentUser()->hasPermissionOrAbort($permission);

        $tmSupport = $request->get('tm_support');
        $locationCode = $request->get('location_code');
        $status = $request->get('status');
        $sortField = $request->get('sort');
        $sortDirection = $request->get('direction');
        $sortableFields = ['total_products', 'total_orders', 'total_items', 'total_sales'];

        $query = Supplier::query();

        if (isset($status)) {
            $query->where('status', $status);
        }

        if (!empty($tmSupport)) {
            $query->where('tm_support', $tmSupport);
        }

        if (!empty($locationCode)) {
            $query->where('location', $locationCode);
        }

        if (!empty($sortField) && in_array($sortField, $sortableFields, true)) {
            $query->orderBy($sortField, $sortDirection);
        }

        return $query->get();
    }

    public function detail(Request $request): JsonResponse
    {
        $supplierId = $request->supplier_id;
        $supplier   = Supplier::query()->find($supplierId);
        $supplier->append('trademark_keywords');
        // find or create api key for this supplier
        $request = new GenerateRequest(['referenceId' => $supplierId]);
        try {
            $token = (new ApiKeyController())->generate($request, AccessExternalApiType::SUPPLIER, true);
        } catch (Throwable $e) {
            $token = '';
        }

        if (is_null($supplier)) {
            return $this->errorResponse();
        }

        $supplier->hasApiCrawlProduct = false;
        if (
            ObjectFulfill::checkSupplierHaveAPI('crawl_product', $supplierId)
            ||
            ObjectFulfill::checkSupplierHaveAPI('crawl_variant', $supplierId)
        ) {
            $supplier->hasApiCrawlProduct = true;
        }
        $supplier->accessToken = $token;
        return $this->successResponse($supplier);
    }

    public function createTrademarkKeyword(Request $request, int $id): JsonResponse
    {
        $keyword = $request->get('keyword');
        $keyword = trim($keyword);
        if ($this->isTrademarkKeywordExists($id, $keyword)) {
            return $this->errorResponse('Keyword already exists');
        }

        TrademarkList::query()
            ->create([
                'supplier_id' => $id,
                'text' => $keyword,
                'block_logo' => 0,
                'block_text' => 0,
                'accept_logo' => 0,
            ]);

        return $this->successResponse('Keyword created');
    }

    public function deleteTrademarkKeyword(Request $request, int $id)
    {
        $keyword = $request->get('keyword');
        $success = TrademarkList::query()
            ->where('supplier_id', $id)
            ->where('text', $keyword)
            ->delete();

        if ($success) {
            return $this->successResponse('Keyword deleted');
        } else {
            return $this->errorResponse('Keyword not found');
        }
    }

    public function editTrademarkKeyword(Request $request, int $id)
    {
        $keyword = $request->get('keyword');
        $editKeyword = $request->get('edit_keyword');
        if ($this->isTrademarkKeywordExists($id, $editKeyword)) {
            return $this->errorResponse('New keyword already exists');
        }

        $success = TrademarkList::query()
            ->where('supplier_id', $id)
            ->where('text', $keyword)
            ->update(['text' => $editKeyword]);

        if ($success) {
            return $this->successResponse('Keyword updated');
        } else {
            return $this->errorResponse('Keyword not found');
        }
    }

    private function isTrademarkKeywordExists($supplierId, $keyword)
    {
        return TrademarkList::query()
            ->where('supplier_id', $supplierId)
            ->where('text', $keyword)
            ->exists();
    }

    public function listingProducts(Request $request): JsonResponse
    {
        $supplierId = $request->supplier_id;
        $products = Product::query()
            ->where('supplier_id', $supplierId)
            ->orderByDesc('id')
            ->get();

        return $products->isNotEmpty()
            ? $this->successResponse($products)
            : $this->errorResponse();
    }

    /**
     * @param CreateSupplierRequest $request
     * @return JsonResponse
     */
    public function create(CreateSupplierRequest $request): JsonResponse
    {
        $supplierId  = $request->post('id');
        $newSupplier = [
            'id'                    => $supplierId,
            'name'                  => $request->post('name'),
            'email'                 => $request->post('email'),
            'password'              => $request->post('password'),
            'phone_number'          => $request->post('phone_number'),
            'address'               => $request->post('address'),
            'website'               => $request->post('website'),
            'location'              => $request->post('location'),
            'max_items'             => $request->post('max_items'),
            'is_separate_name'      => $request->post('is_separate_name'),
        ];

        try {
            Supplier::query()->insert($newSupplier);

            syncClearCache([CacheKeys::SYSTEM_SUPPLIERS], CacheKeys::CACHE_TYPE_ALTERNATIVE);

            return $this->successResponse([
                'supplier_id' => $supplierId
            ]);
        } catch (Exception $e) {
            return $this->errorResponse('Create supplier failed');
        }
    }

    /**
     * @param UpdateSupplierRequest $request
     * @return JsonResponse
     */
    public function update(UpdateSupplierRequest $request): JsonResponse
    {
        $supplierId = $request->post('supplier_id');
        $newSupplier = [
            'name' => $request->post('name'),
            'email' => $request->post('email'),
            'phone_number' => $request->post('phone_number'),
            'address' => $request->post('address'),
            'location' => $request->post('location'),
            'website' => $request->post('website'),
            'dashboard_url' => $request->post('dashboard_url'),
            'dashboard_order_url' => $request->post('dashboard_order_url'),
            'status' => $request->post('status'),
            'max_items' => $request->post('max_items'),
            'is_separate_name' => $request->post('is_separate_name'),
            'no_express_line_ship' => $request->post('no_express_line_ship'),
            'additional_print_space' => $request->post('additional_print_space', 0),
            'support_tiktok_shop' => $request->post('support_tiktok_shop', 0),
        ];


        try {
            $supplier = Supplier::query()->findOrFail($supplierId);
            $supplier->fill($newSupplier);
            if ($supplier->isDirty()) {
                $supplier->save();
                $cacheKeys   = [];
                $cacheKeys[] = CacheKeys::SYSTEM_SUPPLIERS;

                if ($supplier->wasChanged('location')) {
                    $templateIds = ProductFulfillMapping::query()
                        ->where('supplier_id', $supplierId)
                        ->pluck('product_id');

                    foreach ($templateIds as $templateId) {
                        $cacheKeys['tags'][] = CacheKeys::getTemplateFulfillProduct($templateId);
                    }
                }
                syncClearCache($cacheKeys, CacheKeys::CACHE_TYPE_ALTERNATIVE);
            }

            Supplier::getRefreshCacheById($supplierId);

            return $this->successResponse([
                'supplier_id' => $supplierId
            ]);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function saveSupplierProducts(Request $request): JsonResponse
    {
        $supplierId = $request->post('supplier_id');
        $products = $request->post('products');
        $productIds = array_column($products, 'id');

        try {
            DB::beginTransaction();
            FulfillProduct::query()
                ->whereNotIn('id', $productIds)
                ->where('supplier_id', $supplierId)
                ->delete();

            if (!is_null($products) && count($products) > 0) {
                array_map(static function ($product) {
                    if (!is_null($product['id'])) {
                        Product::query()
                            ->where('id', $product['id'])
                            ->update($product);
                    } else {
                        $query = Product::query()
                            ->where([
                                'template_id' => $product['template_id'],
                                'supplier_id' => $product['supplier_id']
                            ]);

                        if ($query->exists()) {
                            $query->restore();
                            unset($product['id']);
                            $query->update($product);
                        } else {
                            $product['product_type'] = ProductType::FULFILL_PRODUCT;
                            Product::query()->insert($product);
                        }
                    }
                }, $products);
            }

            DB::commit();

            return $this->successResponse(
                ['supplier_id' => $supplierId],
                'Update products success'
            );
        } catch (Exception $e) {
            DB::rollBack();
            return $this->errorResponse($e);
        }
    }

    public function listing(): JsonResponse
    {
        $suppliers = Supplier::getAndCache();

        return $suppliers->count() > 0
            ? $this->successResponse($suppliers)
            : $this->errorResponse();
    }

    /**
     * @param Supplier $supplier
     * @return JsonResponse
     */
    public function supplierCreateAccount(Supplier $supplier): JsonResponse
    {
        $password = Str::random(12);
        $hashedPassword = Hash::make($password);

        $updateSuccess = $supplier->update([
            'password' => $hashedPassword,
            'status' => 1,
        ]);

        if(!$updateSuccess) {
            return $this->errorResponse('Update Failed');
        }

        if (!$supplier->hasRole('supplier')) {
            $supplier->assignRole('supplier');
        }

        return $this->successResponse([
            'supplier_id' => $supplier->id,
            'email' => $supplier->email,
            'password' => $password,
        ]);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function forgotPassword(Request $request): JsonResponse
    {
        $request->validate(['email' => 'required|email']);
        $email = $request->post('email');

        try {
            $supplier = Supplier::query()->firstWhere('email', $email);

            if (is_null($supplier) || is_null($supplier->password)) {
                // we return success response to prevent bruce-force
                // todo: use recaptcha instead
                return $this->successResponse();
            }

            if (!$supplier->status) {
                return $this->errorResponse('Your account has been locked. Please contact admin for more information.', 403);
            }

            $count = PasswordReset::query()
                ->where('email', $email)
                ->timeLimit()
                ->count();

            if ((app()->environment(EnvironmentEnum::PRODUCTION))
                && $count >= config('auth.passwords.users.limit_per_try')) {
                return $this->errorResponse('You have reached limit reset password. Please try again in ' . PasswordReset::LIMIT_HOUR . ' hours.');
            }

            SupplierResetPasswordRequested::dispatch($supplier);

            return $this->successResponse();
        } catch (Exception $e) {
            logToDiscord("{$email} have reset password failed.");
            return $this->errorResponse();
        }
    }

    /**
     * @param ResetPasswordRequest $request
     * @return JsonResponse
     */
    public function resetPassword(ResetPasswordRequest $request): JsonResponse
    {
        try {
            $token = $request->post('token');
            $password = $request->post('password');
            $email = PasswordReset::timeLimit()
                ->where('token', $token)
                ->value('email');

            PasswordReset::query()
                ->where('email', $email)
                ->update(['token' => null]);

            $supplier = Supplier::query()->firstWhere('email', $email);

            if (is_null($supplier)) {
                return $this->errorResponse();
            }

            if (!$supplier->status) {
                return $this->errorResponse('Your account has been locked. Please contact admin for more information.', 403);
            }

            $supplier->password = Hash::make($password);
            $supplier->save();
            return $this->successResponse();
        } catch (Exception $e) {
            logToDiscord('Reset password failed: ' . $e->getMessage());
            return $this->errorResponse();
        }
    }

    /**
     * @param UpdatePasswordRequest $request
     * @return JsonResponse
     */
    public function updatePassword(UpdatePasswordRequest $request): JsonResponse
    {
        $newPassword = $request->post('new_password');
        $updated = Supplier::query()
            ->where('id', currentUser()->getUserId())
            ->update([
                'password' => Hash::make($newPassword)
            ]);
        return $updated > 0 ? $this->successResponse() : $this->errorResponse();
    }

    /**
     * @return JsonResponse
     */
    public function createSupplierShippingLateRule(CreateSupplierShippingLateRuleRequest $request, $supplierId): JsonResponse
    {
        $rule = [
            'shipping_method' => $request->post('shipping_method'),
            'location' => $request->post('location') ?? [],
            'no_location' => $request->post('no_location') ?? [],
            'date_late' => $request->post('date_late'),
        ];
        try{
            $response = SupplierService::createSupShippingLateRule($rule, $supplierId);
            if(!$response['completed']) {
                return $this->errorResponse($response['message']);
            }
            return $this->successResponse();
        }catch(\Exception $e){

            return $this->errorResponse($e);
        }
    }

     /**
     * @return JsonResponse
     */
    public function updateSupplierShippingLateRule(UpdateSupplierShippingLateRuleRequest $request, $supplierId, $shippingLateRuleId): JsonResponse
    {
        $rule = [
            'shipping_method' => $request->post('shipping_method'),
            'location' => $request->post('location') ?? [],
            'no_location' => $request->post('no_location') ?? [],
            'date_late' => $request->post('date_late'),
        ];
        try{
            $response = SupplierService::updateSupShippingLateRule($rule, $supplierId, $shippingLateRuleId);
            if(!$response['completed']) {
                return $this->errorResponse($response['message']);
            }
            return $this->successResponse();
        }catch(\Exception $e){
            return $this->errorResponse($e);
        }
    }

     /**
     * @return JsonResponse
     */
    public function getSupplierShippingLateRule($supplierId): JsonResponse
    {
        if (!SupplierShippingLateRule::where('supplier_id', $supplierId)->exists()) {
            return $this->errorResponse('Shipping late rule for this supplier not found !');
        }
        try{
            $rules = SupplierService::getSupShippingLateRule($supplierId);
            return $this->successResponse($rules);
        }catch(\Exception $e){
            return $this->errorResponse($e);
        }
    }

    /**
     * @return JsonResponse
     */
    public function deleteSupplierShippingLateRule(DeleteSupplierShippingLateRuleRequest $request, $supplierId, $shippingLateRuleId): JsonResponse
    {
        try{
            $response = SupplierService::deleteShippingRule($supplierId, $shippingLateRuleId);
            if(!$response['completed']) {
                return $this->errorResponse($response['message']);
            }
            return $this->successResponse();
        }catch(\Exception $e){
            return $this->errorResponse($e);
        }
    }

    public function exportTrademarkKeywords(int $supplierId)
    {
        $supplier = Supplier::query()->find($supplierId);
        if (is_null($supplier)) {
            return $this->errorResponse('Supplier not found');
        }

        $keywords = TrademarkList::query()
            ->where('supplier_id', $supplierId)
            ->orWhere('supplier_id', 0)
            ->orderBy('supplier_id', 'desc')
            ->orderBy('id', 'desc')
            ->get()
            ->pluck('text')
            ->toArray();

        return response()->streamDownload(function () use ($keywords) {
            $file = fopen('php://output', 'w');
            fputcsv($file, ['Keyword']);
            foreach ($keywords as $keyword) {
                fputcsv($file, [$keyword]);
            }
            fclose($file);
        }, 'trademark_keywords.csv');
    }
}

