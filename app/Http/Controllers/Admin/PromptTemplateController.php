<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\SystemConfig;
use App\Services\OpenAI;
use App\Traits\ApiResponse;
use App\Traits\ChatGptPrompt;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PromptTemplateController extends Controller
{
    use ApiResponse;
    use ChatGptPrompt;

    private const KEY = 'crisp_cs_prompt_template';
    private string $template;
    private string $orderNumber;
    private array $contactData;

    public function index(): JsonResponse
    {
        $template = SystemConfig::firstWhere('key', self::KEY);

        if ($template === null || !$template->json_data) {
            return $this->errorResponse();
        }

        try {
            $json = json_decode($template->json_data);

            if (isset($json->template)) {
                return $this->successResponse($json->template);
            }

            return $this->errorResponse('Invalid template');
        } catch (\Throwable $e) {
            // cannot decode json
            return $this->errorResponse($e->getMessage());
        }
    }

    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'template' => 'required|string',
        ]);

        $template = $request->post('template');
        $json = json_encode(['template' => $template]);

        $data = SystemConfig::updateOrCreate(
            ['key' => self::KEY],
            ['json_data' => $json, 'status' => 1]
        );

        if ($data->wasRecentlyCreated || $data->wasChanged()) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function showOrder($orderId): JsonResponse
    {
        $order = Order::query()
            ->where('id', $orderId)
            ->with('products:order_id,product_name,price,quantity,tracking_url,delivered_at,fulfill_status,shipping_cost')
            ->first();

        if (!$order) {
            return $this->errorResponse('Order not found');
        }

        return $this->successResponse($order);
    }

    public function test(Request $request, $orderId): ?JsonResponse
    {
        $template = $request->post('prompt');
        $subject = $request->post('subject');
        $message = $request->post('message');

        if (!$template) {
            return $this->errorResponse('Prompt is required');
        }

        $this->template = $template;

        $order = Order::query()
            ->where('id', $orderId)
            ->with('products:order_id,product_name,price,quantity,tracking_url,delivered_at,fulfill_status,shipping_cost')
            ->first();

        if (!$order) {
            return $this->errorResponse('Order not found');
        }

        $products = '';

        if ($order->products->isNotEmpty()) {
            $products = $order->products
                ->map(
                    fn($product) => $product->quantity . ' x ' . $product->product_name . ' $' . $product->price
                        . ($product->tracking_url ? ', Tracking URL: ' . $product->tracking_url : '')
                        . ($product->delivered_at ? ', Delivered at: ' . self::formatDate($product->delivered_at) : '')
                        . ($product->fulfill_status ? ', Fulfillment Status: ' . $product->fulfill_status : '')
                        . ($product->shipping_cost ? ', Shipping Cost: $' . $product->shipping_cost : '')
                )
                ->implode('; ');
        }

        // create sample data
        $this->orderNumber = $order->order_number;
        $this->contactData = [
            'customer_name' => $order->customer_name,
            'customer_email' => $order->customer_email,
            'subject' => $subject,
            'message' => $message,
            'store_domain' => $order->store_name,
        ];

        $userPrompt = $this->generateChatGptPrompt($order, $products);

        $prompt = [
            [
                'role' => 'system',
                'content' => 'You are customer service agent. You are helping a customer with their order.',
            ],
            [
                'role' => 'user',
                'content' => $userPrompt
            ]
        ];

        try {
            $chatGptResponse = OpenAI::completions($prompt);
            return $this->successResponse($chatGptResponse);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }
}
