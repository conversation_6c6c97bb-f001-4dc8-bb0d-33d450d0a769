<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Jobs\Seller\ProcessNewDomainJob;
use App\Models\Store;
use App\Models\StoreDomain;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class StoreDomainController extends Controller
{
    use ApiResponse;

    public function __invoke(Request $request): JsonResponse
    {
        $request->validate([
            'domains' => 'required|array',
            'domains.*' => 'required|string',
            'update_namedotcom_nameserver' => 'required|boolean',
            'force_create_zone' => 'required|boolean',
        ]);

        $domains = $request->post('domains');
        $isUpdateNameDotComNameserver = $request->boolean('update_namedotcom_nameserver', true);
        $forceCreateZone = $request->boolean('force_create_zone', false);

        if ($isUpdateNameDotComNameserver) {
            foreach ($domains as $domain) {
                $store = self::findStoreByDomain($domain);

                if (!$store) {
                    continue;
                }

                $sellerId = $store->seller_id;
                $isCustomStore = $store->seller->custom_payment && $store->is_proxy;

                ProcessNewDomainJob::dispatchAfterResponse(
                    $domain,
                    $isCustomStore,
                    $sellerId,
                    $isUpdateNameDotComNameserver,
                    $forceCreateZone
                );
            }

            return $this->successResponse();
        }

        // only one domain is allowed
        $domain = $domains[0];
        $store = self::findStoreByDomain($domain);

        if (!$store) {
            return $this->errorResponse('Store not found');
        }

        $sellerId = $store->seller_id;
        $isCustomStore = $store->seller->custom_payment && $store->is_proxy;
        ProcessNewDomainJob::dispatchSync(
            $domain,
            $isCustomStore,
            $sellerId,
            $isUpdateNameDotComNameserver,
            $forceCreateZone
        );

        // get result from cache (set by ProcessNewDomainJob)
        $cacheKey = 'cloudflare_nameserver_' . md5($domain);
        $nameserver = Cache::get($cacheKey);

        if (!$nameserver) {
            return $this->errorResponse('Cannot get nameserver from Cloudflare');
        }

        return $this->successResponse([
            'nameserver' => $nameserver,
        ]);
    }

    private static function findStoreByDomain(string $domain): ?Store
    {
        $fields = [
            'id',
            'domain',
            'seller_id',
            'is_proxy',
        ];

        $store = Store::where('domain', $domain)
            ->with('seller:id,custom_payment')
            ->first($fields);

        if ($store) {
            return $store;
        }

        // if null, find store ID from store_domain table
        $storeId = StoreDomain::where('domain', $domain)
            ->value('store_id');

        if (!$storeId) {
            return null;
        }

        return Store::where('id', $storeId)
            ->with('seller:id,custom_payment')
            ->first($fields);
    }
}
