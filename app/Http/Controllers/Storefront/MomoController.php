<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\PaymentMethodEnum;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Services\OrderService;
use App\Services\StoreService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use PHPViet\Laravel\Omnipay\Facades\MoMo\AllInOneGateway as MoMoGateway;

/**
 * Docs: https://github.com/phpviet/laravel-omnipay/blob/master/docs/momo/AllInOne.md
 */
class MomoController extends Controller
{
    use ApiResponse;

    public function createOrder(Request $request): JsonResponse
    {
        $postData = $request->validate(['order_token' => 'bail|required|string']);
        $orderToken = $postData['order_token'];
        $order = self::getOrderByToken($orderToken);

        if (!$order) {
            return $this->errorResponse('Order not found.');
        }

        //  check if acceptedCurrency
        $currencyCode = OrderService::checkAcceptableCurrency($order->currency_code, PaymentMethodEnum::MOMO);

        // if not USD, multi to rate
        $amount = OrderService::getAmountByCurrency($order->total_amount, $currencyCode, $order->currency_rate);

        $orderData = [
            'orderId' => $order->order_number,
            'amount' => round($amount),
            'returnUrl' => self::getReturnUrl($orderToken),
            'notifyUrl' => 'https://senprints.com/api/public/order/momo-notify-url/' . urlencode($orderToken),
            'requestId' => Str::uuid()->toString(),
        ];

        $response = MoMoGateway::purchase($orderData)->send();

        if ($response->isRedirect()) {
            return $this->successResponse($response->getRedirectUrl());
        }

        return $this->errorResponse($response->getMessage());
    }

    public function verifyPurchase(Request $request, $orderToken): JsonResponse
    {
        $request->validate(['request_id' => 'bail|required|string']);

        $order = self::getOrderByToken($orderToken);

        if (!$order) {
            return $this->errorResponse('Order not found.');
        }

        $response = MoMoGateway::queryTransaction([
            'orderId' => $order->order_number,
            'requestId' => $request->query('request_id'),
        ])->send();

        $arr = $response->getData();

        return (isset($arr['errorCode']) && $arr['errorCode'] === 0)
            ? $this->successResponse()
            : $this->errorResponse($arr['localMessage']);
    }

    public function handleNotify(Request $request, string $orderToken): void
    {
        // get total_amount
        $order = Order::query()->firstWhere('access_token', $orderToken);

        if (!$order) {
            return;
        }

        $transactionId = $request->post('transId');

        if ((int)$request->post('errorCode') === 0) {
            $order->paymentCompleted($order->total_amount, $transactionId);
            return;
        }

        $order->paymentFailed($request->post('localMessage'), $transactionId);
    }

    private static function getOrderByToken($token)
    {
        return Order::query()
            ->select([
                'id',
                'order_number',
                'total_amount',
                'currency_code',
                'currency_rate'
            ])
            ->firstWhere('access_token', $token);
    }

    private static function getReturnUrl($orderToken): string
    {
        $urlEncodedToken = urlencode($orderToken);
        $domain = StoreService::getDomain();

        if (strpos($domain, '.') === false) {
            $domain .= '.' . getStoreBaseDomain();
        }

        $baseUrl = 'https://' . $domain . '/';
        return $baseUrl . 'checkout/momo?orderToken=' . $urlEncodedToken;
    }
}
