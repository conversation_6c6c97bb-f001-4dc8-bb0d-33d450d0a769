<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\ProductStatus;
use App\Http\Controllers\Controller;
use App\Jobs\GeneralRegionOrderJob;
use App\Models\Campaign;
use App\Models\Slug;
use App\Services\CampaignService;
use App\Traits\ApiResponse;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReportCampaignController extends Controller
{
    use ApiResponse;

    public function store(Request $request): JsonResponse
    {
        // todo: validate

        $slug = $request->post('campaign_slug');

        // get campaign by slug
        $seller = Slug::query()
            ->where([
                'slug' => $slug,
            ])
            ->first()?->seller;
        $campaign = Campaign::query()
            ->onSellerConnection($seller)
            ->firstWhere([
                'slug' => $slug,
                'status' => ProductStatus::ACTIVE,
            ]);

        if (!$campaign) {
            return $this->errorResponse('Campaign not found.');
        }

        $data = $request->only([
            'reason',
            'email',
            'name',
            'phone',
            'address',
            'address_2',
            'city',
            'state',
            'postcode',
            'country'
        ]);

        // check if is copyright
        $reason = $request->post('reason');

        if (is_array($reason)) {
            // convert to string for "set" data type
            $data['reason'] = implode(',', $reason);
        }

        $data['seller_id'] = $campaign->seller_id;
        $data['campaign_id'] = $campaign->id;
        $data['ip_address'] = $request->ip();

        $additionalInfo = $request->post('additional_info');

        if ($additionalInfo) {
            $data['additional_info'] = json_encode($additionalInfo);
        }

        try {
            GeneralRegionOrderJob::dispatch(
                CampaignService::class,
                'storeReportedCampaign',
                [$data, $campaign, $slug]
            );

            return $this->successResponse();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->errorResponse();
        }
    }
}
