<?php

namespace App\Http\Controllers\Storefront;

use App\Enums\OrderAddressVerifiedEnum;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderHistoryDisplayLevelEnum;
use App\Http\Controllers\Controller;
use App\Jobs\GeneralRegionOrderJob;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\Staff;
use App\Services\AddressService;
use App\Services\CustomerService;
use App\Services\MailService;
use App\Traits\ApiResponse;
use App\Traits\CorrectFulfillStatus;
use App\Traits\SetOrderCustomerInfo;
use Carbon\Carbon;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Modules\OrderService\Services\RegionOrderService;

class CustomerController extends Controller
{
    use ApiResponse, SetOrderCustomerInfo, CorrectFulfillStatus;

    public function update(Request $request): JsonResponse
    {
        try {
            $userIds = Arr::get($request->header(), 'x-user-id');
            $userId = !empty($userIds) ? reset($userIds) : null;
            $request->validate([
                'customer_email' => 'bail|required|email',
                'order_number' => 'required|string',
                'address' => 'required|string',
                'city' => 'required|string',
                // 'state' => 'required|string',
                'postcode' => 'required|string',
                'country' => 'required|string',
            ]);

            $allRequest = $request->all();
            GeneralRegionOrderJob::dispatch(
                CustomerService::class,
                'updateCustomerInfo',
                [$allRequest, $userId]
            );
            return $this->successResponse();
        } catch (\Exception $e) {
            logToDiscord('CustomerController@update: ' . $e->getMessage());
            return $this->errorResponse();
        }
    }

    public function verifyAddress(string $orderToken): JsonResponse
    {
        $order = Order::query()
            ->select([
                'address',
                'city',
                'state',
                'postcode',
                'country',
                'customer_phone'
            ])
            ->firstWhere('access_token', $orderToken);

        if (is_null($order)) {
            return $this->errorResponse();
        }

        if (AddressService::verify($order)) {
            return $this->successResponse();
        }

        return $this->errorResponse();
    }

    public function confirmAddress(string $orderToken): JsonResponse
    {
        try {
            $order = Order::query()->select(['id', 'fulfill_status'])
                ->whereIn('fulfill_status', [
                    OrderFulfillStatus::UNFULFILLED,
                    OrderFulfillStatus::INVALID,
                    OrderFulfillStatus::ON_HOLD,
                    OrderFulfillStatus::REVIEWING,
                    OrderFulfillStatus::DESIGNING,
                ])
                ->firstWhere('access_token', $orderToken);

            if ($order === null) {
                return $this->errorResponse('Invalid order');
            }
            DB::beginTransaction();
            $updateData = [];
            if (AddressService::verify($order)) {
                $updateData['address_verified'] = OrderAddressVerifiedEnum::CONFIRMED;
                $updated = Order::query()->where('id', $order->id)->update($updateData);
                if ($updated) {
                    $order = Order::query()->whereKey($order->id)->first();
                    OrderHistory::insertLog(
                        $order,
                        OrderHistoryActionEnum::CUSTOMER_CONFIRM_ADDRESS,
                        "Customer confirm address is valid"
                    );
                }
                DB::commit();
                return $updated ? $this->successResponse() : $this->errorResponse();
            }
            DB::commit();
            if ($order->paid_at && Carbon::parse($order->paid_at)->addHours(12)->gt(now())) {
                return $this->errorResponse('Your can not confirm address at this time.');
            }
            return $this->errorResponse('Your address is not valid');
        } catch (\Exception $e) {
            DB::rollBack();
            logToDiscord('CustomerController@confirmAddress: ' . $e->getMessage());
            return $this->errorResponse('Your address is not valid');
        }
    }

    /**
     * @param Request $request
     * @return Application|\Illuminate\Foundation\Application|RedirectResponse|Redirector|string
     */
    public function confirmAddressByEmail(Request $request) {
        try {
            DB::beginTransaction();
            $orderToken = $request->get('order_token');
            $token = $request->get('token');
            $hashId = $request->get('hashId');
            $userId = $request->get('userId');
            if ($hashId) {
                GeneralRegionOrderJob::dispatch(
                    CustomerService::class,
                    'updateClickSendMailLogHandler',
                    [$hashId]
                );
            }
            if ($token && $orderToken) {
                $instance = RegionOrderService::regionOrderModelInstance($orderToken);
                $orderQuery = $instance['order'];
                $orderHistoryQuery = $instance['order_history'];
                $order = $orderQuery
                    ->where('access_token', $orderToken)
                    ->first();
                $staff = Staff::query()->find($userId);
                $staffEmail = $staff?->email;
                $redirectUrlParams = '';
                if ($staff) {
                    $displayLevel = OrderHistoryDisplayLevelEnum::ADMIN;
                    $action =  OrderHistoryActionEnum::ADMIN_CONFIRM_ADDRESS;
                    $detail = 'Admin';
                    $redirectUrlParams = "?user_id=$staff->id";
                } else {
                    $displayLevel = OrderHistoryDisplayLevelEnum::CUSTOMER;
                    $action =  OrderHistoryActionEnum::CUSTOMER_CONFIRM_ADDRESS;
                    $detail = 'Customer';
                }

                if ($order) {
                    if (MailService::isAddressInvalidMailTokenValid($token, $order->id) && in_array($order->fulfill_status, OrderFulfillStatus::canConfirmAddress(), true)) {
                        $order->address_verified = OrderAddressVerifiedEnum::CONFIRMED;
                        if ($order->getModel() instanceof Order) {
                            $insertData = [
                                $order,
                                $action,
                                "$detail confirm address is valid",
                                $displayLevel,
                                $staffEmail
                            ];
                            GeneralRegionOrderJob::dispatch(
                                CustomerService::class,
                                'saveConfirmAddressEmailInRegion',
                                [$order, $orderHistoryQuery->getModel(), $insertData]
                            );
                        } else {
                            $order->save();
                            $orderHistoryQuery->getModel()::insertLog(
                                $order,
                                $action,
                                "$detail confirm address is valid",
                                $displayLevel,
                                $staffEmail
                            );
                        }


                        $order = $order->refresh();
                    }
                    $baseUrl = MailService::generateBaseUrl($order);
                    DB::commit();

                    return redirect($baseUrl . '/order/status/' . $orderToken . $redirectUrlParams);
                }
            }
            DB::commit();
            return 'Email expired';
        } catch (\Exception $e) {
            DB::rollBack();
            logToDiscord('CustomerController@confirmAddressByEmail: ' . $e->getMessage());
            return $e->getMessage();
        }
    }
}
