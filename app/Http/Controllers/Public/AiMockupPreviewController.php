<?php

namespace App\Http\Controllers\Public;

use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use App\Enums\FileTypeEnum;
use App\Enums\OpenAIImageFormatEnum;
use App\Enums\OpenAIImageQualityEnum;
use App\Enums\OpenAIImageSizeEnum;
use App\Http\Controllers\Controller;
use App\Models\AiPrompt;
use App\Models\File;
use App\Models\User;
use App\Rules\FileExistsRule;
use App\Services\OpenAIImageService;
use App\Traits\ApiResponse;
use App\Traits\MockupPromptBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AiMockupPreviewController extends Controller
{
    use ApiResponse, MockupPromptBuilder;

    /**
     * Generate a preview image with personalized text
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generatePreview(Request $request): JsonResponse
    {
        // Increase PHP execution time limit for this request
        set_time_limit(120); // 2 minutes

        // Get seller information first for validation
        $sellerId = $request->input('seller_id');
        $seller = null;

        if ($sellerId) {
            $seller = User::query()->find($sellerId);
            if (!$seller) {
                return $this->errorResponse('Seller not found', 404);
            }
        }

        $validator = Validator::make($request->all(), [
            'edit_image' => 'required|file|mimes:jpeg,png,webp|max:25600', // max 25MB (image to edit - currentProductThumbUrl)
            'customer_upload_image' => 'nullable|file|mimes:jpeg,png,webp|max:25600', // max 25MB (customer uploaded reference image)
            'campaign_id' => 'required|integer',
            'product_id' => 'required|integer',
            'personalization_values' => 'required|json', // JSON string with personalization values
            'color' => 'nullable|string|max:50',
            'color_hex' => 'nullable|string|max:7',
            'thumbnail_file_id' => ['nullable', 'integer', new FileExistsRule($seller)], // Optional thumbnail file ID with custom validation
            'is_customer_upload_image' => 'nullable|string|in:true,false', // Flag to indicate if reference image is customer uploaded
            'seller_id' => 'nullable|integer', // Optional seller ID sent from frontend
        ]);

        if ($validator->fails()) {
            return $this->errorResponse(
                'Validation error',
                422,
                [],
                ['errors' => $validator->errors()]
            );
        }

        try {
            // Create an instance of OpenAIImageService with increased timeout (90 seconds)
            $openAIService = new OpenAIImageService(90);

            // Get parameters from request
            $campaignId = $request->input('campaign_id');
            $productId = $request->input('product_id');
            $personalizationValues = json_decode($request->input('personalization_values'), true);
            $shirtColor = $request->input('color', 'white');
            $colorHex = $request->input('color_hex');
            $thumbnailFileId = $request->input('thumbnail_file_id');
            $isCustomerUploadImage = $request->input('is_customer_upload_image', 'false') === 'true';

            // Get the original prompt and template information from the database
            $userPrompt = null;
            $templateId = null;
            $basePromptTemplate = null;

            // First try to load File by thumbnail_file_id if provided
            if ($thumbnailFileId) {
                $thumbnailFile = File::query()
                    ->onSellerConnection($seller)
                    ->find($thumbnailFileId);

                if ($thumbnailFile && !empty($thumbnailFile->design_json)) {
                    // Decode design_json to get user_prompt and template_id
                    $designJson = json_decode($thumbnailFile->design_json, true);
                    if (isset($designJson['user_prompt'])) {
                        $userPrompt = $designJson['user_prompt'];
                    }
                    if (isset($designJson['template_id'])) {
                        $templateId = $designJson['template_id'];
                    }
                }
            }

            // Fallback: try to get the prompt from the File table (mockup) if not found above
            if (empty($userPrompt)) {
                $mockup = File::query()
                    ->onSellerConnection($seller)
                    ->where([
                        'campaign_id' => $campaignId,
                        'product_id' => $productId,
                        'type' => FileTypeEnum::IMAGE,
                        'type_detail' => FileRenderType::CUSTOM,
                        'status' => FileStatusEnum::ACTIVE,
                    ])
                    ->first();

                if ($mockup && !empty($mockup->design_json)) {
                    // Extract user_prompt from design_json
                    $designJson = json_decode($mockup->design_json, true);
                    if (isset($designJson['user_prompt'])) {
                        $userPrompt = $designJson['user_prompt'];
                    }
                    // Also get template_id if not already set
                    if (empty($templateId) && isset($designJson['template_id'])) {
                        $templateId = $designJson['template_id'];
                    }
                }
            }

            // If still no prompt found, return error
            if (empty($userPrompt)) {
                return $this->errorResponse('No prompt found for this campaign and product', 404);
            }

            // Get the base prompt template from ai_prompts table using template_id
            if ($templateId) {
                $aiPrompt = AiPrompt::mockupTemplates()
                    ->where('id', $templateId)
                    ->first('prompt');

                if ($aiPrompt) {
                    $basePromptTemplate = $aiPrompt->prompt;
                }
            }

            // Determine which case we're dealing with and build the appropriate prompt
            $hasPersonalizationValues = !empty($personalizationValues) && is_array($personalizationValues) && count($personalizationValues) > 0;

            if ($hasPersonalizationValues && $isCustomerUploadImage) {
                // Case 3: Both custom text and custom photo
                $finalPrompt = $this->buildPromptForTextAndPhoto($basePromptTemplate, $userPrompt, $personalizationValues, $shirtColor, $colorHex);
            } elseif ($isCustomerUploadImage) {
                // Case 2: Only custom photo (customer upload)
                $finalPrompt = $this->buildPromptForPhotoOnly($basePromptTemplate, $userPrompt, $shirtColor, $colorHex);
            } else {
                // Case 1: Only custom text (personalization)
                $finalPrompt = $this->buildPromptForTextOnly($basePromptTemplate, $userPrompt, $personalizationValues, $shirtColor, $colorHex);
            }

            // Handle the images
            $imageToEdit = $request->file('edit_image'); // This is always currentProductThumbUrl
            $customerUploadedImage = $request->file('customer_upload_image'); // This is customer uploaded image (if any)

            // For debugging
            $imageToEditInfo = 'Image to edit: ' . $imageToEdit->getClientOriginalName();
            $customerUploadInfo = $customerUploadedImage ? 'Customer upload: ' . $customerUploadedImage->getClientOriginalName() : 'No customer upload';

            // Use OpenAI to edit the image
            $result = $openAIService->editImage(
                image: $imageToEdit, // Always currentProductThumbUrl
                prompt: $finalPrompt,
                referenceImages: $customerUploadedImage, // Customer uploaded image (if any)
                size: OpenAIImageSizeEnum::PORTRAIT,
                output_format: OpenAIImageFormatEnum::WEBP, // Default to webp format
                quality: OpenAIImageQualityEnum::MEDIUM // Default to medium quality
            );

            if (!$result || isset($result['error'])) {
                return $this->errorResponse($result['message'] ?? 'Failed to generate preview image', 500);
            }

            // Include debug information in the response
            $debugInfo = [
                'finalPrompt' => $finalPrompt,
                'templateId' => $templateId,
                'case' => $hasPersonalizationValues && $isCustomerUploadImage ? 'text_and_photo' : ($isCustomerUploadImage ? 'photo_only' : 'text_only'),
                'requestData' => [
                    'image_to_edit' => $imageToEditInfo,
                    'customer_upload' => $customerUploadInfo,
                    'campaign_id' => $campaignId,
                    'product_id' => $productId,
                    'personalization_values' => $personalizationValues,
                    'color' => $shirtColor,
                    'color_hex' => $colorHex ?? null,
                    'thumbnail_file_id' => $thumbnailFileId ?? null,
                    'is_customer_upload_image' => $isCustomerUploadImage,
                    'seller_id' => $sellerId ?? null,
                    'size' => OpenAIImageSizeEnum::PORTRAIT,
                    'output_format' => OpenAIImageFormatEnum::WEBP,
                    'quality' => OpenAIImageQualityEnum::MEDIUM
                ]
            ];

            // Add debug info to the result
            $result['debug'] = $debugInfo;

            return $this->successResponse($result);
        } catch (\Exception $e) {
            return $this->errorResponse('An error occurred while generating the preview image: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Build prompt for Case 1: Only custom text (personalization)
     * Edit image: currentProductThumbUrl (edit_image parameter)
     * Reference image: None
     *
     * @param string|null $basePromptTemplate
     * @param string $userPrompt
     * @param array|null $personalizationValues
     * @param string $shirtColor
     * @param string|null $colorHex
     * @return string
     */
    private function buildPromptForTextOnly(?string $basePromptTemplate, string $userPrompt, ?array $personalizationValues, string $shirtColor, ?string $colorHex): string
    {
        // Replace personalization placeholders in the user prompt
        $processedUserPrompt = $userPrompt;
        if (!empty($personalizationValues) && is_array($personalizationValues)) {
            foreach ($personalizationValues as $placeholder => $value) {
                // Replace both [PLACEHOLDER] and [placeholder] formats
                $processedUserPrompt = str_replace("[$placeholder]", $value, $processedUserPrompt);
                $processedUserPrompt = str_replace("[" . strtoupper($placeholder) . "]", $value, $processedUserPrompt);
            }
        }

        // Use the trait to build the mockup prompt
        return $this->buildMockupPrompt(
            basePromptTemplate: $basePromptTemplate,
            userPrompt: $processedUserPrompt,
            shirtColor: $shirtColor,
            colorHex: $colorHex,
            styleDescription: null,
            mockupType: 'regular',
            printSpace: 'front',
            isPersonalized: false, // We already processed personalization above
            personalizationDefaults: null
        );
    }

    /**
     * Build prompt for Case 2: Only custom photo (customer upload)
     * Edit image: currentProductThumbUrl (edit_image parameter)
     * Reference image: customer uploaded image (customer_upload_image parameter)
     * Add instruction to modify design based on reference image
     *
     * @param string|null $basePromptTemplate
     * @param string $userPrompt
     * @param string $shirtColor
     * @param string|null $colorHex
     * @return string
     */
    private function buildPromptForPhotoOnly(?string $basePromptTemplate, string $userPrompt, string $shirtColor, ?string $colorHex): string
    {
        // Add detailed reference image instruction to the user prompt
        $referenceInstruction = "Replace the existing design content with the subject/object from the reference image, but maintain the exact same artistic style, technique, and visual treatment as the original design. For example: if the original design is a pencil-drawn cat and the reference image shows a tiger, create a pencil-drawn tiger using the same drawing style, line work, shading technique, and artistic approach as the original cat design. Keep the same design placement, size proportions, and overall aesthetic while only changing the subject matter to match what's shown in the reference image.";
        $processedUserPrompt = $userPrompt . "\n\n" . $referenceInstruction;

        // Use the trait to build the mockup prompt
        return $this->buildMockupPrompt(
            basePromptTemplate: $basePromptTemplate,
            userPrompt: $processedUserPrompt,
            shirtColor: $shirtColor,
            colorHex: $colorHex,
            styleDescription: null,
            mockupType: 'regular',
            printSpace: 'front',
            isPersonalized: false,
            personalizationDefaults: null
        );
    }

    /**
     * Build prompt for Case 3: Both custom text and custom photo
     * Edit image: currentProductThumbUrl (edit_image parameter)
     * Reference image: customer uploaded image (customer_upload_image parameter)
     * Replace text with personalization values AND add reference image instruction
     *
     * @param string|null $basePromptTemplate
     * @param string $userPrompt
     * @param array|null $personalizationValues
     * @param string $shirtColor
     * @param string|null $colorHex
     * @return string
     */
    private function buildPromptForTextAndPhoto(?string $basePromptTemplate, string $userPrompt, ?array $personalizationValues, string $shirtColor, ?string $colorHex): string
    {
        // First, replace personalization placeholders in the user prompt
        $processedUserPrompt = $userPrompt;
        if (!empty($personalizationValues) && is_array($personalizationValues)) {
            foreach ($personalizationValues as $placeholder => $value) {
                // Replace both [PLACEHOLDER] and [placeholder] formats
                $processedUserPrompt = str_replace("[$placeholder]", $value, $processedUserPrompt);
                $processedUserPrompt = str_replace("[" . strtoupper($placeholder) . "]", $value, $processedUserPrompt);
            }
        }

        // Then, add detailed reference image instruction
        $referenceInstruction = "Replace the existing design content with the subject/object from the reference image, but maintain the exact same artistic style, technique, and visual treatment as the original design. Keep the personalized text content as specified above. For example: if the original design is a pencil-drawn cat with text and the reference image shows a tiger, create a pencil-drawn tiger using the same drawing style, line work, shading technique, and artistic approach as the original cat design, while preserving all the personalized text elements. Keep the same design placement, size proportions, text positioning, and overall aesthetic while only changing the main subject matter to match what's shown in the reference image.";
        $processedUserPrompt = $processedUserPrompt . "\n\n" . $referenceInstruction;

        // Use the trait to build the mockup prompt
        return $this->buildMockupPrompt(
            basePromptTemplate: $basePromptTemplate,
            userPrompt: $processedUserPrompt,
            shirtColor: $shirtColor,
            colorHex: $colorHex,
            styleDescription: null,
            mockupType: 'regular',
            printSpace: 'front',
            isPersonalized: false, // We already processed personalization above
            personalizationDefaults: null
        );
    }
}
