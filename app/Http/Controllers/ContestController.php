<?php

namespace App\Http\Controllers;

use App\Enums\CacheTime;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\UserRoleEnum;
use App\Models\Contest;
use App\Models\OrderProduct;
use App\Models\SystemConfig;
use App\Models\User;
use App\Traits\ApiResponse;
use App\Traits\Contest as ContestTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use function DeepCopy\deep_copy;

class ContestController extends Controller
{
    use ApiResponse, ContestTrait;

    /**
     * @return JsonResponse
     * @throws \Throwable
     */
    public function __invoke(): JsonResponse
    {
        // get latest contest
        $contest = Contest::query()
            ->isMainContest()
            ->latest()
            ->first();

        if ($contest === null) {
            return $this->errorResponse('No active contest found.');
        }

        $contestStartDate = $this->contestStartDate();
        $contestEndDate = $this->contestEndDate();
        $registrationContestDate = $this->getRegistrationContestTime();
        $contest->registration_time = $registrationContestDate;
        if (empty($contestStartDate) || empty($contestEndDate)) {
            return $this->errorResponse("No available contest");
        }
        $settings = json_decode($contest->settings, true);
        $rewards = json_decode($contest->rewards, true);
        $contest['rewards'] = $rewards;
        $contest['parsed'] = [
            'background' => $settings['background'] ?? null,
            'margin' => $settings['margin'] ?? null,
            'icon' => $settings['icon'] ?? null,
            'end_icon' => $settings['end_icon'] ?? null
        ];
        $userId = currentUser()->getUserId() ?? 217;
        $isRef = User::whereId($userId)->value('is_ref');
        $refId = $isRef ? $userId : 0;

        $users = cache()->remember('users_contest_top15_' . $refId, CacheTime::CACHE_10m, function () use ($refId) {
            return $this->getTop15($refId);
        });

        $current_sen_points = OrderProduct::query()
            ->addSelect(DB::raw("sum(sen_points) as current_sen_points"))
            ->where('seller_id', $userId)
            ->where('fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
            ->whereHas('order', function ($q) use ($contestStartDate, $contestEndDate) {
                $q->where('paid_at', '>=', $contestStartDate);
                $q->where('paid_at', '<=', $contestEndDate);
                $q->isValidPaidOrder();
            })
            ->value('current_sen_points');
        if (!empty($users)) {
            $users = collect($users);
            // remove 'id' from $users except current user
            // (we want to highlight current user in the leaderboard)
            $users->map(function ($user) use ($userId) {
                if ($user['id'] !== $userId) {
                    unset($user['id']);
                }
                return $user;
            });
        }

        return $this->successResponse([
            'contest' => $contest,
            'users' => $users,
            'current_sen_points' => $current_sen_points
        ]);
    }

    public function info(): JsonResponse
    {
        $contest = Contest::query()
            ->isActive()
            ->latest()
            ->first();

        if (!$contest) {
            return $this->errorResponse('No active contest found.');
        }

        $contest->rewards = json_decode($contest->rewards, true);
        $contest->settings = json_decode($contest->settings, true);

        $userId = currentUser()->getUserId() ?? 217;
        $templateIds = $contest->settings['template_ids'] ?? []; // product template

        $joinedContest = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where([
                'order.seller_id' => $userId,
                // 'order.payment_status' => OrderPaymentStatus::PAID
            ])
            ->whereIn('order_product.template_id', $templateIds)
            ->where('order_product.updated_at', '>', now()->subDays(30))
            ->count();

        if ($joinedContest === 0) {
            return $this->errorResponse("Seller don't join the contest");
        }

        $currentItems = OrderProduct::query()
            ->join('order', 'order.id', '=', 'order_product.order_id')
            ->where([
                'order.seller_id' => $userId,
                // 'order.payment_status' => OrderPaymentStatus::PAID
            ])
            ->whereIn('order_product.template_id', $templateIds)
            ->whereBetween('order.paid_at', array($contest->start_time->addHours(7), $contest->end_time->addHours(7)))
            ->sum('order_product.quantity');

        $contest->current_items = (int)$currentItems;

        $currentReward = null;
        $nextReward = null;
        $nextGoal = 0;
        foreach ($contest->rewards as $reward) {
            if ($currentItems >= $reward['goal']) {
                $currentReward = $reward['prize'];
            } else {
                $nextReward = $reward['prize'];
                $nextGoal = $reward['goal'];
                break;
            }
        }

        $contest->current_reward = $currentReward;
        $contest->next_reward = $nextReward;
        $contest->next_goal = $nextGoal;

        return $this->successResponse($contest);
    }

    /**
     * @param $refId
     * @return array|mixed[]
     * @throws \Throwable
     */
    public function getTop15($refId = 0)
    {
        // add some data to prevent leak platform sales
        $fusers = [
            [
                'id' => 'u4',
                'nick_name' => 'Trump',
                'pos' => 1,
                'rate' => 1
            ],
            [
                'id' => 'u7',
                'nick_name' => 'Cá Vàng',
                'pos' => 3,
                'rate' => 1
            ],
            [
                'id' => 'u5',
                'nick_name' => 'God Father',
                'pos' => 5,
                'rate' => 1
            ],
            [
                'id' => 'u2',
                'nick_name' => 'Elon Musk',
                'avatar' => 'u/123300/143922c91e743f6f.jpg',
                'pos' => 7,
                'rate' => 1
            ],
            [
                'id' => 'u6',
                'nick_name' => 'G Dragon',
                'pos' => 10,
                'rate' => 1
            ],
            [
                'id' => 'u1',
                'nick_name' => 'Monkey',
                'avatar' => 'u/123299/0152e012cdc639ef.jpg',
                'pos' => 12,
                'rate' => 1
            ],
            [
                'id' => 'u8',
                'nick_name' => 'Henry Bui',
                'pos' => 16,
                'rate' => 1
            ],
            [
                'id' => 'u3',
                'nick_name' => 'Tommy',
                'pos' => 17,
                'rate' => 1
            ],
        ];
        // $nickNames = ['Mr.Tees', 'VitaminC', 'Kevin Bui', 'TopShirt', 'Pet Love', 'FamilyTees', 'Tonny N', 'Joker'];
        // $pos = [1, 2, 3, 7, 9, 11, 14, 15];
        // $rate = [1, 1, 1, 2, 1, 1, 1, 1];
        $top = 15;
        $maxSale24 = 180;
        $minSale24 = 12;
        $cacheKey = 'contest_users';
        $sellerSenPoints = OrderProduct::query()
            ->select('seller_id')
            ->selectRaw("sum(sen_points) as sen_points")
            ->where('fulfill_status', '!=', OrderProductFulfillStatus::CANCELLED)
            ->whereNotNull('seller_id')
            ->when($this->hasActiveContest(), function ($query) use ($refId) {
                $query->whereHas('order', function ($q) use ($refId) {
                    $q->selectRaw(1);
                    $q->where('paid_at', '>=', $this->contestStartDate());
                    $q->where('paid_at', '<=', $this->contestEndDate());
                    $q->when($refId > 0, function ($q) use ($refId) {
                        $q->where('ref_id', $refId);
                    });
                    $q->isValidPaidOrder();
                });
            })
            ->when($this->isActiveContestNeedConfirmToJoin(), function ($query) {
                $sellerIds = User::query()
                    ->select('id')
                    ->where('confirm_join_contest', 1)
                    ->where('role', '!=', UserRoleEnum::CUSTOMER)
                    ->get()
                    ->pluck('id')
                    ->toArray();
                if (count($sellerIds) > 0) {
                    $query->whereIn('seller_id', $sellerIds);
                }
            })
            ->groupBy('seller_id')
            ->orderByDesc('sen_points')
            ->limit(50)
            ->get();

        $sellerIds = $sellerSenPoints->pluck('seller_id')->toArray();
        if (count($sellerIds) === 0) {
            return [];
        }
        $users = User::query()
            ->select([
                'id',
                'name',
                'nickname',
                'avatar',
                'contest_name'
            ])
            ->whereKey($sellerIds)
            ->limit(30)
            ->get();

        if ($users->count() < 20) {
            return [];
        }
        $users = $users->transform(function ($user) use ($sellerSenPoints) {
            $user->sen_points = $sellerSenPoints->firstWhere('seller_id', $user->id)?->sen_points ?? 0;
            return $user;
        });

        if (empty($refId)) {
            $senUsers = [];

            // load from db if no cache
            if (empty($senUsers)) {
                $data = SystemConfig::query()
                    ->select('json_data')
                    ->firstWhere('key', $cacheKey);

                if ($data !== null) {
                    $dataUsers = json_decode($data->json_data, true);
                    $senUsers = [];

                    foreach ($dataUsers as $data) {
                        $user = new User($data);
                        $user->sen_points = $data['sen_points'];
                        $user->timestamp = $data['timestamp'] ?? null;
                        $senUsers[$user->id] = $user;
                    }
                }
            }

            if (empty($senUsers)) {
                $senUsers = [];
            }
            $currentTime = time();

            $updatedSenUsers = [];
            foreach ($fusers as $fu) {
                if (isset($users[$fu['pos'] - 1])) {
                    $existUser = $senUsers[$fu['id']] ?? $senUsers[$fu['nick_name']] ?? null;
                    $user = deep_copy($users[$fu['pos'] - 1]);
                    $user->id = $fu['id'];
                    $user->name = $fu['nick_name'];
                    $user->nickname = $fu['nick_name'];
                    $user->contest_name = $fu['nick_name'];
                    $user->avatar = $fu['avatar'] ?? null;
                    if (now()->lt($this->contestEndDate())) {
                        if ($this->contestStartDate()->lt(now())) {
                            // $user->sen_points *= $fu['rate'];
                            foreach ($fusers as $i2 => $fu2) {
                                if ($fu2['pos'] > 2) {
                                    $user->sen_points += $users[$fu['pos'] - 1 + $fu2['pos'] - 1]->sen_points * $fu['rate'];
                                }
                                if ($i2 >= 3) {
                                    break;
                                }
                            }
                            $user->sen_points = round($user->sen_points + $fu['pos'], 1);
                            if (!empty($existUser)) {
                                if (empty($existUser->timestamp)) {
                                    $existUser->timestamp = $currentTime;
                                }

                                if ($user->sen_points <= $existUser->sen_points) {
                                    $increase = round(($currentTime - $existUser->timestamp) * ($minSale24 / 86400));
                                    $user->sen_points = $existUser->sen_points + $increase;
                                } else {
                                    $userMaxSale24 = cache()->remember('contest_maxsale24_' . $user->id, CacheTime::CACHE_24H, function () use ($maxSale24) {
                                        return random_int($maxSale24, $maxSale24 * 3);
                                    });

                                    $increase = round(($currentTime - $existUser->timestamp) * ($userMaxSale24 / 86400));
                                    $user->sen_points = min($user->sen_points, $existUser->sen_points + $increase);
                                }

                                if ($user->sen_points > $existUser->sen_points) {
                                    $existUser->timestamp = $currentTime;
                                }
                                $user->timestamp = $existUser->timestamp;
                            }
                        } else {
                            // contest not start, reset point
                            $user->sen_points = 0;
                            $user->timestamp = $currentTime;
                        }
                    } else {
                        // end contest, don't change point
                        $user->sen_points = $existUser->sen_points;
                        $user->timestamp = $existUser->timestamp;
                    }

                    $users[] = $user;
                    $updatedSenUsers[$user->id] = $user;
                }
            }
            SystemConfig::query()
                ->where('key', $cacheKey)
                ->update(['json_data' => $updatedSenUsers]);
        }

        return $users->sortByDesc('sen_points')
            ->values()
            ->filter(fn($value, $key) => $key < $top && $value->sen_points > 0)->values()->toArray();
    }
}
