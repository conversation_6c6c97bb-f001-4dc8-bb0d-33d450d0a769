<?php

namespace App\Http\Controllers\Analytic2;

use App\Actions\Admin\Analytic\GetSaleReportDetailAction;
use App\Enums\DateRangeEnum;
use App\Enums\OrderPaymentStatus;
use App\Enums\OrderTypeEnum;
use App\Enums\PgsAndMysqlVersionEnum;
use App\Enums\SellerBillingStatus;
use App\Enums\SellerBillingType;
use App\Enums\UserInfoKeyEnum;
use App\Http\Requests\Analytic\Admin\GetSaleReportsRequest;
use App\Http\Requests\Analytic\Admin\GetSellersStatsRequest;
use App\Models\IndexOrder;
use App\Models\Order;
use App\Models\SellerBilling;
use App\Models\StatsOrder;
use App\Models\SystemConfig;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;

class AdminController extends SellerController
{
    public function __construct()
    {
        parent::__construct();
        $excludeCountries = SystemConfig::getConfig(UserInfoKeyEnum::EXCLUDE_COUNTRIES);
        if (!empty($excludeCountries)) {
            $this->controller->arrExclude[UserInfoKeyEnum::EXCLUDE_COUNTRIES] = explode(',', $excludeCountries);
        }
    }

    protected function addAttributeToOverview(&$overview, $allReportsWithoutTime, $user): void
    {
        parent::addAttributeToOverview($overview, $allReportsWithoutTime, $user);

        $payout = SellerBilling::query()
            ->selectRaw(
                "SUM(CASE WHEN `status` in ('" . SellerBillingStatus::PENDING . "','" . SellerBillingStatus::PROCESSING . "') THEN `amount` END) as payout_pending"
            )
            ->selectRaw(
                "SUM(CASE WHEN `status` = '" . SellerBillingStatus::COMPLETED . "' THEN `amount` END) as seller_profit_paid"
            )
            ->where('type', SellerBillingType::PAYOUT)
            ->first();

        $overview->payout_pending     = $payout->payout_pending ?? 0;
        $overview->seller_profit_paid = $payout->seller_profit_paid ?? 0;
    }

    public function getSellersStats(GetSellersStatsRequest $request): object
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::MYSQL : $dbVersion;

        $this->controller->setCommonFilter($request);
        if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
            $activeIds = StatsOrder::query()
                ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
                ->addExcludeAnalytic($this->controller->arrExclude)
                ->distinct()
                ->whereNotNull('seller_id')
                ->pluck('seller_id')
                ->toArray();
        } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
            $activeIds = IndexOrder::query()
                ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
                ->addExcludeAnalytic($this->controller->arrExclude)
                ->distinct()
                ->whereNotNull('seller_id')
                ->pluck('seller_id')
                ->toArray();
        }

        $options               = $request->except('sort');
        $options['date_range'] = $request->get('seller_date_range');
        $options['start_date'] = $request->get('seller_start_date');
        $options['end_date']   = $request->get('seller_end_date');
        $options['ids']        = $activeIds;

        $sellerIds = User::getSellers(
            $options,
            0,
            false,
        )
            ->pluck('id')
            ->toArray();

        $request->merge(['seller_ids' => $sellerIds]);
        $this->controller->setCommonFilter($request);

        $overview = $this->controller
            ->getCommonQuery(false)
            ->first();

        if ($overview) {
            $overview->active_sellers = count($activeIds);
        }

        return model_map($overview, 'floatval');
    }

    private const ARR_SALE_REPORTS_NEED_RECALCULATED = [
        'cr',
        'upsell',
        'active_sellers',
        'fulfill_sellers',
    ];

    public function getSaleReports(GetSaleReportsRequest $request): JsonResponse
    {
        $dbVersion = $request->get('db_version');
        $dbVersion = !isset($dbVersion) || $dbVersion == '' ? PgsAndMysqlVersionEnum::POSTGRES : $dbVersion;

        try {
            $rangeType  = $request->input('range_type');
            $year       = $request->input('year', date('Y'));
            $customMode = $request->filled('year');
            $data       = [];
            $arr        = [];
            $now        = now();
            $count      = 0;
            $title      = '';

            if ($customMode) {
                $start      = Carbon::createFromDate($year, 1, 1);
                $numberLoop = 11;
            }

            switch ($rangeType) {
                default:
                case 'month':
                    $start      ??= $now->copy()->subYearNoOverflow();
                    $numberLoop ??= 12;
                    $step       = 1;
                    break;
                case 'quarter':
                    $start      ??= $now->copy()->subYearNoOverflow()->firstOfQuarter();
                    $numberLoop ??= $start->diffInMonths($now);
                    $step       = 3;
                    break;
                case 'year':
                    // launched year: 2021
                    $start      ??= Carbon::createFromDate(2021, 1, 1);
                    $numberLoop ??= $start->diffInMonths($now);
                    $step       = 12;
                    break;
            }

            for ($i = 0; $i <= $numberLoop; $i++) {
                $endDate = $start->copy()->addMonthsNoOverflow($i);
                $year    = $endDate->year;
                $month   = $endDate->month;
                $compare = $this->compareTimeToCurrent($year, $month);
                if ($compare > 0) {
                    break;
                }
                // $arr[] = $month . '/' . $year;
                $obj = (new GetSaleReportDetailAction($this->controller, $year, $month, $dbVersion))->handle($compare);
                foreach ($obj as $key => $value) {
                    if (!isset($arr[$key])) {
                        $arr[$key] = 0;
                    }
                    $arr[$key] += $value;
                }

                switch ($rangeType) {
                    case 'month':
                        $title = 'M' . $endDate->format('m-Y');
                        break;
                    case 'quarter':
                        $title = 'Q' . $endDate->quarter . '-' . $endDate->year;
                        $this->setDateRanges($endDate->year, $endDate->quarter);
                        $this->reCalculate($arr, $dbVersion);
                        break;
                    case 'year':
                        $title = $endDate->year;
                        $this->setDateRanges($endDate->year);
                        $this->reCalculate($arr, $dbVersion);
                        break;
                }
                $count++;
                if ($count === $step) {
                    $data[$title] = $arr;
                    $arr          = [];
                    $count        = 0;
                }
            }
            if (!empty($arr)) {
                $data[$title] = $arr;
            }

            return $this->successResponse($data);
        } catch (Throwable $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    private function reCalculate(&$arr, $dbVersion = PgsAndMysqlVersionEnum::POSTGRES): void
    {
        foreach (self::ARR_SALE_REPORTS_NEED_RECALCULATED as $key) {
            switch ($key) {
                case 'cr':
                    if (!empty($arr['orders']) && !empty($arr['visit'])) {
                        $arr[$key] = $arr['orders'] / $arr['visit'];
                    }
                    break;
                case 'upsell':
                    if (!empty($arr['orders']) && !empty($arr['items'])) {
                        $arr[$key] = $arr['items'] / $arr['orders'];
                    }
                    break;
                case 'active_sellers':
                    if ($dbVersion == PgsAndMysqlVersionEnum::POSTGRES) {
                        $arr[$key] = StatsOrder::query()
                            ->countActiveSellers()
                            ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
                            ->addExcludeAnalytic($this->controller->arrExclude)
                            ->value('active_sellers');
                    } else if ($dbVersion == PgsAndMysqlVersionEnum::MYSQL) {
                        $arr[$key] = IndexOrder::query()
                            ->countActiveSellers()
                            ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
                            ->addExcludeAnalytic($this->controller->arrExclude)
                            ->value('active_sellers');
                    }

                    break;
                case 'fulfill_sellers':
                    $arr[$key] = Order::query()
                        ->selectRaw('COUNT(DISTINCT `seller_id`) as fulfill_sellers')
                        ->whereIn('payment_status', [OrderPaymentStatus::PAID, OrderPaymentStatus::PARTIALLY_REFUNDED])
                        ->whereIn('type', [OrderTypeEnum::FULFILLMENT, OrderTypeEnum::FBA])
                        ->addFilterAnalytic($this->controller->arrFilter, $this->controller->dateRanges)
                        ->addExcludeAnalytic($this->controller->arrExclude)
                        ->value('fulfill_sellers');
                    break;
            }
        }
    }

    public static function compareTimeToCurrent($year, $month): int
    {
        $month   = ($month < 10) ? '0' . $month : $month;
        $current = date('Y-m');

        if ($year . '-' . $month < $current) {
            return -1;
        }

        if ($year . '-' . $month > $current) {
            return 1;
        }

        return 0;
    }

    private function setDateRanges($year, $quarter = null): void
    {
        if ($quarter) {
            $startDate = Carbon::createFromDate($year, ($quarter - 1) * 3 + 1, 1);
            $endMethod = 'lastOfQuarter';
        } else {
            $startDate = Carbon::createFromDate($year, 1, 1);
            $endMethod = 'lastOfYear';
        }

        $dateRanges['range'][0] = $startDate->format('Y-m-d');
        $dateRanges['range'][1] = $startDate->copy()->$endMethod()->format('Y-m-d');
        $dateRanges['type']     = DateRangeEnum::CUSTOM;

        $this->controller->dateRanges = $dateRanges;
    }
}
