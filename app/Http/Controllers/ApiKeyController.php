<?php

namespace App\Http\Controllers;

use App\Enums\AccessExternalApiType;
use App\Http\Requests\ApiKey\GenerateRequest;
use App\Models\ApiKey;
use App\Services\SenPrintsApi;
use App\Traits\ApiResponse;
use Throwable;

class Api<PERSON>eyController extends Controller
{
    use ApiResponse;

    public function generate(GenerateRequest $request, $type, $returnTokenOnly = false)
    {
        $currentUser = currentUser();

        if (
            !$returnTokenOnly
            &&
            $type === AccessExternalApiType::SUPPLIER
            &&
            !$currentUser->isAdmin()
        ) {
            return $this->errorResponse('Access denied', 403);
        }

        if ($type === AccessExternalApiType::SELLER) {
            $referenceId = $currentUser->getUserId();
        } else {
            $referenceId = $request->input('referenceId');
        }

        $apiKeyQuery = ApiKey::query();
        $tokenDB     = $apiKeyQuery
            ->where([
                'reference_id' => $referenceId,
                'type'         => $type,
            ])
            ->where(function ($q) {
                $q
                    ->orWhere('expired', '>', now())
                    ->orWhereNull('expired');
            })
            ->value('access_token');
        if ($tokenDB) {
            return $this->responseSuccess($returnTokenOnly, $tokenDB);
        }

        $token = SenPrintsApi::generateKey();
        if (is_null($token)) {
            if ($returnTokenOnly) {
                return '';
            }

            return $this->errorResponse('Cannot create API key.');
        }

        if (in_array($type, AccessExternalApiType::getArrayDecade())) {
            $expired = now()->addDecade();
        } else {
            $expired = now()->addYear();
        }

        $data = [
            'reference_id' => $referenceId,
            'type'         => $type,
            'access_token' => $token,
            'expired'      => $expired
        ];

        if (
            $type === AccessExternalApiType::STORE
            &&
            $currentUser->isSeller()
        ) {
            $data['user_id'] = $currentUser->getUserId();
        }

        try {
            $apiKeyQuery->create($data);

            return $this->responseSuccess($returnTokenOnly, $token);
        } catch (Throwable $e) {
            if ($returnTokenOnly) {
                return '';
            }

            return $this->errorResponse();
        }
    }

    private function responseSuccess($returnTokenOnly, string $token)
    {
        if ($returnTokenOnly) {
            return $token;
        }

        $response['apikey'] = $token;

        return $this->successResponse($response);
    }
}
