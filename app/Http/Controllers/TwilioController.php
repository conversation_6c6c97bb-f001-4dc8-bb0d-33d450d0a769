<?php

namespace App\Http\Controllers;

use App\Enums\AbandonedLogStatusEnum;
use App\Enums\SMSLogStatus;
use App\Enums\TwilioCallbackStatus;
use App\Http\Requests\SmsSettingsSaveRequest;
use App\Models\AbandonedLog;
use App\Models\SmsLog;
use App\Models\SystemConfig;
use App\Models\SystemLocation;
use App\Models\TwillioSms;
use App\Services\SMSService;
use App\Traits\ApiResponse;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Twilio\Rest\Client;

class TwilioController extends Controller
{
    use ApiResponse;

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function twilioCallback(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'MessageStatus' => 'required',
            'MessageSid' => 'required',
            'key' => 'nullable',
            'driver' => 'nullable|in:whatsapp,sms',
        ]);
        $key = $request->input('key');
        $driver = $request->input('driver');
        if ($validator->fails()) {
            logToDiscord("[$driver]: Callback twilio error: \r\n" . $request->getContent(), 'abandoned.error');
            return response()->json([
                'success' => false,
                'message' => 'Invalid request',
                'errors' => $validator->getMessageBag()
            ], 400);
        }
        $accountSid = '**********************************';
        $authToken = '69c5ab77f98e6bb2aca61b0ed82cff50';
        $sid = SystemConfig::getConfig('twilio_account_sid', $accountSid);
        $token = SystemConfig::getConfig('twilio_auth_token', $authToken);
        try {
            $client = new Client($sid, $token);
            $twilioStatus = $request->input('MessageStatus');
            $externalId = $request->input('MessageSid');
            $sms_log = SmsLog::query()->where('external_id', $externalId)->first();
            if (!empty($key)) {
                $sms_log = SmsLog::query()->whereKey($key)->first();
            }
            if (!$sms_log) {
                graylogInfo("[$driver]: Message log not found.", [
                    'category' => 'abandoned_error',
                    'data' => $request->getContent(),
                ]);
                return $this->errorResponse('Message log not found.');
            }
            if (empty($key)) {
                $key = $sms_log->id;
            }
            if (empty($driver)) {
                $driver = $sms_log->send_by;
            }
            $abandonedLog = null;
            if (!empty($sms_log->external_key)) {
                $abandonedLog = AbandonedLog::query()->where('id', $sms_log->external_key)->first();
            }
            $phoneNumber = $sms_log->phone_number;
            if ($sms_log->send_by === 'whatsapp') {
                $phoneNumber = $sms_log->send_by . ':' . $phoneNumber;
            }
            SMSService::createOrUpdatePhoneNumberStatus($phoneNumber, $twilioStatus);
            switch ($twilioStatus) {
                case TwilioCallbackStatus::DELIVERED:
                case TwilioCallbackStatus::RECEIVED:
                    $status = SMSLogStatus::SUCCESS;
                    if ($abandonedLog) {
                        SmsController::minusSmsCredit($abandonedLog->seller_id);
                        $abandonedLog->updateStatus(AbandonedLogStatusEnum::SUCCESS);
                    }
                    break;
                case TwilioCallbackStatus::UNSUBSCRIBED:
                    $status = SMSLogStatus::SENT;
                    if ($abandonedLog) {
                        $abandonedLog->order->customer->unsubscribeSms();
                    }
                    break;
                case TwilioCallbackStatus::READ:
                    $status = SMSLogStatus::READ;
                    break;
                case TwilioCallbackStatus::SENT:
                    $status = SMSLogStatus::SENT;
                    break;
                case TwilioCallbackStatus::FAILED:
                case TwilioCallbackStatus::UNDELIVERED:
                    $status = SMSLogStatus::FAILED;
                    break;
                default:
                    $status = SMSLogStatus::PENDING;
                    if ($abandonedLog) {
                        $abandonedLog->updateStatus();
                    }
            }

            if (empty($sms_log->external_id)) {
                return $this->errorResponse('External id not found.');
            }

            $message = $client->messages($sms_log->external_id)->fetch();

            if ($driver === 'whatsapp' && $status === SMSLogStatus::FAILED) {
                $sms_log->updateData(SMSLogStatus::FAILED, $driver, $message->errorMessage ?? null);
                $sms_log_check = SmsLog::query()->where([
                    'send_by' => 'sms',
                    'phone_number' => $sms_log->phone_number,
                    'external_key' => $sms_log->external_key
                ])->first();
                if (!$sms_log_check) {
                    return $this->successResponse([
                        'key' => $key,
                        'message' => 'Sms message already re-sent',
                        'status' => $status
                    ], 'Sms message already re-sent.');
                }
                $smsId = Str::uuid();
                $sms = new SMSService($smsId, $sms_log->message, $sms_log->phone_number,'sms', null, $sms_log->external_key, $sms_log->order_id, $sms_log->seller_id);
                [$status, $twilioMsg] = $sms->sendNow();
                if ($status === TwilioCallbackStatus::FAILED) {
                    graylogInfo("Can not re-send message.", [
                        'category' => 'abandoned_error',
                        'status' => $status,
                        'text' => $twilioMsg,
                        'data' => json_encode($sms_log->toArray()),
                        'action' => 'sms'
                    ]);
                    return $this->errorResponse($twilioMsg);
                }
                if ($abandonedLog && $status === TwilioCallbackStatus::UNSUBSCRIBED) {
                    $abandonedLog->order->customer->unsubscribeSms();
                }
                graylogInfo("Message re-sent.", [
                    'category' => 'abandoned_success',
                    'status' => $status,
                    'data' => json_encode($twilioMsg),
                    'action' => 'sms'
                ]);
                return $this->successResponse([
                    'key' => $key,
                    'message' => $twilioMsg,
                    'status' => $status
                ], 'Sms message re-sent.');
            }

            if ($abandonedLog && $status === SMSLogStatus::FAILED) {
                $abandonedLog->updateStatus(AbandonedLogStatusEnum::CANCELLED);
            }

            $sms_log->updateData($status, $driver, $message->errorMessage ?? null);
            $driver = Str::ucfirst($driver);
            graylogInfo("[$driver]: The message log status updated.", [
                'category' => 'abandoned_success',
                'data' => json_encode($message->toArray()),
            ]);
            return $this->successResponse([
                'key' => $key,
                'message' => $message,
                'status' => $status
            ], "[$driver]: The message log status updated");
        } catch (Exception $e) {
            logException($e, __FUNCTION__, 'abandoned.error');
            return $this->errorResponse($e->getMessage());
        }
    }

    public function createSmsSettings(SmsSettingsSaveRequest $request)
    {
        try {
            $data = $request->validated();
            $data = $this->processedSmsSettings($data);
            TwillioSms::query()->create($data);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse();
    }

    public function updateSmsSettings(SmsSettingsSaveRequest $request, $id)
    {
        try {
            $data = $request->validated();
            $data = $this->processedSmsSettings($data, $id);
            TwillioSms::query()
                ->where('id', $id)
                ->update($data);
        } catch (Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
        return $this->successResponse();
    }

    public function deleteSmsSettings($id)
    {
        $sms = TwillioSms::query()->where('id', $id)->delete();
        if (!$sms) {
            return $this->errorResponse('Sms settings not found.');
        }
        return $this->successResponse();
    }

    public function listSmsSettings(Request $request)
    {
        $perPage = $request->input('per_page', 15);
        $sms = TwillioSms::query()
            ->orderBy('updated_at', 'desc')
            ->paginate($perPage);
        return $this->successResponse($sms);
    }

    /**
     * @throws Exception
     */
    private function processedSmsSettings($data, $id = null)
    {
        $from = $data['phone'];
        if(!Str::startsWith($from, '+')) {
            $from = '+' . $from;
        }
        $pattern = "/^(\+?[0-9]{1,4}[\s-]?)?(\(?\d{1,4}\)?[\s-]?)?[\d\s-]{5,15}$/";
        if (!preg_match($pattern, $from)) {
            throw new Exception('Invalid phone number.');
        }
        $exists = TwillioSms::query()
            ->where('phone', $from)
            ->when($id, fn($query) => $query->where('id', '!=', $id))
            ->exists();
        if ($exists) {
            throw new Exception('Duplicate phone number.');
        }
        $regions = $data['region'] ?? null;
        $code = [];
        if (!empty($regions)) {
            if (in_array('*', $regions)) {
                $code = null; // Default for all regions
            } else {
                $locationRecords = SystemLocation::query()
                    ->whereIn('code', $regions)
                    ->get();
                foreach ($locationRecords as $record) {
                    if ($record->type === 'country') {
                        $code[] = $record->phone_code;
                    } else {
                        $code[] = $this->getPhoneCodeFromRegion($record);
                    }
                }
                $code = collect($code)->flatten()->unique()->values()->toArray();
            }
        }
        $data['phone'] = $from;
        $data['code'] = $code ? json_encode($code) : null;
        $data['region'] = json_encode($regions);
        return $data;
    }

    private function getPhoneCodeFromRegion(SystemLocation $record): array
    {
        $code = [];
        $column = match ($record->type) {
            'region' => 'region_code',
            'intermediate_region' => 'sub_region_code',
            'sub_region' => 'intermediate_region_code',
            default => null,
        };

        if ($column) {
            $countries = SystemLocation::query()
                ->where($column, $record->code)
                ->where('type', 'country')
                ->get();
            foreach ($countries as $country) {
                if ($country->phone_code === 0 || !empty($country->phone_code)) {
                    $code[] = $country->phone_code;
                }
            }
            $code = array_unique($code);
            $code = array_values($code);
        }

        return $code;
    }
}
