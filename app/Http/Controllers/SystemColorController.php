<?php

namespace App\Http\Controllers;

use App\Models\IndexOrderProduct;
use App\Models\SystemColor;
use App\Rules\Admin\Product\ColorNotUsedRule;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SystemColorController extends Controller
{
    public function index()
    {
        // todo: cache query
        return SystemColor::all();
    }

    /**
     * @param Request $request
     * @return SystemColor[]|Builder[]|Collection|JsonResponse
     */
    public function search(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255|nullable',
            'hex_code' => 'string|max:255|nullable',
            'is_heather' => 'boolean|nullable',
            'multi_color_mode' => 'boolean|nullable',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid search parameters',
                'errors' => $validator->errors(),
            ], 400);
        }
        $query = SystemColor::query();
        if ($request->has('name') && $request->name !== null) {
            $query->where('name', 'like', '%' . $request->name . '%');
        }
        if ($request->has('hex_code') && $request->hex_code !== null) {
            $query->where('hex_code', 'like', '%' . $request->hex_code . '%');
        }
        if ($request->has('is_heather') && $request->is_heather !== null) {
            $query->where('is_heather', $request->is_heather);
        }
        if ($request->has('multi_color_mode') && $request->multi_color_mode !== null) {
            $query->where('multi_color_mode', $request->multi_color_mode);
        }
        return response()->json([
            'success' => true,
            'data' => $query->get(),
        ]);
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $color = SystemColor::whereId($id)->first();
        if ($color) {
            return response()->json([
                'success' => true,
                'data' => $color,
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => 'Color not found',
        ], 404);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
//            'name' => 'required|string|max:255|unique:system_color',
            'name' => ['required', 'string', 'max:255', new ColorNotUsedRule(), 'unique:system_color'],
            'hex_code' => 'required|string|max:50|unique:system_color,hex_code',
            'is_heather' => 'boolean|nullable',
            'multi_color_mode' => 'boolean|nullable',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid color data',
                'errors' => $validator->errors(),
            ], 400);
        }
        $color = new SystemColor();
        $color->name = strtolower($request->name);
        $color->hex_code = $request->hex_code;
        $color->is_heather = $request->is_heather ?? false;
        // multi_color_mode will be auto-set by model boot method
        if ($color->save()) {
            return response()->json([
                'success' => true,
                'data' => $color,
            ], 201);
        }
        return response()->json([
            'success' => false,
            'message' => 'Error creating color',
        ], 500);
    }

    /**
     * @param int $id
     * @return JsonResponse
     */
    public function destroy(int $id): JsonResponse
    {
        $color = SystemColor::whereId($id)->first();
        if ($color) {
            $colorName = strtolower($color->name);
            if (IndexOrderProduct::whereColor($colorName)->exists()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Color is used in product',
                ], 400);
            }
            $color->delete();
            return response()->json([
                'success' => true,
                'message' => 'Color deleted',
            ], 200);
        }
        return response()->json([
            'success' => false,
            'message' => 'Color not found',
        ], 404);
    }

    /**
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
//            'name' => 'required|string|max:255|IsColorNameExists',
            'name' => ['required', 'string', 'max:255', new ColorNotUsedRule()],
            'hex_code' => 'required|string|max:50',
            'is_heather' => 'boolean|nullable',
            'multi_color_mode' => 'boolean|nullable',
        ]);
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid color data',
                'errors' => $validator->errors(),
            ], 400);
        }
        $color = SystemColor::whereId($id)->first();
        if ($color) {
            $color->name = strtolower($request->name);
            $color->hex_code = $request->hex_code;
            $color->is_heather = $request->is_heather ?? false;
            // multi_color_mode will be auto-set by model boot method
            if ($color->isDirty('name')) {
                $validator = Validator::make($request->all(), [
                    'name' => 'unique:system_color',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid color data',
                        'errors' => $validator->errors(),
                    ], 400);
                }
            }
            if ($color->isDirty('hex_code')) {
                $validator = Validator::make($request->all(), [
                    'hex_code' => 'unique:system_color,hex_code',
                ]);
                if ($validator->fails()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid color data',
                        'errors' => $validator->errors(),
                    ], 400);
                }
            }
            if ($color->save()) {
                return response()->json([
                    'success' => true,
                    'data' => $color,
                ]);
            }
            return response()->json([
                'success' => false,
                'message' => 'Error updating color',
            ], 500);
        }
        return response()->json([
            'success' => false,
            'message' => 'Color not found',
        ], 404);
    }
}
