<?php

namespace App\Http\Requests;

use App\Enums\OrderFraudStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class BulkUpdateFraudStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fraud_status' => [
                'required',
                Rule::in(OrderFraudStatus::getValues())
            ],
            'order_ids'=>[
                'required','array'
            ],
        ];
    }
}
