<?php

namespace App\Http\Requests\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdateFulfillmentTrackingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fulfillment_id' => 'required|integer|exists:fulfillment,id',
            'order_id' => 'required|integer|exists:order,id',
            'supplier_id' => 'nullable|integer',
            'supplier_name' => 'nullable|string',
            'shipping_carrier' => 'nullable|string',
            'tracking_number' => 'nullable|string',
            'tracking_url' => 'nullable|string',
        ];
    }
}
