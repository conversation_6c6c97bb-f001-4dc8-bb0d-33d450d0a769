<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SellerUpdatePaymentGatewayRequest extends FormRequest
{
    public function authorize(): bool
    {
        return Auth::check();
    }

    public function rules(): array
    {
        $rule                       = [];
        $rule['payment_gateway_id'] = [
            'required',
            'integer',
        ];

        return array_merge($rule, (new SellerCreatePaymentGatewaysRequest())->rules());
    }
}
