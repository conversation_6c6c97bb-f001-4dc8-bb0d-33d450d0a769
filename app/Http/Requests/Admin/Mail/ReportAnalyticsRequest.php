<?php

namespace App\Http\Requests\Admin\Mail;

use App\Enums\SendMail\ReportType;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class ReportAnalyticsRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;
    protected $hideLog = true;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return currentUser()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'seller_id' => 'nullable|integer',
            'store_id' => 'nullable|integer',
            'start_date' => 'nullable|integer',
            'end_date' => 'nullable|integer',
        ];
    }
}
