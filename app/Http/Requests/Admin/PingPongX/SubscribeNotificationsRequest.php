<?php

namespace App\Http\Requests\Admin\PingPongX;

use App\Enums\PingPongX\PingPongXHookEventNameEnum;
use App\Enums\SystemRole;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SubscribeNotificationsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $currentUser = currentUser();
        return $currentUser->hasRole(SystemRole::ADMIN);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'hookUrl' => 'required|url',
            'hookEventName' => ['required', 'string', Rule::in(PingPongXHookEventNameEnum::getValues())],
        ];
    }
}
