<?php

namespace App\Http\Requests\Admin\Order;

use App\Enums\OrderDisputeStatusEnum;
use App\Enums\OrderDisputeTrackingStatusEnum;
use App\Enums\OrderDisputeTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreateOrderDisputeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return currentUser()->isAdmin();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'dispute_info' => ['required'],

            'dispute_info.order_id' => ['required', 'numeric'],
            'dispute_info.staff_id' => ['required', 'numeric'],
            'dispute_info.dispute_id' => 'required|string',
            'dispute_info.dispute_tracking_status' => [
                'nullable',
                'string',
                'in:' . implode(',', OrderDisputeTrackingStatusEnum::getValues())
            ],
            'dispute_info.dispute_status' => [
                'nullable',
                'string',
                'in:' . implode(',', OrderDisputeStatusEnum::getValues())
            ],
            'dispute_info.dispute_type' => [
                'nullable',
                'string',
                'in:' . implode(',', OrderDisputeTypeEnum::getValues())
            ],
            'dispute_info.dispute_opened_reason' => ['nullable', 'string'],
            'dispute_info.dispute_solution' => ['nullable', 'string'],
            'dispute_info.klarna' => 'nullable',
            'dispute_info.dispute_due_date' => 'nullable|string',
            'dispute_info.dispute_created_at' => 'nullable|string',
            'dispute_info.create_actions' => 'nullable|array',
            'dispute_info.payment_gate' => 'nullable|string',
            'dispute_info.is_reopened' => 'nullable',
            'dispute_info.inquiry' => 'nullable',
        ];
    }

    public function failedValidation($validator)
    {
        $response = response()->json([
            'message' => $validator->errors()->toArray(),
            'success' => false,
        ], 400);
        throw new HttpResponseException($response);
    }
}
