<?php

namespace App\Http\Requests\Admin\Order;

use App\Enums\OrderDisputeActionTypeEnum;
use App\Services\OrderService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class UpdateOrderDisputeActionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    public function validationData()
    {
        return array_merge($this->all(), [
            'disputeId' => $this->route('disputeId'),
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'type' => [
                'required',
                'string',
                'in:'.implode(',' , OrderDisputeActionTypeEnum::getValues()),
            ]
        ];
    }


    public function withValidator ($validator) {
        $validator->after(function ($validator) {
            $actionType = $this->type;
            $disputeId = $this->disputeId;
            $response = OrderService::validateDisputeOrderAction($disputeId, $actionType);
            if (!$response['accept']) {
                $validator->errors()->add('Update_order_dispute_action_error', $response['message']);
            }
        });
    }

    public function failedValidation($validator)
    {
        $response = response()->json([
            'message' => $validator->errors()->toArray()['Update_order_dispute_action_error'][0],
            'success' => false,
        ], 400);
        throw new HttpResponseException($response);
    }
}
