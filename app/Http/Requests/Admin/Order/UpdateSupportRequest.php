<?php

namespace App\Http\Requests\Admin\Order;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderSupportStatusEnum;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateSupportRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [
            'order_id'       => [
                'required',
                'string',
            ],
            'support_status' => [
                'required',
                Rule::in(OrderSupportStatusEnum::getValues()),
            ],
            'assignee'       => [
                'nullable',
                'integer',
            ],
            'status'         => [
                'nullable',
                Rule::in([
                    OrderFulfillStatus::ON_HOLD,
                    OrderFulfillStatus::PROCESSING,
                ]),
            ],
        ];

        $columns = [
            'admin_note',
            'order_note',
        ];
        if (request('status') === OrderFulfillStatus::ON_HOLD) {
            $columns       = [
                'admin_note',
            ];
            $rules['note'] = [
                'required',
                'string',
            ];
        } else {
            $rules['note'] = [
                'nullable',
                'string',
            ];
        }
        $rules['column'] = [
            'required',
            Rule::in($columns),
        ];

        return $rules;
    }
}
