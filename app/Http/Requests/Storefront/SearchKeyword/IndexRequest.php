<?php

namespace App\Http\Requests\Storefront\SearchKeyword;

use App\Enums\DateRangeEnum;
use App\Http\Requests\DateRangeRequest;
use App\Rules\Analytic\DateRangeRule;
use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        $rules                = (new DateRangeRequest())->rules();
        $rules['date_type']   = [
            'string',
            'in:' . implode(',', DateRangeEnum::asArray()),
        ];
        $rules['end_date'][]  = new DateRangeRule($this->start_date);
        $rules['store_id'][]  = [
            'numeric',
            'fulfilled',
        ];
        $rules['seller_id'][] = [
            'numeric',
            'fulfilled',
        ];

        return $rules;
    }
}
