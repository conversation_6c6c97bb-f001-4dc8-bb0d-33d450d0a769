<?php

namespace App\Http\Requests\Analytic\Seller;

use App\Http\Requests\DateRangeRequest;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class DashboardRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules               = (new DateRangeRequest())->rules();

        $rules['store_id']   = (new StoreRequest())->rules();

        return $rules;
    }
}
