<?php

namespace App\Http\Requests\Analytic\Seller;

use App\Rules\Analytic\AdOptionRule;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class GetAdsRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules             = (new DashboardRequest())->rules();
        $rules['store_id'] = (new StoreRequest())->rules();
        $rules['ad_option'] = [
            'string',
            new AdOptionRule(),
        ];

        return $rules;
    }
}
