<?php

namespace App\Http\Requests\Analytic\Admin;

use App\Enums\UserRoleEnum;
use App\Enums\UserStatusEnum;
use App\Http\Controllers\SystemConfigController;
use App\Http\Requests\DateRangeRequest;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetSellersStatsRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = (new DateRangeRequest())->rules();
        $rules = array_merge($rules, (new DateRangeRequest('seller_'))->rules());

        $rules['q']                = [
            'string',
            'nullable',
        ];
        $rules['status']           = [
            Rule::in(UserStatusEnum::getValues()),
        ];
        $rules['support_staff_id'] = [
            Rule::in(SystemConfigController::supporters()->pluck('id')->toArray()),
        ];
        $rules['role']             = [
            Rule::in(UserRoleEnum::getValues()),
        ];

        return $rules;
    }
}
