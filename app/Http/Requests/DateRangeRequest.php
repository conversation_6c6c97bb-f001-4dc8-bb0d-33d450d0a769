<?php

namespace App\Http\Requests;

use App\Enums\DateRangeEnum;
use App\Rules\Analytic\DateRangeRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DateRangeRequest extends FormRequest
{
    private string $prefix = '';

    public function __construct($prefix = null)
    {
        $this->prefix = $prefix ? : '';
    }

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $rules = [];

        $rules[$this->prefix . 'date_range'] = $rules[$this->prefix . 'date_type'] = [
            'string',
            Rule::in(DateRangeEnum::getValues()),
        ];

        if (
            (
                request()->has($this->prefix . 'date_range')
                &&
                request($this->prefix . 'date_range') === DateRangeEnum::CUSTOM
            )
            ||
            (
                request()->has($this->prefix . 'date_type')
                &&
                request($this->prefix . 'date_type') === DateRangeEnum::CUSTOM
            )
        ) {
            $rules = [
                $this->prefix . 'start_date' => [
                    'required_with:' . $this->prefix . 'end_date',
                    'date',
                    'before_or_equal:' . $this->prefix . 'end_date',
                ],
                $this->prefix . 'end_date'   => [
                    'required_with:' . $this->prefix . 'start_date',
                    'date',
                    'after_or_equal:' . $this->prefix . 'start_date',
                ],
            ];

            $key = $this->prefix . 'start_date';

            $rules[$this->prefix . 'end_date'][] = new DateRangeRule(request($key));
        }

        return $rules;
    }
}
