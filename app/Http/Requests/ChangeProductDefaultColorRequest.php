<?php

namespace App\Http\Requests;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class ChangeProductDefaultColorRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'campaign_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
            'color' => 'required|string|exists:App\Models\SystemColor,name'
        ];
    }
}
