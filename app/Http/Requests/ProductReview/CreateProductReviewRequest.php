<?php

namespace App\Http\Requests\ProductReview;

use App\Enums\ProductReviewFileTypeEnum;
use App\Models\OrderProduct;
use App\Models\ProductReview;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateProductReviewRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    protected $stopOnFirstFailure = true;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $allowedVideoExtensions = ['mp4', 'mov', 'wmv', 'avi', 'webm', 'mkv'];

        return [
            'order_product_id' => [
                'required',
                Rule::exists(OrderProduct::class, 'id'),
                Rule::unique(ProductReview::class, 'order_product_id')
            ],
            'print_quality_rating' => 'required|integer|min:1|max:5',
            'product_quality_rating' => 'required|integer|min:1|max:5',
            'customer_support_rating' => 'required|integer|min:1|max:5',
            'comment' => 'nullable|string|max:300',
            'assets' => 'array|max:5',
            'assets.*.type' => [
                'required',
                Rule::in(ProductReviewFileTypeEnum::getValues())
            ],
            'assets.*.thumb'  => [
                'required_if:assets.*.type,video',
                function($attribute, $value, $fail) use ($allowedImageExtensions) {
                    $extension = strtolower(pathinfo($value, PATHINFO_EXTENSION));
                    if (!in_array($extension, $allowedImageExtensions)) {
                        $fail('Unsupported file format ' . $extension);
                    }
                }
            ],
            'assets.*.url' => [
                'required',
                function($attribute, $value, $fail) use ($allowedImageExtensions, $allowedVideoExtensions) {
                    $type = $this->input(str_replace('.url', '', $attribute))['type'];
                    $extension = strtolower(pathinfo($value, PATHINFO_EXTENSION));

                    if ($type == ProductReviewFileTypeEnum::VIDEO) {
                        if (!in_array($extension, $allowedVideoExtensions)) {
                            $fail('Unsupported file format ' . $extension);
                        }
                    } else {
                        if (!in_array($extension, $allowedImageExtensions)) {
                            $fail('Unsupported file format ' . $extension);
                        }
                    }
                }
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'order_product_id.unique' => 'You have already reviewed this product.',
        ];
    }
}
