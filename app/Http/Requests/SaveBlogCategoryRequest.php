<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SaveBlogCategoryRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $categoryId = $this->input('id');
        $currentUser = currentUser();

        return [
            'id' => ['nullable', 'integer', 'exists:blog_categories,id'],
            'name' => [
                'required',
                'string',
                'max:255',
            ],
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('blog_categories', 'slug')
                    ->where('seller_id', $currentUser->getUserId())
                    ->where('store_id', $this->store_id)
                    ->ignore($categoryId)
                ],
            'store_id' => ['required', 'integer', 'exists:store,id'],
        ];
    }
}
