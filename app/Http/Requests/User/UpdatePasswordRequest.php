<?php

namespace App\Http\Requests\User;

use App\Rules\IsCurrentPassword;
use App\Rules\IsNotCurrentPassword;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class UpdatePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => ['bail', 'required', new IsCurrentPassword()],
            'new_password' => ['required', 'min:8', new IsNotCurrentPassword()],
            'confirm_new_password' => ['required', 'min:8', 'same:new_password']
        ];
    }
}
