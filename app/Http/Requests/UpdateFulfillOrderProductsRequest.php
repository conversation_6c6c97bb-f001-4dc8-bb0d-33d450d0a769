<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateFulfillOrderProductsRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'order_id' => ['required', 'integer', Rule::exists('order', 'id')],
            'products' => ['required', 'array'],
            'products.*.options' => ['required', 'json'],
            'products.*.id' => ['required', 'integer', Rule::exists('order_product', 'id')],
            'products.*.images' => ['required', 'array'],
            'products.*.images.*.file_id' => ['required', 'integer', Rule::exists('file', 'id')],
            'products.*.images.*.file_url' => ['required', 'string'],
        ];
    }
}
