<?php

namespace App\Http\Requests;

use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;

class SaveSellerCustomDesignRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        $seller = currentUser()->getInfoAccess();
        $connection = $seller->getPrivateConnection();

        return [
            'campaign_id' => ['integer', 'required', new CheckExistsIdRule('product', connection: $connection)],
            'designs' => ['array', 'required'],
            'designs.*.name' => ['string', 'required'],
            'design.*.path' => ['string', 'required']
        ];
    }
}
