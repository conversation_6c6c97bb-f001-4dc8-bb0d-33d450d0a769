<?php

namespace App\Http\Requests\Store;

use Illuminate\Foundation\Http\FormRequest;

class UpdateWooCommerceWebhookRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'webhook_url' => 'nullable|url|max:255'
        ];
    }

    public function messages(): array
    {
        return [
            'webhook_url.url' => 'The webhook URL must be a valid URL',
            'webhook_url.max' => 'The webhook URL must not exceed 255 characters'
        ];
    }
}
