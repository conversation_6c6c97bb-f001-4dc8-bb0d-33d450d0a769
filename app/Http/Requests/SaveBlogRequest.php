<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SaveBlogRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'id' => ['nullable', 'integer', 'exists:blogs,id'],
            'store_id' => ['required', 'integer', 'exists:store,id'],
            'slug' => ['nullable', 'string', 'max:255', Rule::unique('blogs', 'slug')->where('store_id', $this->store_id)->ignore($this->id)],
            'title' => ['required', 'string', 'max:255'],
            'sub_description' => ['nullable', 'string', 'max:1000'],
            'html' => ['nullable', 'string'],
            'main_image' => ['nullable', 'string', 'max:255'],
            'status' => ['nullable', 'boolean'],
            'category_id' => ['nullable', 'integer', 'exists:blog_categories,id'],
            'collections' => ['nullable', 'array'],
        ];

        return $rules;
    }
}
