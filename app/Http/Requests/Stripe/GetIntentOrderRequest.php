<?php

namespace App\Http\Requests\Stripe;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class GetIntentOrderRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    protected $stopOnFirstFailure = true;
    protected $hideError = true;

    public function rules(): array
    {
        return [
            'order_token'       => [
                'required',
                'string',
            ],
            'gateway_id'        => [
                'required',
                'integer',
            ],
            'currency_code'     => [
                'string',
                'in:USD,EUR',
            ],
        ];
    }
}
