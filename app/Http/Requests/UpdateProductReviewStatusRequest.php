<?php

namespace App\Http\Requests;

use App\Enums\ProductReviewEnum;
use App\Models\ProductReview;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProductReviewStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => [
                'required',
                Rule::in(ProductReviewEnum::getValues())
            ],
            'review_ids' => 'required|array',
            'review_ids.*' => [
                Rule::exists(ProductReview::class, 'id')
            ],
        ];
    }
}
