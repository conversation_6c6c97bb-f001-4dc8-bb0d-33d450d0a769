<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class NotifySellerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'image' => ['nullable'],
            'image_link' => ['nullable'],
            'seller_id' => ['nullable'],
            'subject' => ['required', 'min:3'],
            'message' => ['required_without:image', 'min:3'],
            'expiry_date' => ['required', 'date'],
            'is_warning' => ['required', 'boolean'],
            'type'  => ['nullable', 'in:1,2']
        ];
    }
}
