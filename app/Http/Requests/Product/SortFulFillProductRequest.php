<?php

namespace App\Http\Requests\Product;

use App\Models\Template;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SortFulFillProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'items' => [
                'array',
            ],
            'items.*.id' => [
                'required',
                'integer',
            ],
            'items.*.product_id' => [
                'required',
                'integer',
            ],
            'items.*.fulfill_product_id' => [
                'required',
                'integer',
            ],
            'items.*.supplier_id' => [
                'required',
                'integer',
            ],
            'items.*.assign_rate' => [
                'required',
                'integer',
            ],
            'items.*.max_items' => [
                'required',
                'integer',
                'min:0',
                'max:' . 1_000_000,
            ],
            'template_id' => [
                'required',
                'integer',
                Rule::exists(Template::class, 'id'),
            ],
        ];
    }

    public function attributes()
    {
        return [
            'items' => 'Items',
            'items.*.id' => 'Id',
            'items.*.product_id' => 'Product',
            'items.*.fulfill_product_id' => 'Fulfill Product',
            'items.*.supplier_id' => 'Supplier',
            'items.*.assign_rate' => 'Assign Rate',
            'items.*.max_items' => 'Max Items',
        ];
    }
}
