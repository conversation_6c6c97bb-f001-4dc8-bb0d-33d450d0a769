<?php

namespace App\Http\Requests\Product;

use App\Enums\TypeMockup3DFiles;
use App\Rules\CheckExistsIdRule;
use App\Traits\CanUseSingleStoreConnection;
use Illuminate\Foundation\Http\FormRequest;

class DeleteProductMockupRequest extends FormRequest
{
    use CanUseSingleStoreConnection;
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = currentUser();

        return $user->isAdmin() && $user->can('update_product');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'product_id' => [
                'required',
                'integer',
                new CheckExistsIdRule('product'),
            ],
            'file_id' => 'required|exists:file,id',
            'type' => 'in:' . implode(',', TypeMockup3DFiles::asArray()),
        ];
    }
}
