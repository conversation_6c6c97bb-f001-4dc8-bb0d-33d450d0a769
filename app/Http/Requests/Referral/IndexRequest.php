<?php

namespace App\Http\Requests\Referral;

use App\Enums\DateRangeEnum;
use App\Rules\Analytic\DateRangeRule;
use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IndexRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    public function authorize(): bool
    {
        return auth()->check();
    }

    public function rules(): array
    {
        return [
            'ref_id' => [
                'exists:user,id',
            ],
            'q' => [
                'string',
                'nullable'
            ],
            'date_range' => [
                Rule::in(array_merge(DateRangeEnum::asArray(), ['by_month'])),
            ],
            'start_date' => [
                'required_if:date_range,custom',
                'date',
                'before_or_equal:end_date',
            ],
            'end_date' => [
                'required_if:date_range,custom',
                'date',
                'after_or_equal:start_date',
                new DateRangeRule($this->start_date, 365),
            ],
            'sort_by' => [
                'string',
                'in:created_at,sold_items,fulfill_items,sales',
            ],
        ];
    }
}
