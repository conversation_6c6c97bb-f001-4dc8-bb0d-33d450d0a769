<?php

namespace App\Http\Requests;

use App\Enums\FileRenderType;
use App\Enums\FileStatusEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveMockupDesignRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return Auth::check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'file_id' => 'nullable|integer',
            'file_url' => 'nullable|string',
            'product_id' => 'nullable|integer',
            'design_json' => 'string|nullable',
            'print_space' => 'string|nullable',
            'option' => 'string|nullable',
            'type_detail' => 'string|nullable',
            'render_type' => [Rule::in(FileRenderType::asArray()), 'required'],
            'status' => [Rule::in(FileStatusEnum::asArray()), 'required'],
        ];
    }
}
