<?php

namespace App\Http\Requests\Seller\Promotion;

use App\Traits\PreventsRedirectWhenFailedTrait;
use Illuminate\Foundation\Http\FormRequest;

class IndexRequest extends FormRequest
{
    use PreventsRedirectWhenFailedTrait;

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'public_status' => [
                'nullable',
                'boolean',
            ],
            'store_id' => [
                'nullable',
                'integer',
            ],
            'sort' => [
                'nullable',
                'string',
                'in:used_count',
            ],
            'direction' => [
                'nullable',
                'in:asc,desc',
            ],
        ];
    }

    public function attributes()
    {
        return [
            'public_status' => 'Pubic Status',
            'store_id' => 'Store',
        ];
    }
}
