<?php
namespace App\Console\Commands;
use App\Enums\OrderCancelRequestStatus;
use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Models\OrderCancelRequest;
use App\Models\OrderHistory;
use App\Services\StoreService;
use Illuminate\Console\Command;

class SendEmailConfirmCancelOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:send-email-cancel-order';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send an email confirm cancel order to customer after 10 minutes';

    /**
     * Execute the console command.
     *
     * @return boolean
     */
    public function handle()
    {
        try {
            $this->handle_auto_resume_order();
            $this->handle_send_email_confirm();
        } catch (\Throwable $e) {
            logToDiscord('Send email confirm request cancel order to customer failed. Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * @return boolean
     */
    private function handle_send_email_confirm() {
        $pending_requests = OrderCancelRequest::query()
            ->with('order')
            ->where('created_at', '<=', now()->subMinutes(10))
            ->where([
                'sent_email' => 0,
                'status' => OrderCancelRequestStatus::PENDING
            ])->get();
        if($pending_requests->isEmpty()) {
            return false;
        }
        $processed[] = [];
        foreach ($pending_requests as $pending_request) {
            $order = $pending_request->order;
            if (in_array($order->id, $processed, true)) {
                continue;
            }
            $storeInfo = StoreService::getStoreInfo($order->store_id);
            $dataSendMailLog = [
                'sellerId' => $order->seller_id,
                'storeId' => $order->store_id,
                'orderId' => $order->id,
            ];
            $config = [
                'to' => $order->customer_email,
                'template' => 'buyer.request_cancel_order_confirmation',
                'data' => [
                    'subject' => 'Confirm Your Request Cancel Order #' . $order->order_number,
                    'name' => $order->customer_name,
                    'email' => $order->customer_email,
                    'store_info' => $storeInfo,
                    'order' => $order,
                    'token' => $pending_request->id
                ],
                'sendMailLog' => $dataSendMailLog
            ];
            $status = sendEmail($config);
            $processed[] = $order->id;
            if ($status) {
                $pending_request->sent_email = 1;
                $pending_request->updated_at = now();
                $pending_request->expired_at = now()->clone()->addHours(12)->toDateTimeString();
                if(!$pending_request->save()) {
                    logToDiscord('Sent email to customer, but can not update status of send email into database, Order Id: #' . $order->id);
                }
                continue;
            }
            graylogInfo('Send email confirm request cancel order to customer failed.', [
                'category' => 'send_email_confirm_cancel_order',
                'order_id' => $order->id,
                'customer_email' => $order->customer_email,
                'customer_name' => $order->customer_name,
                'store_info' => $storeInfo,
                'token' => $pending_request->id
            ]);
        }
        return true;
    }

    /**
     * @return boolean
     */
    private function handle_auto_resume_order() {
        $pending_requests = OrderCancelRequest::query()
            ->with('order')
            ->where([
                'sent_email' => 1,
                'status' => OrderCancelRequestStatus::PENDING
            ])
            ->whereNotNull('expired_at')
            ->whereDate('expired_at', '<', now())
            ->get();
        if($pending_requests->isEmpty()) {
            return false;
        }
        $processed[] = [];
        foreach ($pending_requests as $pending_request) {
            $order = $pending_request->order;
            if (in_array($order->id, $processed, true)) {
                continue;
            }
            $processed[] = $order->id;
            OrderCancelRequest::query()
                ->whereKey($pending_request->id)
                ->update([
                    'status' => OrderCancelRequestStatus::CANCELLED,
                    'updated_at' => now()
                ]);
            if (!empty($order->admin_note) && $order->admin_note !== OrderHistoryActionEnum::REQUEST_CANCEL_12H_CONFIRMED) {
                continue;
            }
            if ($order->fulfill_status === OrderFulfillStatus::ON_HOLD) {
                $order->fulfill_status = OrderFulfillStatus::UNFULFILLED;
                $order->admin_note = null;
                $order->save();
                $order = $order->refresh();
                OrderHistory::insertLog(
                    $order,
                    OrderHistoryActionEnum::RESUME_FULFILL,
                    'Automatic cancellation of cancel order request - Customer did not confirm'
                );
                $storeInfo = StoreService::getStoreInfo($order->store_id);
                $dataSendMailLog = [
                    'sellerId' => $order->seller_id,
                    'storeId' => $order->store_id,
                    'orderId' => $order->id,
                ];
                $config = [
                    'to' => $order->customer_email,
                    'template' => 'buyer.resume_order',
                    'data' => [
                        'subject' => "Your order #{$order->order_number} was resumed",
                        'name' => $order->customer_name,
                        'email' => $order->customer_email,
                        'store_info' => $storeInfo,
                        'order' => $order,
                    ],
                    'sendMailLog' => $dataSendMailLog
                ];
                $status = sendEmail($config);
                if(!$status) {
                    graylogInfo('Send email resume order to customer failed.', [
                        'category' => 'send_email_resume_order',
                        'order_id' => $order->id,
                        'customer_email' => $order->customer_email,
                        'customer_name' => $order->customer_name,
                        'store_info' => $storeInfo,
                        'token' => $pending_request->id
                    ]);
                }
            }
        }
        return true;
    }
}
