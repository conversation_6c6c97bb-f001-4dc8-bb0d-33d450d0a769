<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\CurrencyEnum;
use App\Enums\EnvironmentEnum;
use App\Models\Currency;
use App\Services\StoreService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class CrawlCurrencyRate extends Command
{
    public const CURRENCY_CONVERSION_FEE = 1.022; // 2.2%
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'currency:crawl';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Crawl currency rate';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws \Exception
     */
    public function handle(): int
    {
        if (!app()->environment(EnvironmentEnum::PRODUCTION, EnvironmentEnum::STAGING)) {
            $this->info('This command only run on production or staging environment.');
            return self::SUCCESS;
        }
        $apiKey = config('services.api_layer.api_key');
        $appId = config('services.open_exchange.app_id');
        if (!$apiKey || !$appId) {
            $this->error('API key is not set.');
            return self::FAILURE;
        }
        $currencyCodes = StoreService::systemCurrencies()?->pluck('code')->join(',');
        dispatch(function () use ($apiKey, $appId, $currencyCodes) {
            $api_endpoint = 'https://api.apilayer.com/exchangerates_data/latest';
            $api_endpoint_fallback = 'https://openexchangerates.org/api/latest.json';
            $isDefault = false;
            try {
                $response = Http::acceptJson()
                    ->get($api_endpoint_fallback, [
                        'app_id' => $appId,
                        'base' => CurrencyEnum::USD,
                        'symbols' => $currencyCodes
                    ]);
            } catch (\Throwable $e) {
                $isDefault = true;
                $response = Http::acceptJson()
                    ->withHeaders(['apikey' => $apiKey])
                    ->get($api_endpoint, [
                        'base' => CurrencyEnum::USD,
                        'symbols' => $currencyCodes
                    ]);
            }

            if ($response->failed()) {
                return 0;
            }

            $json = $response->json();
            if (($isDefault && !$json['success']) || !isset($json['rates'])) {
                return 0;
            }
            try {
                $arr = [];
                $index = 0;
                foreach ($json['rates'] as $code => $rate) {
                    if (!in_array($code, [CurrencyEnum::USD, CurrencyEnum::EUR, CurrencyEnum::VND], true)) {
                        $rate *= self::CURRENCY_CONVERSION_FEE;
                    }
                    $arr[$index]['code'] = $code;
                    $arr[$index]['rate'] = $rate;
                    $index++;
                }
                Currency::query()->upsert($arr, ['code']);
                syncClearCache([CacheKeys::CURRENCIES]);
                syncClearCache([CacheKeys::CURRENCIES], CacheKeys::CACHE_TYPE_ALTERNATIVE);
                return 1;
            } catch (\Throwable $e) {
                logException($e, 'CrawlCurrencyRate::handle');
            }
            return 0;
        })->onQueue('clear-cache');
        return self::SUCCESS;
    }
}
