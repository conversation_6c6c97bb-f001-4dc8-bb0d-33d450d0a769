<?php

namespace App\Console\Commands;

use App\Enums\SupplierEnum;
use App\Facades\ProcessLock;
use App\Services\OrderService;
use Illuminate\Console\Command;

class ScanSendOrderLateShippingForSupplier extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'scan-send-order-late-shipping-for-supplier';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scan order late shipping for supplier';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        ProcessLock::handle($this->signature, callback: function () {
            $suppliers = [
                SupplierEnum::LUXURY_PRO,
                SupplierEnum::FM_EASY_CUSTOM,
                SupplierEnum::YOYCOL,
            ];
            $result = OrderService::scanOnDeliveryOrderProductOverXDays($suppliers);
            if (!empty($result)) {
                $this->info('Scan order late shipping for supplier success: ' . implode(', ', $result));
            } else {
                $this->info('No order late shipping for supplier');
            }
        });
        return self::SUCCESS;
    }
}
