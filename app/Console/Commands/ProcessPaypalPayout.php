<?php

namespace App\Console\Commands;

use App\Enums\Paypal\PayoutStatusEnum as PaypalPayoutStatus;
use App\Enums\SellerBillingStatus;
use App\Facades\ProcessLock;
use App\Library\Paypal\PaypalClient;
use App\Models\SellerBilling;
use App\Models\SystemConfig;
use Illuminate\Console\Command;

class ProcessPaypalPayout extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'paypal:process-payout';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This is a command handle payouts for method Paypal';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        ProcessLock::handle($this->signature, callback: function () {
            $paypalClient = PaypalClient::instance();

            if (is_null($paypalClient)) {
                $this->error('Paypal client is null');
                return 0;
            }

            $transactionUpdatedAts = [];
            $transactionCompletedIds = [];

            try {
                $sellerBilling = SellerBilling::getPaypalProcessBilling();
                $transactions = [
                    'items' => []
                ];

                if ($sellerBilling->isNotEmpty()) {
                    $this->info('Paypal: Found ' . $sellerBilling->count() . ' billing(s) to process');

                    $sellerBilling->each(function ($bill) use (&$transactions, &$transactionUpdatedAts, $paypalClient, &$transactionCompletedIds) {
                        $this->info('Paypal: Processing billing ' . $bill->id);

                        // Calculate fee for %2 on total amount
                        // Ex: 50 USD - 2% fee = 49.98 USD
                        $paypal_withdraw_settings = SystemConfig::getCustomConfig('paypal_withdraw_settings');
                        $paypal_withdraw_settings = optional($paypal_withdraw_settings)->json_data;
                        $withdrawPercentFee = 2;
                        $withdrawFee = 60;
                        if (!empty($paypal_withdraw_settings)) {
                            $paypal_withdraw_settings = json_decode($paypal_withdraw_settings, true);
                            $withdrawPercentFee = $paypal_withdraw_settings['percent_charge'] ?? 2;
                            $withdrawFee = $paypal_withdraw_settings['max_fee'] ?? 60;
                        }
                        $actualWithdrawFee = calcPercentOfAmount($withdrawPercentFee, $bill->amount);
                        if ($actualWithdrawFee > $withdrawFee) {
                            $amount = round(abs($bill->amount) - $withdrawFee, 2);
                        } else {
                            $amount = round(abs($bill->amount) - $actualWithdrawFee, 2);
                        }

                        $paypalEmail = $bill->payment_account->account_id ?? null;
                        $billItemId = $bill->id . '_' . $bill->seller_id;

                        if (is_null($bill->transaction_key)) {
                            if (!empty($paypalEmail)) {
                                $transactionUpdatedAts[$bill->id] = $bill->updated_at;
                                $transactions['items'][] = [
                                    'amount' => [
                                        'value' => $amount,
                                        'currency' => 'USD'
                                    ],
                                    'sender_item_id' => $billItemId,
                                    'receiver' => $paypalEmail
                                ];
                            }
                        } else if ($bill->status === SellerBillingStatus::PROCESSING) {
                            $paypalClient->payoutItemDetail($bill->transaction_key);

                            if ($paypalClient->getPayoutStatus() === PaypalPayoutStatus::SUCCESS) {
                                $transactionCompletedIds[] = $bill->id;
                            }
                        }
                    });
                }

                // free memory $sellerBilling
                unset($sellerBilling);

                $this->info('Paypal: Found ' . count($transactionCompletedIds) . ' transaction(s) completed');

                if (!empty($transactionCompletedIds)) {
                    SellerBilling::updateStatusToCompleted($transactionCompletedIds);
                    // free memory $transactionCompletedIds
                    unset($transactionCompletedIds);
                }

                $senderBatchId = 'SPPaypalPayout_' . now()->timestamp;

                if (empty($transactions['items'])) {
                    return 0;
                }

                $paypalClient->createPayouts($senderBatchId, $transactions, true);

                if (is_null($paypalClient->payoutBatchDetailItems)) {
                    return 0;
                }

                foreach ($paypalClient->payoutBatchDetailItems as $item) {
                    $senderItemId = data_get($item, 'payout_item.sender_item_id');
                    $payoutItemId = $item['payout_item_id'] ?? null;

                    if (!empty($senderItemId) && !empty($payoutItemId) && strpos($senderItemId, '_')) {
                        $transactionSenderInfo = explode('_', $senderItemId);

                        if (!empty($transactionSenderInfo)) {
                            $billId = trim($transactionSenderInfo[0]) ?? 0;
                            $transactionUpdatedAt = $transactionUpdatedAts[$billId] ?? null;

                            if (!empty($transactionUpdatedAt)) {
                                SellerBilling::updatePayoutTransactionKeyPaypal(
                                    $billId,
                                    $payoutItemId,
                                    $transactionUpdatedAt
                                );
                            }
                        }
                    }
                }

                unset($transactions, $transactionUpdatedAts);
            } catch (\Exception $e) {
                graylogError("ProcessPaypalPayout: Cron Error! - Message: {$e->getMessage()} - \n\r Trace info: {$e->getTraceAsString()}", [
                    'category' => 'process_payout_paypal_errors',
                    'user_type' => 'system',
                    'user_id' => null,
                    'action' => 'schedule'
                ]);
            }

            return 0;
        });
        return self::SUCCESS;
    }
}
