<?php

namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Jobs\UpdateSellerIdUpsellJob;
use App\Models\Product;
use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Console\Command;

class UpdateSellerIdUpsellCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update_seller_id_upsell';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $timeStartJobObject = SystemConfig::getCustomConfig(CacheKeys::UPDATE_SELLER_ID_UPSELL);
            if (isset($timeStartJobObject) && !empty($timeStartJobObject->value)) {
                return;
            }
            $jsonData = json_decode($timeStartJobObject->json_data, true);
            $type = data_get($jsonData, 'type', 'seller_sharding');
            $sellerIds = [];
            if (data_get($jsonData, 'seller_ids')) {
                $sellerIds = explode(',', data_get($jsonData, 'seller_ids'));
            }
            if ($type === 'seller_sharding') {
                User::query()
                    ->select('id', 'db_connection')
                    ->where('db_connection', '!=', 'mysql')
                    ->whereHas('collections')
                    ->when(!empty($sellerIds), fn ($query) => $query->whereIn('id', $sellerIds))
                    ->get()
                    ->each(function ($seller) {
                        UpdateSellerIdUpsellJob::dispatch(type: 'seller_sharding', seller: $seller);
                    });
            }
            else if ($type === 'seller_master') {
                UpdateSellerIdUpsellJob::dispatch(type: 'seller_master', seller: $sellerIds);
            }

        } catch (\Exception $e) {
            logException($e);
        }

    }
}
