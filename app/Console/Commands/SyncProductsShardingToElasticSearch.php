<?php
namespace App\Console\Commands;

use App\Enums\CacheKeys;
use App\Enums\SystemConfigTypeEnum;
use App\Jobs\SyncProductsShardingToElasticSearchJob;
use App\Models\SystemConfig;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Throwable;

class SyncProductsShardingToElasticSearch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:products-sharding-to-elasticsearch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync product type (campaign, product) to elasticsearch (active, inactive, reviewing, blocked)';

    /**
     * @throws Throwable
     */
    public function handle()
    {
        try {
            $timeStartJobObject = SystemConfig::getCustomConfig(CacheKeys::SYNC_PRODUCTS_SHARDING_TO_ELASTICSEARCH);
            if (isset($timeStartJobObject) && !empty($timeStartJobObject->value) && now()->clone()->lte(Carbon::parse($timeStartJobObject->value))) {
                return;
            }
            $value = optional($timeStartJobObject)->json_data;
            $setting = json_decode($value, true, 512, JSON_THROW_ON_ERROR);
            $currentDatetime = now()->clone()->addMinutes(10)->toDateTimeString();
            SystemConfig::setConfig(CacheKeys::SYNC_PRODUCTS_SHARDING_TO_ELASTICSEARCH, array(
                'value' => $currentDatetime,
                'status' => 1,
                'type' => SystemConfigTypeEnum::BACKEND
            ));
            SyncProductsShardingToElasticSearchJob::dispatch(null, $setting['limit'] ?? 1000, false, true, $setting['debug'] ?? false);
        } catch (Throwable $e) {
            Log::error("Sync products error: {$e->getMessage()} - " . "Line: {$e->getLine()} - " . "File: {$e->getFile()}");
        }
    }
}
