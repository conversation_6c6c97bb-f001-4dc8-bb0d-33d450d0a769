<?php

namespace App\Console\Commands;

use App\Enums\PaymentGatewayRefundStatusEnums;
use App\Jobs\ProcessRefundJob;
use App\Models\PaymentGatewayRefund;
use Illuminate\Console\Command;

class RefundToPaymentGatewayCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payment:refund-to-gateway';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Refund payment to gateway';

    public function handle()
    {
        $limit = 50;
        $refunds = PaymentGatewayRefund::query()
            ->with(['order'])
            ->where('status', PaymentGatewayRefundStatusEnums::PROCESSING)
            ->limit($limit)
            ->get();

        if ($refunds->isEmpty()) {
            return;
        }

        foreach ($refunds as $refund) {
            ProcessRefundJob::dispatch($refund)->onQueue('refund');
        }
    }
}
