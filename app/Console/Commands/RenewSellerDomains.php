<?php

namespace App\Console\Commands;

use App\Facades\ProcessLock;
use App\Jobs\Seller\ProcessRenewDomainJob;
use App\Models\SellerDomain;
use Illuminate\Console\Command;

class RenewSellerDomains extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'renew-seller-domains';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Renew seller domains which are expired';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        ProcessLock::handle($this->signature, callback: function () {
            // find all expired domains (today) and enabled auto_renew
            $domains = SellerDomain::query()->where('domain_expired_at', '<=', now())
                ->where('auto_renew', true)
                ->with('seller:id,email,balance')
                ->get();

            if ($domains->isEmpty()) {
                $this->info('No domains to renew');
                return;
            }
            // renew domains
            $domains->each(function (SellerDomain $domain) {
                if ($domain->seller->balance > 0) {
                    ProcessRenewDomainJob::dispatch($domain->id);
                }
            });
        });
        return self::SUCCESS;
    }
}
