<?php

namespace App\Imports;

use App\Enums\ShippingMethodEnum;
use App\Models\Product;
use App\Models\ShippingRule;
use App\Models\Template;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

// no format heading
HeadingRowFormatter::default('none');

class TemplateShippingRuleImport implements ToCollection, WithHeadingRow
{
    // fields: Product SKU;Variant Key;Supplier Id;standard-base-150,*,US;standard-extra-150,*,US;express-base-US,*;express-extra-US,*...

    public function collection(Collection $rows): void
    {
        $arr    = [];
        $arrSku = [];

        foreach ($rows as $row) {
            $productSku         = $row->first(); //first column is product sku
            $arrSku[]           = $productSku;
            $arr[$productSku][] = $this->getShippingCost($row);
        }

        $productsAvailable = Template::query()
            ->select('sku', 'id')
            ->whereIn('sku', $arrSku)
            ->get();

        $arrShippingRule = [];
        $arrProduct      = [];
        $arrProductId    = [];
        foreach ($productsAvailable as $product) {
            $productId      = $product->id;
            $arrProductId[] = $productId;
            $productSku     = $product->sku;
            $maxPrice       = 0;
            foreach ($arr[$productSku] as $eachProduct) {
                foreach ($eachProduct as $each) {
                    $each['product_id'] = $productId;
                    if (
                        $each['shipping_method'] === ShippingMethodEnum::STANDARD
                        &&
                        $maxPrice < $each['shipping_cost']
                    ) {
                        $maxPrice = $each['shipping_cost'];
                    }
                    $arrShippingRule[] = $each;
                }
            }
            $arrProduct[] = [
                'id'            => $productId,
                'shipping_cost' => $maxPrice,
            ];
        }

        Product::upsert($arrProduct, 'id');
        ShippingRule::whereIn('product_id', $arrProductId)->delete();
        foreach (array_chunk($arrShippingRule, 1000) as $each) {
            ShippingRule::insert($each);
        }
    }

    private function getShippingCost($row): array
    {
        $shippingMethods = ['standard', 'express'];
        $arr             = [];
        $arr['data']     = [];
        foreach ($row as $key => $value) {
            $arr['supplier_id'] = $row['Supplier Id'] ?? '';
            $arr['variant_key'] = strtolower(str_replace(' ', '', Arr::get($row, 'Variant Key', '')));
            foreach ($shippingMethods as $shippingMethod) {
                // ex: standard-base-US,150,*;standard-extra-US,150,*,FR
                // if column title have shipping method, remove shipping method from string
                if (strpos($key, $shippingMethod) !== false) {
                    [$shippingMethod, $type, $locationCodes] = explode('-', $key);
                    $locationCodes = explode(',', $locationCodes);
                    foreach ($locationCodes as $locationCode) {
                        $arr['data'][$locationCode][$shippingMethod][$type] = $value;
                    }
                }
            }
        }

        $arrUpdated = [];
        foreach ($arr['data'] as $locationCode => $each1) {
            $supplierId = $arr['supplier_id'];
            $variantKey = $arr['variant_key'];
            foreach ($each1 as $shippingMethod => $each2) {
                if (!empty($each2['base'])) { // prevent empty
                    $arrUpdated[] = [
                        'supplier_id'     => ($supplierId !== '') ? $supplierId : null,
                        'variant_key'     => ($variantKey !== '') ? $variantKey : null,
                        'shipping_method' => $shippingMethod,
                        'location_code'   => strtoupper($locationCode),
                        'shipping_cost'   => $each2['base'],
                        'extra_cost'      => $each2['extra'],
                        'min_days'        => Arr::get($each2, 'min_days'),
                        'max_days'        => Arr::get($each2, 'max_days'),
                    ];
                }
            }
        }

        return $arrUpdated;
    }
}
