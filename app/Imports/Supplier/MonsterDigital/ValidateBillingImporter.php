<?php

namespace App\Imports\Supplier\MonsterDigital;

use App\Imports\Supplier\BaseValidateBillingImporter;

/**
 * Class ValidateBillingImporter
 */
class ValidateBillingImporter  extends BaseValidateBillingImporter
{
    /**
     * @param     array     $row
     *
     * @return bool
     */
    public function validRow(array $row): bool
    {
        return $this->mapFulfillOrderId($row) && $this->mapQuantity($row);
    }

    /**
     * Map fulfill order id
     *
     * @override BaseValidateBillingImporter::mapFulfillOrderId()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapFulfillOrderId(array $row): ?string
    {
        return $row['partner_order_id'] ?? '';
    }

    /**
     * Không có order id
     *
     * @override BaseValidateBillingImporter::mapOrderId()
     *
     * @param     array     $row
     *
     * @return string|null
     */
    public function mapOrderId(array $row): ?string
    {
        return null;
    }

    /**
     * Get quantity
     *
     * @override BaseValidateBillingImporter::mapQuantity()
     *
     * @param     array     $row
     *
     * @return int
     */
    public function mapQuantity(array $row): ?int
    {
        return (int) ($row['quantity'] ?? 0);
    }

    /**
     * Get fulfill sku
     *
     * @override BaseValidateBillingImporter::mapFulfillSku()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapFulfillSku(array $row): ?string
    {
        return $row['sku'] ?? null;
    }

    /**
     * Cả 2 file excel shipping và item đều có không có tracking code
     *
     * @override BaseValidateBillingImporter::mapTrackingCode()
     *
     * @param     array     $row
     *
     * @return string
     */
    public function mapTrackingCode(array $row): ?string
    {
        return $row['trackingnumber'] ?? null;
    }

    /**
     * Map fulfill base cost
     *
     * @override BaseValidateBillingImporter::mapFulfillBaseCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillBaseCost(array $row): ?float
    {
        return $row['line_total'];
    }

    /**
     * Map fulfill shipping cost
     *
     * @override BaseValidateBillingImporter::mapFulfillShippingCost()
     *
     * @param     array     $row
     *
     * @return float
     */
    public function mapFulfillShippingCost(array $row): ?float
    {
        return $row['ship_cost'];
    }

    /**
     * @override BaseValidateBillingImporter::stopAtFirstError()
     *
     * @return bool
     */
    public function stopAtFirstError(): bool
    {
        return false;
    }

    /**
     * @return string[]
     */
    public function validators(): array
    {
        return [
            'billedValidator',
            'quantityValidator',
            'fulfillStatusValidator',
            'fulfillBaseCostValidator',
        ];
    }
}
