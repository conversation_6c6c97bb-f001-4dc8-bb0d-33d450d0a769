<?php /** @noinspection DuplicatedCode */

namespace App\Imports\Supplier\QTCO;

use App\Enums\ProductOptionEnum;
use App\Enums\ProductStatus;
use App\Imports\Supplier\ImportTrait;
use App\Models\FulfillProduct;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class InStockImport implements ToArray, WithHeadingRow
{
    use ImportTrait;

    public function array(array $array): void
    {
        // dd($array);
        $arr = [];
        $lastProductSku = '';

        foreach ($array as $index => $row) {
            if (
                empty($lastProductSku)
                || $lastProductSku !== $row['style_number']
            ) {
                $lastProductSku = $row['style_number'];
                $productName = $row['type'] . ' - ' . $row['brand'] . ' - ' . $row['body'] . ' - ' . $row['style_number'];
                $arr[$lastProductSku]['product'] = [
                    'name' => $productName,
                ];
            }

            $color = $this->mappingOptions(
                $row['color'],
                ProductOptionEnum::COLOR,
            );
            $size = $this->mappingOptions(
                $row['size'],
                ProductOptionEnum::SIZE,
            );
            $options = [
                ProductOptionEnum::COLOR => $color,
                ProductOptionEnum::SIZE  => $size,
            ];
            sortArrayOptions($options);

            if (empty($arr[$lastProductSku]['product']['options'])) {
                $arr[$lastProductSku]['product']['options'] = [];
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR] = [];
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE] = [];
            }
            if (
                !in_array(
                    $color,
                    $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR]
                )
            ) {
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::COLOR][] = $color;
            }
            if (
                !in_array(
                    $size,
                    $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE]
                )
            ) {
                $arr[$lastProductSku]['product']['options'][ProductOptionEnum::SIZE][] = $size;
            }

            $arr[$lastProductSku]['variants'][] = [
                'sku'         => $row['sku'],
                'variant_key' => getVariantKey($options),
            ];
        }

        $arrVariants = [];
        foreach ($arr as $productSku => $each) {
            $productOption = json_encode($each['product']['options']);
            $product = FulfillProduct::query()
                ->updateOrCreate([
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ], [
                    'name'    => $each['product']['name'],
                    'options' => $productOption,
                    'status'  => ProductStatus::ACTIVE,
                ]);

            $productId = $product->id;
            foreach ($each['variants'] as $variant) {
                $arrVariants[] = [
                    'sku'         => $variant['sku'],
                    'product_id'  => $productId,
                    'variant_key' => $variant['variant_key'],
                ];
            }
        }

        $this->updateToDB($arrVariants);
    }
}
