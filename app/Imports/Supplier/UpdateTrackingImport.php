<?php

namespace App\Imports\Supplier;

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderHistoryActionEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Enums\OrderStatus;
use App\Models\Order;
use App\Models\OrderHistory;
use App\Models\OrderProduct;
use App\Services\OrderService;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithColumnLimit;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class UpdateTrackingImport implements ToCollection, WithHeadingRow, WithChunkReading, WithColumnLimit
{
    protected $supplier_id;
    protected $supplier_name;
    public $successCount = 0;
    public $failCount = 0;
    public $message = [];

    public function __construct($supplierId, $supplierName)
    {
        $this->supplier_id = $supplierId;
        $this->supplier_name = $supplierName;
    }

    public function collection(Collection $collection): void
    {
        foreach ($collection as $row) {
            $orderProductId = data_get($row, 'order_product_id');
            $trackingCode = data_get($row, 'tracking_code');
            $trackingUrl = data_get($row, 'tracking_url');
            $shippingMethod = data_get($row, 'shipping_method');
            $shippingCarrier = data_get($row, 'shipping_carrier');

            if (Str::startsWith($trackingCode, "'")) {
                $trackingCode = substr($trackingCode, 1);
            }

            if (Str::startsWith($orderProductId, "'")) {
                $orderProductId = (int) substr($orderProductId, 1);
            }

            if (empty($orderProductId) || (empty($trackingCode) && empty($trackingUrl))) {
                $this->failCount++;
                $this->message[] = 'Order product ' . $orderProductId . ' missing required field';
                continue;
            }
            $cfg = suppliers()->where('supplier_id', $this->supplier_id)->first();
            if (!$cfg) {
                $this->message[] = 'Supplier ' . $this->supplier_name . ' not exist';
                $this->failCount++;
                continue;
            }
            $shipping_carriers = data_get($cfg, 'shipping_carriers', []);
            $orderProduct = OrderProduct::query()
                ->where('id', $orderProductId)
                ->where('supplier_id', $this->supplier_id)
                ->whereIn('fulfill_status', [
                    OrderProductFulfillStatus::PROCESSING,
                    OrderProductFulfillStatus::ON_DELIVERY,
                ])->with('order')->first();

            if (!$orderProduct) {
                $this->message[] = 'Order product ' . $orderProductId . ' status is not processing or on delivery or not belong to supplier ' . $this->supplier_name . ' or not exist';
                $this->failCount++;
                continue;
            }

            try {
                DB::beginTransaction();
                $orderProduct->shipping_carrier = empty($shippingCarrier) ? data_get($shipping_carriers, $shippingMethod) : $shippingCarrier;
                $orderProduct->tracking_code = $trackingCode;
                $orderProduct->tracking_url = $trackingUrl;
                $orderProduct->fulfill_status = $orderProduct->fulfill_status === OrderProductFulfillStatus::ON_DELIVERY ? OrderProductFulfillStatus::ON_DELIVERY : OrderProductFulfillStatus::PROCESSING;
                $orderProduct->save();
                $countUnfulfilled = OrderProduct::query()
                    ->where('order_id', $orderProduct->order_id)
                    ->whereNotIn('fulfill_status', [
                        OrderProductFulfillStatus::FULFILLED,
                        OrderProductFulfillStatus::CANCELLED,
                        OrderProductFulfillStatus::ON_DELIVERY,
                    ])
                    ->count();
                $updated = 0;
                if ($countUnfulfilled === 0) {
                    $updated = Order::query()
                        ->whereKey($orderProduct->order_id)
                        ->update([
                            'fulfill_status' => OrderFulfillStatus::ON_DELIVERY,
                            'status' => OrderStatus::COMPLETED,
                        ]);
                }
                if ($updated > 0) {
                    OrderHistory::insertLog(
                        $orderProduct->order,
                        OrderHistoryActionEnum::UPDATED,
                        $this->supplier_name . ' update product ' . $orderProduct->id . ': Tracking code ' . $trackingCode,
                    );
                }
                DB::commit();
                OrderService::updateOrderTrackingStatus($orderProduct->order_id, $trackingCode);
            } catch (\Throwable $e) {
                $this->message[] = $e->getMessage();
                DB::rollBack();
                $this->failCount++;
                continue;
            }
            $this->successCount++;
        }
        unset($collection);
        gc_collect_cycles();
    }

    public function endColumn(): string
    {
        return 'AG';
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
