<?php

namespace App\Imports\Product;

use App\Enums\CacheKeys;
use App\Models\ProductSizeGuide;
use App\Models\Template;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use RuntimeException;

class TemplateSizeGuideImport implements ToArray, WithHeadingRow
{
    public function array($array): void
    {
        $arr        = [];
        $productIds = [];
        foreach ($array as $each) {
            $sku = $each['product_sku'];

            if (empty($productIds[$sku])) {
                $productId = Template::query()->where('sku', $sku)->value('id');
                if (is_null($productId)) {
                    throw new RuntimeException('Not found product sku ' . $sku);
                }
                $productIds[$sku] = $productId;
            }

            ProductSizeGuide::query()->where('product_id', $productIds[$sku])->delete();
            $arr[] = [
                'product_id' => $productIds[$sku],
                'sku'        => $sku,
                'size'       => Arr::get($each, 'size'),
                'length'     => Arr::get($each, 'length'),
                'width'      => Arr::get($each, 'width'),
                'sleeve'     => Arr::get($each, 'sleeve'),
                'waist'      => Arr::get($each, 'waist'),
                'hip'        => Arr::get($each, 'hip'),
                'height'     => Arr::get($each, 'height'),
                'weight'     => Arr::get($each, 'weight'),
                'unit'       => Arr::get($each, 'unit') !== 'cm' ? 'in' : 'cm',
                'note'       => Arr::get($each, 'note'),
            ];
        }
        ProductSizeGuide::insert($arr);
        syncClearCache([
            CacheKeys::SIZE_GUIDE,
            CacheKeys::GENERAL_SETTINGS,
        ], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }
}
