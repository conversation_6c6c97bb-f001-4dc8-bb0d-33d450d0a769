<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\FulfillmentProduct
 *
 * @property int $id
 * @property int $fulfillment_id
 * @property int $order_product_id
 * @property int $fulfilled_quantity
 * @method static \Database\Factories\FulfillmentProductFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct whereFulfilledQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct whereFulfillmentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|FulfillmentProduct whereOrderProductId($value)
 * @mixin \Eloquent
 */
class FulfillmentProduct extends Model
{
    use HasFactory;

    protected $table = 'fulfillment_product';

    public $timestamps = false;
}
