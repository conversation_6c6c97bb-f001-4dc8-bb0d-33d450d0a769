<?php

namespace App\Models;

/**
 * App\Models\Faq
 *
 * @property int $id
 * @property int|null $category_id
 * @property string $question
 * @property string|null $answer
 * @property string $language
 * @property int $position
 * @method static \Illuminate\Database\Eloquent\Builder|Faq newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Faq newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Faq query()
 * @method static \Illuminate\Database\Eloquent\Builder|Faq whereAnswer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Faq whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Faq whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Faq whereLanguage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Faq wherePosition($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Faq whereQuestion($value)
 * @mixin \Eloquent
 */
class Faq extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'faq';
}
