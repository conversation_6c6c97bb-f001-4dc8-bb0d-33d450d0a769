<?php

namespace App\Models;

use App\Enums\CacheKeys;


/**
 * App\Models\SystemTimeZone
 *
 * @property string $timezone_name
 * @property float $utc_offset
 * @property float $utc_dst_offset
 * @property string|null $country_code
 * @property string $status
 * @property string $link_to
 * @property-read string $display_name
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone query()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereCountryCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereLinkTo($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereTimezoneName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereUtcDstOffset($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemTimeZone whereUtcOffset($value)
 * @mixin \Eloquent
 */
class SystemTimeZone extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'system_timezone';

    protected $appends = ['display_name'];

    public static function systemTimezones()
    {
        return cacheGet(CacheKeys::TIMEZONES, CacheKeys::CACHE_30D, function () {
            return SystemTimeZone::query()
                ->orderBy('utc_offset')
                ->get(['timezone_name', 'utc_offset', 'utc_dst_offset']);
        }, [], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }

    public function getDisplayNameAttribute(): string
    {
        $utcOffset = $this->utc_offset;
        $offsetStr = 'UTC' . ($utcOffset >= 0 ? '+' : '-') . sprintf('%02d', (int)abs($utcOffset)) . ':' . sprintf('%02d', (60 * abs($utcOffset - (int)$utcOffset)));
        return $offsetStr . ' - ' . $this->timezone_name;
    }
}
