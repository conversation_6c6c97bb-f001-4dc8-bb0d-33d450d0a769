<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\Fulfillment
 *
 * @property int $id
 * @property int $order_id
 * @property int|null $seller_id
 * @property int|null $store_id
 * @property int|null $supplier_id
 * @property string|null $supplier_name
 * @property string|null $shipping_carrier
 * @property int|null $items_quantity
 * @property string|null $tracking_number
 * @property string|null $tracking_url
 * @property string $status
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\FulfillmentProduct[] $fulfillment_products
 * @property-read int|null $fulfillment_products_count
 * @property-read \App\Models\Order $order
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\OrderProduct[] $orderProducts
 * @property-read int|null $order_products_count
 * @method static \Database\Factories\FulfillmentFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment query()
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereItemsQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereShippingCarrier($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereSupplierId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereSupplierName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereTrackingNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereTrackingUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Fulfillment whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Fulfillment extends Model
{
    use HasFactory;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'fulfillment';

    public function fulfillment_products(): HasMany
    {
        return $this->hasMany(FulfillmentProduct::class, 'fulfillment_id', 'id')
            ->select([
                'fulfillment_product.*',
                'order_product.campaign_title',
                'order_product.product_name',
                'order_product.thumb_url',
                'order_product.options',
                'order_product.price',
                'order_product.total_amount',
            ])
            ->join('order_product', 'order_product.id', '=', 'fulfillment_product.order_product_id');
    }

    public function orderProducts(): HasMany
    {
        return $this->hasMany(OrderProduct::class, 'order_id', 'order_id');
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
