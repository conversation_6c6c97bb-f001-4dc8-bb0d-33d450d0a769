<?php

namespace App\Models;

use App\Enums\ProductType;
use App\Traits\ScopeFilterAnalyticTrait;
use App\Traits\ScopeFilterDateRangeTrait;
use App\Traits\SPModel;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * App\Models\OrderProduct
 *
 * @property int $id
 * @property int $order_id
 * @property int|null $product_id
 * @property int|null $campaign_id
 * @property int|null $seller_id
 * @property int|null $auth_id
 * @property int|null $ref_id
 * @property int|null $template_id
 * @property int|null $shipping_rule_id
 * @property string|null $campaign_title copy from campaign
 * @property string|null $product_name copy from product type
 * @property string|null $product_url
 * @property string $thumb_url
 * @property string|null $options
 * @property string|null $custom_options
 * @property string|null $custom_print_space custom print space
 * @property string|null $color
 * @property string|null $size
 * @property float $cost
 * @property float $base_cost
 * @property float $price
 * @property float|null $adjust_test_price
 * @property float $extra_custom_fee
 * @property float $weight pound:lbs
 * @property int $quantity
 * @property float $tax
 * @property float $total_amount
 * @property float $fulfill_shipping_cost
 * @property float $base_shipping_cost
 * @property float $shipping_cost
 * @property float $discount_amount discount of product if applied
 * @property float $profit
 * @property float $seller_profit
 * @property float $artist_profit
 * @property float $sen_points
 * @property int $upsell_status 1:yes,0:no
 * @property int $fulfilled_quantity set if create fulfillment
 * @property string $fulfill_status 'unfulfilled','fulfilled','partial_fulfilled','cancelled','processing','on_hold','rejected','exception','invalid','out_of_stock','no_ship','pending','reviewing','designing','on_delivery'
 * @property string $sen_fulfill_status YES,NO,REVIEW,PENDING
 * @property int $refund_quantity
 * @property string|null $sku
 * @property int|null $supplier_id
 * @property string|null $supplier_name
 * @property string|null $fulfill_sku
 * @property int|null $fulfill_product_id
 * @property float $fulfill_cost
 * @property float $extra_print_cost
 * @property string $fulfill_order_id
 * @property string|null $fulfill_exception_log
 * @property string|null $shipping_carrier
 * @property string|null $tracking_code
 * @property string|null $tracking_url
 * @property string|null $tracking_status
 * @property string $updated_at
 * @property string|null $fulfilled_at
 * @property string|null $delivered_at
 * @property Carbon|null $deleted_at
 * @property string|null $received_at
 * @property float $processing_day
 * @property float|null $shipping_day
 * @property string|null $billed_at
 * @property int $personalized 1:yes,0:no (yes if customer customize text/image)
 * @property int $full_printed 1:yes (if product is full printed
 * @property string|null $exported_at
 * @property int $shard_id
 * @property int|null $collection_id
 * @property string $tm_status
 * @property string|null $external_product_id
 * @property string|null $external_fulfillment_id
 * @property string|null $external_id
 * @property-read User|null $author
 * @property-read Campaign|null $campaign
 * @property-read Collection|Design[] $customOptionDesigns
 * @property-read int|null $custom_option_designs_count
 * @property-read Collection|File[] $designs
 * @property-read int|null $designs_count
 * @property-read Collection|File[] $fulfillOrderDesigns
 * @property-read int|null $fulfill_order_designs_count
 * @property-read Collection|File[] $fulfillOrderMockups
 * @property-read int|null $fulfill_order_mockups_count
 * @property-read array $collections
 * @property-read mixed $custom_options_pb
 * @property-read mixed $custom_options_regular
 * @property-read array|null $design_pb
 * @property-read mixed $print_url
 * @property-read array $stores
 * @property-read Collection|File[] $mockups
 * @property-read int|null $mockups_count
 * @property-read Order $order
 * @property-read Pricing|null $pricing
 * @property-read Collection|File[] $printDesigns
 * @property-read int|null $print_designs_count
 * @property-read Product|null $product
 * @property-read ProductReview|null $productReview
 * @property-read ProductPoint|null $product_point
 * @property-read User|null $seller
 * @property-read ShippingRule|null $shipping_rule
 * @property-read Supplier|null $supplier
 * @property-read Product|null $template
 * @property-read TrackingStatus|null $trackingStatus
 * @method static \Database\Factories\OrderProductFactory factory(...$parameters)
 * @method static Builder|IndexOrderProduct filterDateRange($dateRange, $startDate = null, $endDate = null, $column = null, $sellerId = null, $isOrderBy = false)
 * @method static Builder|IndexOrderProduct filterDateRangePaidAt($dateRange, $startDate = null, $endDate = null, $isOrderBy = true, $column = 'order.paid_at')
 * @method static Builder|IndexOrderProduct filterFulfill($arrFulfillStatus = null, $supplierId = null, $isListing = false)
 * @method static Builder|IndexOrderProduct filterFulfillProduct($arrFulfillStatus, $supplierId = 0, $personalized = false, $isListing = true)
 * @method static Builder|IndexOrderProduct makeHiddenAll()
 * @method static Builder|IndexOrderProduct newModelQuery()
 * @method static Builder|IndexOrderProduct newQuery()
 * @method static Builder|IndexOrderProduct onlyTrashed()
 * @method static Builder|IndexOrderProduct query()
 * @method static Builder|IndexOrderProduct shippingLate()
 * @method static Builder|IndexOrderProduct whereAdjustTestPrice($value)
 * @method static Builder|IndexOrderProduct whereArtistProfit($value)
 * @method static Builder|IndexOrderProduct whereAuthId($value)
 * @method static Builder|IndexOrderProduct whereBaseCost($value)
 * @method static Builder|IndexOrderProduct whereBaseShippingCost($value)
 * @method static Builder|IndexOrderProduct whereBilledAt($value)
 * @method static Builder|IndexOrderProduct whereCampaignId($value)
 * @method static Builder|IndexOrderProduct whereCampaignTitle($value)
 * @method static Builder|IndexOrderProduct whereCollectionId($value)
 * @method static Builder|IndexOrderProduct whereColor($value)
 * @method static Builder|IndexOrderProduct whereCost($value)
 * @method static Builder|IndexOrderProduct whereCustomOptions($value)
 * @method static Builder|IndexOrderProduct whereCustomPrintSpace($value)
 * @method static Builder|IndexOrderProduct whereDeletedAt($value)
 * @method static Builder|IndexOrderProduct whereDeliveredAt($value)
 * @method static Builder|IndexOrderProduct whereDiscountAmount($value)
 * @method static Builder|IndexOrderProduct whereExportedAt($value)
 * @method static Builder|IndexOrderProduct whereExternalFulfillmentId($value)
 * @method static Builder|IndexOrderProduct whereExternalId($value)
 * @method static Builder|IndexOrderProduct whereExternalProductId($value)
 * @method static Builder|IndexOrderProduct whereExtraCustomFee($value)
 * @method static Builder|IndexOrderProduct whereFulfillCost($value)
 * @method static Builder|IndexOrderProduct whereFulfillExceptionLog($value)
 * @method static Builder|IndexOrderProduct whereFulfillOrderId($value)
 * @method static Builder|IndexOrderProduct whereFulfillProductId($value)
 * @method static Builder|IndexOrderProduct whereFulfillSku($value)
 * @method static Builder|IndexOrderProduct whereFulfillStatus($value)
 * @method static Builder|IndexOrderProduct whereFulfilledAt($value)
 * @method static Builder|IndexOrderProduct whereFulfilledQuantity($value)
 * @method static Builder|IndexOrderProduct whereFullPrinted($value)
 * @method static Builder|IndexOrderProduct whereId($value)
 * @method static Builder|IndexOrderProduct whereOptions($value)
 * @method static Builder|IndexOrderProduct whereOrderId($value)
 * @method static Builder|IndexOrderProduct wherePersonalized($value)
 * @method static Builder|IndexOrderProduct wherePrice($value)
 * @method static Builder|IndexOrderProduct whereProcessingDay($value)
 * @method static Builder|IndexOrderProduct whereProductId($value)
 * @method static Builder|IndexOrderProduct whereProductName($value)
 * @method static Builder|IndexOrderProduct whereProductUrl($value)
 * @method static Builder|IndexOrderProduct whereProfit($value)
 * @method static Builder|IndexOrderProduct whereQuantity($value)
 * @method static Builder|IndexOrderProduct whereReceivedAt($value)
 * @method static Builder|IndexOrderProduct whereRefId($value)
 * @method static Builder|IndexOrderProduct whereRefundQuantity($value)
 * @method static Builder|IndexOrderProduct whereSellerId($value)
 * @method static Builder|IndexOrderProduct whereSellerProfit($value)
 * @method static Builder|IndexOrderProduct whereSenFulfillStatus($value)
 * @method static Builder|IndexOrderProduct whereSenPoints($value)
 * @method static Builder|IndexOrderProduct whereShardId($value)
 * @method static Builder|IndexOrderProduct whereShippingCarrier($value)
 * @method static Builder|IndexOrderProduct whereShippingCost($value)
 * @method static Builder|IndexOrderProduct whereShippingDay($value)
 * @method static Builder|IndexOrderProduct whereShippingRuleId($value)
 * @method static Builder|IndexOrderProduct whereSize($value)
 * @method static Builder|IndexOrderProduct whereSku($value)
 * @method static Builder|IndexOrderProduct whereSupplierId($value)
 * @method static Builder|IndexOrderProduct whereSupplierName($value)
 * @method static Builder|IndexOrderProduct whereTax($value)
 * @method static Builder|IndexOrderProduct whereTemplateId($value)
 * @method static Builder|IndexOrderProduct whereThumbUrl($value)
 * @method static Builder|IndexOrderProduct whereTmStatus($value)
 * @method static Builder|IndexOrderProduct whereTotalAmount($value)
 * @method static Builder|IndexOrderProduct whereTrackingCode($value)
 * @method static Builder|IndexOrderProduct whereTrackingStatus($value)
 * @method static Builder|IndexOrderProduct whereTrackingUrl($value)
 * @method static Builder|IndexOrderProduct whereUpdatedAt($value)
 * @method static Builder|IndexOrderProduct whereUpsellStatus($value)
 * @method static Builder|IndexOrderProduct whereWeight($value)
 * @method static Builder|IndexOrderProduct withTrashed()
 * @method static Builder|IndexOrderProduct withoutTrashed()
 * @mixin \Eloquent
 */
class IndexOrderProduct extends Model
{
    use HasFactory;
    use SPModel;
    use SoftDeletes;
    use ScopeFilterDateRangeTrait;
    use ScopeFilterAnalyticTrait;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'order_product';
    protected $connection = 'singlestore';
    public const FILTER_COLUMN_DATE = 'order.paid_at';

    public $timestamps = false;

    public $incrementing = false;

    public function order(): BelongsTo
    {
        return $this->belongsTo(IndexOrder::class, 'order_id');
    }

    public function template_product(): BelongsTo
    {
        return $this->belongsTo(IndexProduct::class, 'template_id')->where('product_type', ProductType::TEMPLATE);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function scopeCountActiveSellers($query)
    {
        return $query->selectRaw('count(DISTINCT(`order_product`.seller_id)) as active_sellers');
    }

    public function scopeCalculateOrders($query)
    {
        return $query->selectRaw('COUNT(DISTINCT(`order_product`.order_id)) AS orders');
    }

    public function scopeCalculateItems($query)
    {
        return $query->selectRaw('SUM(`order_product`.quantity) AS items');
    }

    public function scopeCalculateProfits($query)
    {
        return $query->selectRaw('SUM(`order_product`.seller_profit) AS profits');
    }

    public function scopeCalculateSales($query)
    {
        return $query->selectRaw('SUM(`order_product`.total_amount) AS sales');
    }

    public function scopeGetAnalyticOverview($query)
    {
        return $query
            ->calculateOrders()
            ->calculateItems()
            ->calculateSales()
            ->calculateProfits();
    }

}
