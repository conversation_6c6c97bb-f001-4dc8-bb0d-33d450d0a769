<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ProductReviewFile
 *
 * @property string $id
 * @property string $product_review_id
 * @property string $type
 * @property string $url
 * @property string|null $thumb_url
 * @property string $token
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ProductReview $productReview
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereProductReviewId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereThumbUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductReviewFile whereUrl($value)
 * @mixin \Eloquent
 */
class ProductReviewFile extends Model
{
    protected $table = 'product_review_files';
    protected $connection = 'mysql_sg';
    public $incrementing = false;
    public $keyType = 'string';
    protected $fillable = [
        'product_review_id',
        'type',
        'url',
        'thumb_url',
        'token',
        'status'
    ];

    protected static function booted(): void
    {
        static::creating(
            function ($model) {
                $model->id = generateUUID();
            }
        );
    }

    public function productReview(): BelongsTo
    {
        return $this->belongsTo(ProductReview::class);
    }
}
