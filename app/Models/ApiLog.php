<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ApiLog
 *
 * @property string $id
 * @property string|null $name
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property string|null $from
 * @property string|null $reference_id
 * @property string|null $reference_type
 * @property string|null $order_id
 * @property string|null $fulfill_order_id
 * @property string|null $url
 * @property string|null $request_body
 * @property string|null $response_body
 * @property string $type
 * @property string $created_at
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog filter($requests)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereFulfillOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereReferenceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereReferenceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereRequestBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereResponseBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ApiLog whereUserAgent($value)
 * @mixin \Eloquent
 */
class ApiLog extends Model
{
    use HasFactory;

    protected $table = 'api_logs';
    protected $fillable = [
        'id',
        'name',
        'ip_address',
        'user_agent',
        'from',
        'reference_id',
        'reference_type',
        'order_id',
        'fulfill_order_id',
        'url',
        'request_body',
        'response_body',
        'type',
    ];
    public $timestamps = false;
    public $incrementing = false;
    public $keyType = 'string';

    protected static function booted(): void
    {
        static::creating(
            static function ($model) {
                $model->id = generateUUID();
            }
        );
    }

    private const ARR_FILTER = [
        'id',
        'name',
        'from',
        'reference_id',
        'reference_type',
        'order_id',
        'fulfill_order_id',
        'type',
        'start_at',
        'end_at',
        'url',
    ];

    /** @noinspection ReturnTypeCanBeDeclaredInspection */
    public function scopeFilter($query, $requests)
    {
        foreach ($requests as $column => $value) {
            $query->when(
                !is_null($value) && in_array($column, self::ARR_FILTER),
                static function ($q) use ($column, $value) {
                    if ($column === 'start_at') {
                        return $q->whereDate('created_at', '>=', $value);
                    }
                    if ($column === 'end_at') {
                        return $q->whereDate('created_at', '<=', $value);
                    }
                    if ($column === 'fulfill_order_id') {
                        return $q->where($column, 'like', "$value%");
                    }

                    return $q->where($column, (string)$value);
                }
            );
        }

        return $query;
    }

    // substring fulfill_order_id by _ or s and take the first one
    // cause re-order make fulfill_order_id like this: {id}_{timestamp} or {id}s{timestamp}
    public static function getSubStringToJoin($table = 'api_logs'): string
    {
        return "SUBSTRING_INDEX(SUBSTRING_INDEX(`$table`.`fulfill_order_id`,'-',1),'s',1)";
    }

    public function fulfill_order (): BelongsTo {
        return $this->belongsTo(Order::class, 'fulfill_order_id', 'id');
    }
}
