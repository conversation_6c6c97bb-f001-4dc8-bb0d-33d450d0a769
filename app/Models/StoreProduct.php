<?php

namespace App\Models;

use App\Enums\ProductStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\StoreProduct
 *
 * @property int $store_id
 * @property int $product_id
 * @property-read \App\Models\Campaign $campaign
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct whereStoreId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|StoreProduct filterAddedCampaign()
 * @mixin \Eloquent
 */
class StoreProduct extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'store_product';
    protected $fillable = ['store_id', 'product_id'];
    public $timestamps = false;

    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'product_id', 'id');
    }

    public function scopeFilterAddedCampaign($query)
    {
        return $query->leftJoin('product', 'product.id', '=', 'store_product.product_id')
            ->where('product.status', ProductStatus::ACTIVE)
            ->where('product.is_deleted', 0)
            ->orderBy('store_product.product_id', 'DESC')
            ->limit(30);
    }
}
