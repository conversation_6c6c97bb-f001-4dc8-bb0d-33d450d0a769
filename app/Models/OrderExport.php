<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


/**
 * App\Models\OrderExport
 *
 * @property int $id
 * @property string $export_name
 * @property int $total_orders
 * @property string|null $status
 * @property string|null $download_url
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Database\Factories\OrderExportFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereDownloadUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereExportName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereTotalOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrderExport whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrderExport extends Model
{
    use HasFactory;


    /**
     * @var string
     *
     * table name
     */
    protected $table = 'order_export';

    /**
     * @var array
     */
    protected $fillable = [
        'export_name',
        'total_orders',
        'download_url',
    ];
}
