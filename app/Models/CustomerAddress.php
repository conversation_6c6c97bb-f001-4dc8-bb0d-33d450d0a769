<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\CustomerAddress
 *
 * @property string $id
 * @property int $user_id
 * @property string|null $name
 * @property string|null $phone
 * @property string|null $address
 * @property string|null $address_2
 * @property string|null $city
 * @property string|null $state
 * @property string|null $postcode
 * @property string|null $country
 * @property string $status This is status of user profile
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 * @method static \Database\Factories\CustomerAddressFactory factory(...$parameters)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress getChangesDetail()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress query()
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereAddress2($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereCity($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereCountry($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress wherePostcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|CustomerAddress whereUserId($value)
 * @mixin \Eloquent
 */
class CustomerAddress extends Model
{
    use HasFactory;

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'customer_address';
    protected $guarded = ['created_at', 'updated_at'];

    protected $hidden = ['created_at', 'updated_at', 'deleted_at', 'laravel_through_key'];

    public $incrementing = false;
    protected $keyType = 'string';

    protected static function booted(): void
    {
        static::creating(static function ($model) {
            $model->id = generateUUID();
        });
    }

    public function scopeGetChangesDetail(): string
    {
        return getChangesDetailForModel($this);
    }

    public function scopeCheckAddressExisted($query) {
        return $query->clone()->where(
            [
                'user_id' => $this->customer_id,
                'name' => $this->name,
                'phone' => $this->phone,
                'address' => $this->address,
                'address_2' => $this->address_2,
                'city' => $this->city,
                'state' => $this->state,
                'postcode' => $this->postcode,
                'country' => $this->country
            ]
        )->exists();
    }

    /*
    * Setters
    */
    public function setId ($value) {
        $this->id = $value;
    }
    public function setUserId ($value) {
        $this->user_id = $value;
    }
    public function setName ($value) {
        $this->name = $value;
    }
    public function setPhone ($value) {
        $this->phone = $value;
    }
    public function setAddress ($value) {
        $this->address = $value;
    }
    public function setAddress2 ($value) {
        $this->address_2 = $value;
    }
    public function setCity ($value) {
        $this->city = $value;
    }
    public function setState ($value) {
        $this->state = $value;
    }
    public function setPostcode ($value) {
        $this->postcode = $value;
    }
    public function setCountry ($value) {
        $this->country = $value;
    }

    /*
    * Getters
    */
    public function getId ($value) {
        return $this->id;
    }
    public function getCustomerId () {
        return $this->customer_id;
    }
    public function getName () {
        return $this->name;
    }
    public function getPhone () {
        return $this->phone;
    }
    public function getAddress () {
        return $this->address;
    }
    public function getAddress2 () {
        return $this->address_2;
    }
    public function getCity () {
        return $this->city;
    }
    public function getState () {
        return $this->state;
    }
    public function getPostcode () {
        return $this->postcode;
    }
    public function getCountry () {
        return $this->country;
    }
}
