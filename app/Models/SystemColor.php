<?php

namespace App\Models;

/**
 * App\Models\SystemColor
 *
 * @property int $id
 * @property string $name
 * @property string $hex_code
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor query()
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor whereHexCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SystemColor whereName($value)
 * @mixin \Eloquent
 */
class SystemColor extends Model
{

    /**
     * table name
     *
     * @var string
     */
    protected $table = 'system_color';
    protected $fillable = [
        'name',
        'hex_code',
        'is_heather',
        'multi_color_mode',
    ];
    public $timestamps = false;

    /**
     * Boot the model and set up event listeners
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set multi_color_mode based on hex_code format
        static::saving(function ($color) {
            $color->multi_color_mode = $color->isMultiColor();
        });
    }

    /**
     * Check if this color is multi-color based on hex_code format
     */
    public function isMultiColor(): bool
    {
        return !empty($this->hex_code) && str_contains($this->hex_code, '-');
    }

    /**
     * Get array of individual colors from hex_code
     */
    public function getColorsArray(): array
    {
        if (!$this->isMultiColor()) {
            return [$this->hex_code];
        }

        return array_filter(explode('-', $this->hex_code));
    }
}
