<?php

namespace App\Models;

use App\ActivityLogIndexConfigurator;
use App\ActivityLogRule;
use ScoutElastic\Searchable;

/**
 * App\Models\ActivityLog
 *
 * @property \ScoutElastic\Highlight|null $highlight
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ActivityLog query()
 * @mixin \Eloquent
 */
class ActivityLog extends Model
{
    use Searchable;

    /**
     * @var string
     */
    protected $indexConfigurator = ActivityLogIndexConfigurator::class;

    /**
     * @var array
     */
    protected $searchRules = [
        ActivityLogRule::class
    ];

    /**
     * @var array
     */
    protected $mapping = [
        'properties' => [
            'appId' => [
                'type' => 'text',
                'fields' => [
                    'raw' => [
                        'type' => 'keyword',
                    ]
                ]
            ],
            'sessionId' => [
                'type' => 'text',
                'fields' => [
                    'raw' => [
                        'type' => 'keyword',
                    ]
                ]
            ],
        ],
    ];
}
