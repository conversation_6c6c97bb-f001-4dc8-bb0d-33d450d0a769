<?php

namespace App\Models;

use App\Enums\SellerNotificationActionEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * App\Models\SellerNotification
 *
 * @property int $id
 * @property int|null $seller_id
 * @property string $admin_id
 * @property string $subject
 * @property string $message
 * @property string|null $image_link
 * @property string|null $image
 * @property int $is_warning
 * @property int $type
 * @property int $status
 * @property string $expiry_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Staff|null $admin
 * @property-read \App\Models\User|null $seller
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\SellerNotificationLog[] $logs
 * @property-read string $call_to_action_link
 * @property-read int $total_clicked
 * $property-read bool $is_new
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification query()
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereAdminId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereExpiryDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereIsWarning($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SellerNotification whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class SellerNotification extends Model
{
    use HasFactory;

    protected $table = 'seller_notification';

    protected $fillable = [
        'seller_id',
        'subject',
        'message',
        'expiry_date',
        'is_warning',
        'admin_id',
        'type',
        'image_link',
        'image'
    ];

    protected $appends = [
        'is_new'
    ];

    public function seller(): BelongsTo
    {
        return $this->belongsTo(User::class, 'seller_id', 'id')
            ->select('id', 'name', 'email')
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown'
            ]);
    }

    public function admin(): BelongsTo
    {
        return $this->belongsTo(Staff::class, 'admin_id', 'id')
            ->select(['id', 'name', 'email'])
            ->withDefault([
                'id' => '',
                'email' => '<EMAIL>',
                'name' => 'Unknown'
            ]);
    }

    public function logs(): HasMany
    {
        return $this->hasMany(SellerNotificationLog::class, 'notification_id', 'id');
    }

    public function getIsNewAttribute(): bool
    {
        if (count($this->logs) > 0) {
            return false;
        }
        return true;
    }

    public function getCallToActionLinkAttribute($value): ?string
    {
        if (empty($this->image_link)) {
            return null;
        }
        $baseUrl = 'https://seller.senprints.com';
        if (app()->environment() === 'development') {
            $baseUrl = 'https://seller-v2.dev.senprints.net';
        }
        return $baseUrl . '/api2/seller/cta/' . $this->id;
    }

    public function getTotalClickedAttribute(): int
    {
        return $this->logs->where('action', SellerNotificationActionEnum::CLICKED)->count();
    }
}
