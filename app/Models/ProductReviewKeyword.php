<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ProductReviewKeyword extends Model
{
    protected $table = 'product_review_keyword';
    protected $connection = 'mysql_sg';

    protected $fillable = [
        'keyword'
    ];

    public function productReviews()
    {
        return $this->belongsToMany(ProductReview::class, 'product_review_keyword_map', 'keyword_id', 'product_review_id', 'id', 'id');
    }
}
