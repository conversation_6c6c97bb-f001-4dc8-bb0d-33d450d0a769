<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\ProductPromotion
 *
 * @property int $id
 * @property int $product_id
 * @property string $start_time
 * @property string $end_time
 * @property float $point
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \App\Models\Product|null $product
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion wherePoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductPromotion withoutTrashed()
 * @mixin \Eloquent
 */
class ProductPromotion extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $table = 'product_promotion';

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'product_id',
        'start_time',
        'end_time',
        'point'
    ];

    public function product() :BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id', 'id');
    }
}
