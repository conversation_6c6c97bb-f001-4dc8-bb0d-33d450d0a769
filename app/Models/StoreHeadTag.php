<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;


class StoreHeadTag extends Model
{
    use HasFactory;

    protected $table = 'store_head_tag';

    protected $fillable = [
        'store_id',
        'seller_id',
        'name',
        'tag',
        'code',
        'enabled',
        'position',
        'priority',
        'additional_properties',
        'path',
    ];
}
