<?php

namespace App\Models;

use App\Enums\CacheKeys;
use App\Traits\HasCompositePrimaryKey;
use Awo<PERSON>z\Compoships\Compoships;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * App\Models\ProductFulfillMapping
 *
 * @property int $id
 * @property int $product_id
 * @property int $fulfill_product_id
 * @property int $supplier_id
 * @property-read \App\Models\Product $fulfill_product
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\ProductVariant[] $product_variants
 * @property-read int|null $product_variants_count
 * @property-read \App\Models\Supplier $supplier
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping query()
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping whereFulfillProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ProductFulfillMapping whereSupplierId($value)
 * @mixin \Eloquent
 */
class ProductFulfillMapping extends Model
{
    use HasCompositePrimaryKey;
    use HasFactory;
    use Compoships;

    protected $table = 'product_fulfill_mapping';
    protected $primaryKey = ['product_id', 'fulfill_product_id'];
    protected $keyType = 'array';

    protected $guarded = [
        'id',
    ];

    static private $cache = [];

    public $timestamps = false;

    // attribute for setter
    public array $variant;

    public function supplier(): BelongsTo
    {
        return $this->belongsTo(Supplier::class, 'supplier_id');
    }

    public function fulfill_product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'fulfill_product_id');
    }

    public function product_variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class, 'product_id', 'fulfill_product_id');
    }

    /**
     * @param $templateId
     * @param bool $useCache
     * @return Collection|mixed
     * @throws \Throwable
     */
    public static function findAndCacheByTemplateId($templateId, bool $useCache = true)
    {
        if ($useCache) {
            if (isset(self::$cache[$templateId])) {
                return self::$cache[$templateId];
            }

            $tag1 = CacheKeys::SYSTEM_PRODUCT_TEMPLATES;
            $tag2 = CacheKeys::getTemplateFulfillProduct($templateId);
            $tags = [$tag1, $tag2];
            $val = cacheAlt()->tags($tags)->remember(
                CacheKeys::getTemplateFulfillProduct($templateId),
                CacheKeys::CACHE_24H,
                fn() => self::findByTemplateId($templateId)->toJson()
            );
            if (is_string($val)) {
                $val = json_decode($val, false, 512, JSON_THROW_ON_ERROR);
            }
            return self::$cache[$templateId] = $val;
        }
        $val = self::findByTemplateId($templateId)->toJson();
        if (is_string($val)) {
            $val = json_decode($val, false, 512, JSON_THROW_ON_ERROR);
        }
        return $val;
    }

    /**
     * @param $templateId
     *
     * @return \Illuminate\Support\Collection
     */
    public static function findByTemplateId($templateId): \Illuminate\Support\Collection
    {
        return self::query()
            ->select([
                'product_fulfill_mapping.product_id',
                'product_fulfill_mapping.fulfill_product_id',
                'product_fulfill_mapping.supplier_id',
                'product_fulfill_mapping.assign_rate',
                'product_fulfill_mapping.max_items',
                'product_fulfill_mapping.holds_until',
            ])
            ->where('product_fulfill_mapping.product_id', $templateId)
            ->join('supplier', static fn($join) => $join->on('supplier.id', '=', 'product_fulfill_mapping.supplier_id')->where('supplier.status', 1))
            ->with('supplier:id,name,location,priority,additional_print_space')
            ->with([
                'product_variants' => fn ($q) => $q->select([
                    'product_id', 'base_cost', 'sku', 'variant_key', 'weight'
                ])->where('out_of_stock', 0)
            ])
            ->whereHas('fulfill_product', fn ($q) => $q->where('is_deleted', 0))
            ->orderBy('product_fulfill_mapping.id') // order by oldest
            ->get();
    }

    public function order_products(): HasMany
    {
        return $this->hasMany(OrderProduct::class, ['product_id', 'fulfill_product_id'], ['product_id', 'fulfill_product_id']);
    }

    /**
     * @return HasMany|OrderProduct
     */
    public function order_products_completed(): HasMany
    {
        return $this
            ->order_products()
            ->whereHas('order', function ($query) {
                $query->where('paid_at', '>=', now()->subHours(24));
            });
    }

    public static function getAndCacheHoldsById($templateId, $fulfillProductId)
    {
        $tags = CacheKeys::getProductTemplateTags($templateId);
        return cacheAlt()->tags($tags)->remember(
            CacheKeys::getProductFulfillMappingHoldCacheKey($templateId, $fulfillProductId),
            CacheKeys::CACHE_1H,
            function () use ($templateId, $fulfillProductId) {
                return ProductFulfillMapping::query()
                    ->select([
                        'id',
                        'holds_until',
                        'max_items',
                        'assign_rate',
                    ])
                    ->where('product_id', $templateId)
                    ->where('fulfill_product_id', $fulfillProductId)
                    ->first();
            }
        );
    }

    public static function fulfillItemsLast24Hours($templateId, $fulfillProductId)
    {
        return OrderProduct::where('template_id', $templateId)
            ->where('fulfill_product_id', $fulfillProductId)
            ->whereHas('order', function ($query) {
                $query->where('paid_at', '>=', now()->subHours(24));
            })
            ->sum('quantity');
    }

    public static function getRefreshCacheById($templateId): void
    {
        syncClearCache(['tags' => [CacheKeys::getTemplateFulfillProduct($templateId)]], CacheKeys::CACHE_TYPE_ALTERNATIVE);
    }
}
