<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * App\Models\Dev
 *
 * @property int $id
 * @property string $jira_id
 * @property string $name
 * @property string $avatar
 * @property int $commit_count
 * @property string $status
 * @property string|null $last_commit_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DevGitCommit> $commits
 * @property-read int|null $commits_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\DevTarget> $targets
 * @property-read int|null $targets_count
 * @method static \Illuminate\Database\Eloquent\Builder|Dev newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Dev newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Dev onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Dev query()
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereAvatar($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereCommitCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereJiraId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereLastCommitAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Dev withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Dev withoutTrashed()
 * @mixin \Eloquent
 */
class Dev extends Model
{
    use HasFactory;
    use SoftDeletes;

    /**
     * @var int|mixed|string
     */
    protected $table = 'dev';

    protected $fillable = [
        'jira_id',
        'name',
        'avatar',
        'status'
    ];

    public function targets(): HasMany
    {
        return $this->hasMany(DevTarget::class, 'dev_id', 'id');
    }

    public function commits(): HasMany
    {
        return $this->hasMany(DevGitCommit::class, 'jira_id', 'jira_id');
    }
}
