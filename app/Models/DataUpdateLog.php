<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * App\Models\DataUpdateLog
 *
 * @property int $id
 * @property string $action
 * @property string $data_type
 * @property string $data_id
 * @property string $data
 * @property string|null $detail
 * @property int $status
 * @property string $created_at
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog query()
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereAction($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereDataId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereDataType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DataUpdateLog whereStatus($value)
 * @mixin \Eloquent
 */
class DataUpdateLog extends Model
{
    use HasFactory;

    protected $table = 'data_update_logs';
    protected $fillable = [
        'id',
        'action',
        'data_type',
        'data_id',
        'detail',
        'status',
    ];

    public $timestamps = false;
}
