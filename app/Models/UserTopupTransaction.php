<?php

namespace App\Models;

use App\Enums\UserTopupTransactionStatus;
use App\Enums\UserTopupTransactionType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * App\Models\UserTopupTransaction
 *
 * @property string $id
 * @property int $seller_id
 * @property float $amount_usd currency USD
 * @property float $amount_vnd Convert amount to VND
 * @property string $code
 * @property string $type
 * @property string|null $detail Sms detail of transaction
 * @property string $status 'pending','hold','completed','cancel'
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User|null $user
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction query()
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereAmountUsd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereAmountVnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereDetail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereSellerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|UserTopupTransaction whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class UserTopupTransaction extends Model
{
    use HasFactory;

    public const RETRY_NUMBER = 100;

    public $fillable = [
        'seller_id',
        'amount_usd',
        'amount_vnd',
        'transaction_number',
        'code',
        'payment_email',
        'detail',
        'type',
        'status'
    ];

    public static function getTransactionGreaterThan24h()
    {
        return self::query()
            ->where('status', UserTopupTransactionStatus::PENDING)
            ->where('created_at', '<=', Carbon::now()->subDay())
            ->get([
                'id'
            ]);
    }

    public static function cancelTransaction(array $ids = []): ?int
    {
        return self::updateStatus($ids, UserTopupTransactionStatus::CANCEL);
    }

    public static function updateStatus(array $ids = [], $status = UserTopupTransactionStatus::PENDING): ?int
    {
        if (empty($ids)) {
            return null;
        }
        return self::query()
            ->whereIn('id', $ids)
            ->update([
                'status' => $status
            ]);
    }

    public static function getTransactionByEmailAndAmount($email, $amount, $transactionId = null, $type = UserTopupTransactionType::PINGPONG)
    {
        $transactionIdFound = false;
        if (!is_null($transactionId) && $type === UserTopupTransactionType::PINGPONG) {
            $transactionIdFound = self::pingPongCheckTransactionId($transactionId);
        }
        if ($transactionIdFound) {
            return null;
        }
        $queryBuild = self::query()
            ->where([
                'status' => UserTopupTransactionStatus::PENDING,
                'payment_email' => $email,
                'amount_usd' => $amount
            ]);
        if (!is_null($transactionId)) {
            $queryBuild->where(function ($query) use ($transactionId) {
                $query->where('detail->transactionId', '!=', $transactionId);
                $query->orWhereNull('detail->transactionId');
            });
        }
        if (str_contains($type, UserTopupTransactionType::PAYONEER)) {
            $queryBuild->whereIn('type', [UserTopupTransactionType::PAYONEER, UserTopupTransactionType::PAYONEER_VN]);
        } else {
            $queryBuild->where('type', $type);
        }
        return $queryBuild->orderByDesc('created_at')->first();
    }

    public static function pingPongCheckTransactionId($transactionId): bool
    {
        return self::query()
            ->where('type', UserTopupTransactionType::PINGPONG)
            ->whereIn('status', [UserTopupTransactionStatus::COMPLETED, UserTopupTransactionStatus::HOLD, UserTopupTransactionStatus::CANCEL])
            ->where('detail->transactionId', $transactionId)
            ->exists();
    }

    /**
     * @param $code
     * @param string $type
     * @param int $amount
     * @param string $status
     * @param bool $checkExist
     * @return UserTopupTransaction|null|bool
     */
    public static function getTransactionByCode($code, string $type = UserTopupTransactionType::BANK, $amount = 0, string $status = UserTopupTransactionStatus::PENDING, bool $checkExist = false)
    {
        if (!self::verifyCode($code)) {
            return null;
        }
        $queryWhere = [
            'code' => $code,
            'status' => $status
        ];
        if ($checkExist) {
            if ($type === UserTopupTransactionType::BANK) {
                $queryWhere['amount_vnd'] = $amount;
            } elseif (str_contains($type, UserTopupTransactionType::PAYONEER)) {
                $queryWhere['amount_usd'] = $amount;
            }
        }
        $query = self::query()->where($queryWhere);
        if (str_contains($type, UserTopupTransactionType::PAYONEER)) {
            $query->whereIn('type', [UserTopupTransactionType::PAYONEER, UserTopupTransactionType::PAYONEER_VN]);
        } else {
            $query->where('type', $type);
        }
        if ($checkExist) {
            return $query->where('created_at', '>=', Carbon::now()->subDay())->exists();
        }
        return $query->orderByDesc('created_at')->first();
    }

    public static function verifyCode($code): bool
    {
        $prefix = substr($code, 0, 3);
        $code = str_replace(self::getTransactionPrefix(), '', $code);
        return ($prefix === self::getTransactionPrefix() && strlen($code) === 6);
    }

    public static function getTransactionPrefix()
    {
        return config('senprints.topup_code_prefix');
    }

    /**
     * @param $code
     * @param string $type
     * @return UserTopupTransaction|null
     */
    public static function getTransactionBy($code, string $type = UserTopupTransactionType::BANK)
    {
        if (!self::verifyCode($code)) {
            return null;
        }
        $queryWhere = [
            'code' => $code,
        ];
        $query = self::query()->where($queryWhere);
        if (str_contains($type, UserTopupTransactionType::PAYONEER)) {
            $query->whereIn('type', [UserTopupTransactionType::PAYONEER, UserTopupTransactionType::PAYONEER_VN]);
        } else {
            $query->where('type', $type);
        }
        return $query->orderByDesc('created_at')->first();
    }

    public static function boot()
    {
        parent::boot();
        static::creating(function ($model) {
            if (empty($model->{$model->getKeyName()})) {
                $model->{$model->getKeyName()} = generateUUID();
            }
            $payCode = self::generatePayCode();
            $topupCode = self::checkTopupCode($payCode);
            if (!is_null($topupCode)) {
                $model->code = $topupCode;
            }
        });
    }

    /**
     * @param $str
     * @return string
     * @throws \Exception
     */
    public static function generatePayCode($str = null): string
    {
        if (is_null($str)) {
            $str = random_int(0, 999999);
        }
        return self::getTransactionPrefix() . str_pad($str, 6, '0', STR_PAD_LEFT);
    }

    /**
     * @param $topupCode
     * @param $retryNumber
     * @return string|null
     * @throws \Exception
     */
    public static function checkTopupCode($topupCode = null, $retryNumber = 0): ?string
    {
        if (is_null($topupCode) || $retryNumber >= self::RETRY_NUMBER) {
            return null;
        }
        $topupTransaction = self::query()->orderByDesc('created_at')->firstWhere([
            'code' => $topupCode,
            'status' => UserTopupTransactionStatus::PENDING
        ]);
        if (is_null($topupTransaction)) {
            return $topupCode;
        }
        // random a new topup code
        return self::checkTopupCode(self::generatePayCode(), $retryNumber + 1);
    }

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'seller_id', 'id');
    }

    /**
     * Get the value indicating whether the IDs are incrementing.
     *
     * @return bool
     */
    public function getIncrementing()
    {
        return false;
    }

    /**
     * Get the auto-incrementing key type.
     *
     * @return string
     */
    public function getKeyType()
    {
        return 'string';
    }

    public function topupUnmatchTransaction()
    {
        return $this->hasOne(TopupUnmatchTransaction::class, 'topup_id', 'id');
    }
}
