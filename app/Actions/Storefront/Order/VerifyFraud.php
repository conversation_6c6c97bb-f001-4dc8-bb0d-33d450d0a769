<?php

namespace App\Actions\Storefront\Order;

use App\Enums\OrderTypeEnum;
use App\Models\Order;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Modules\OrderService\Models\RegionOrders;

class VerifyFraud
{
    const IP_FRAUD = 'ip_fraud';

    const DEVICE_FRAUD = 'device_fraud';

    const FLAG_CHECKOUT = 'flag_checkout';

    const FLAG_PAYMENT_RATE = 'flag_payment_rate';

    const CAPTCHA = 'captcha';

    /**
     * @param $sellerId
     * @param string|null $deviceId
     * @param string|null $deviceIp
     * @param string|null $deviceDetail
     * @param bool $isRegion
     * @return string|null
     */
    public function handle($sellerId, ?string $deviceId = '', ?string $deviceIp = '', ?string $deviceDetail = '', bool $isRegion = false): ?string
    {
        if (!$deviceId && !$deviceIp) {
            return null;
        }

        if ($deviceIp && $this->isReqIpFraud($deviceIp)) {
            return self::IP_FRAUD;
        }

        if ($deviceDetail && $this->isReqDeviceDetailFraud($deviceDetail)) {
            return self::DEVICE_FRAUD;
        }

        $numOrderByDevice = $this->countOrderCreatedByDevice($deviceId, $deviceIp, $isRegion);

        // Khi số lượng đơn hàng được tạo theo thiết bị lơn hơn 5 trong một giờ sẽ gắn cờ đơn hàng
        // để xử lý thủ công
        if ($numOrderByDevice > 5) {
            return self::FLAG_CHECKOUT;
        }

        if ($numOrderByDevice > 2) {
            // Khi số lượng đơn trong một giờ từ 3-5 thì check tỉ lệ đơn hàng đã thanh toán so với tổng số đơn trong 24h.
            $orderStats = $this->countPaidAndTotalOrderIn24Hours($sellerId, $isRegion);
            $paidOrders = data_get($orderStats, 'paid_orders') ?: 1;
            $totalOrders = data_get($orderStats, 'total_orders');
            $totalPerPaid = $totalOrders / $paidOrders;

            //  Nếu tỉ lệ này lớn hơn 100 thì cần xác thực captcha
            if ($totalPerPaid > 100) {
                return self::CAPTCHA;
            }

            //  Nếu tỉ lệ này lớn hơn 10 thì gắn cờ đơn hàng để xử lý thủ công.
            if ($totalPerPaid > 10) {
                return self::FLAG_PAYMENT_RATE;
            }
        }

        return null;
    }

    /**
     * @param string $ip
     *
     * @return bool
     */
    private function isReqIpFraud(string $ip): bool
    {
        return in_array($ip, $this->fraudIps(), true);
    }

    /**
     * @param string|null $deviceDetail
     *
     * @return bool
     */
    private function isReqDeviceDetailFraud(?string $deviceDetail): bool
    {
        return in_array($deviceDetail, $this->fraudDeviceDetails(), true);
    }

    /**
     * @param string|null $deviceId
     * @param string|null $ipAddress
     * @param bool $isRegion
     * @return int
     */
    private function countOrderCreatedByDevice(?string $deviceId, ?string $ipAddress, bool $isRegion = false): int
    {
        if ($isRegion) {
            return RegionOrders::onRegion(config('app.region'))
                ->where(function ($q) use ($deviceId, $ipAddress) {
                    if ($deviceId) {
                        $q->where('device_id', $deviceId);
                    }
                    if ($ipAddress && ! in_array($ipAddress, $this->internalIps())) {
                        $q->orWhere('ip_address', $ipAddress);
                    }
                })
                ->where('created_at', '>', now()->subMinutes(60))
                ->count();
        }
        return Order::query()
            ->where(function ($q) use ($deviceId, $ipAddress) {
                if ($deviceId) {
                    $q->where('device_id', $deviceId);
                }

                if ($ipAddress && ! in_array($ipAddress, $this->internalIps())) {
                    $q->orWhere('ip_address', $ipAddress);
                }
            })
            ->where('created_at', '>', now()->subMinutes(60))
            ->count();
    }

    /**
     * @param $sellerId
     * @param bool $isRegion
     * @return Order|Builder|Model|RegionOrders|object|null
     */
    private function countPaidAndTotalOrderIn24Hours($sellerId, bool $isRegion = false)
    {
        if ($isRegion) {
            return RegionOrders::onRegion(config('app.region'))
                ->selectRaw('count(CASE WHEN payment_status = "paid" THEN 1 END) as paid_orders')
                ->selectRaw('count(*) as total_orders')
                ->where('seller_id', $sellerId)
                ->where('created_at', '>', now()->subHours(24))
                ->where('type', '<>', OrderTypeEnum::SERVICE)
                ->first();
        }
        return Order::query()
            ->selectRaw('count(CASE WHEN payment_status = "paid" THEN 1 END) as paid_orders')
            ->selectRaw('count(*) as total_orders')
            ->where('seller_id', $sellerId)
            ->where('created_at', '>', now()->subHours(24))
            ->where('type', '<>', OrderTypeEnum::SERVICE)
            ->first();
    }

    /**
     * @return string[]
     */
    private function internalIps(): array
    {
        return config('senprints.internal_ips') ?: ['**************', '***************', '*************'];
    }

    /**
     * @return array
     */
    private function fraudIps(): array
    {
        return [];
    }

    /**
     * @return string[]
     */
    private function fraudDeviceDetails(): array
    {
        return ['windows/chrome headless'];
    }
}
