<?php

namespace App\Actions\Commons;

use App\Enums\SmartRemarketingEnum;
use App\Enums\UpsellTypeEnum;
use App\Exceptions\CurrencyNotFoundException;
use App\Exceptions\StoreNotFoundException;
use App\Http\Controllers\Storefront\UpsellController;
use App\Models\Currency;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Services\StoreService;
use App\Traits\GetStoreDomain;
use Exception;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrders;
class SendEmailConfirmToBuyer
{
    use GetStoreDomain;

    /**
     * @param Order|RegionOrders $order
     *
     * @return bool
     * @throws CurrencyNotFoundException
     * @throws StoreNotFoundException
     * @throws Exception
     */
    public function handle(Order|RegionOrders $order): bool
    {
        if (!$store = StoreService::getStoreInfo($order->store_id)) {
            throw new StoreNotFoundException("Store not found");
        }

        $currency = Currency::firstByCode($order->currency_code);
        if (!$currency) {
            throw new CurrencyNotFoundException("Currency not found");
        }
        $currency->rate = $order->currency_rate ?? $currency->rate;
        $email = $order->customer_email;
        $name = $order->customer_name;
        $baseUrl = "https://{$order->store_domain}";
        $order->loadMissing('products');
        $sellers = User::query()
            ->selectRaw('id,name,status,custom_payment,tags,sharding_status,db_connection')
            ->whereIn('id', $order->products->pluck('seller_id')->unique()->values()->toArray())
            ->get();
        $order->products->map(function ($orderProduct) use ($sellers) {
            $seller = $sellers->firstWhere('id', $orderProduct->seller_id);
            $product = Product::query()->onSellerConnection($seller)->find($orderProduct->product_id);
            $orderProduct->setRelation('product', $product);
            return $orderProduct;
        });
        $products = $order->products;
        foreach ($products as $product) {
            if (!is_object($product)) {
                continue;
            }
            $url = $product->product_url;
            if (preg_match('#http(s)?://#', $url)) {
                continue;
            }
            $url = str_replace('/campaign', '', $url);
            $product->product_url = $baseUrl . $url;
            if ($product->product && Str::isJson($product->product->options) && Str::isJson($product->options)) {
                $productOptions = json_decode($product->product->options, true, 512, JSON_THROW_ON_ERROR);
                $options = json_decode($product->options, true, 512, JSON_THROW_ON_ERROR);
                if (isset($options['color'], $productOptions['color']) && count($productOptions['color']) === 1 && data_get($productOptions, 'color.0') === 'white') {
                    unset($options['color']);
                }
                if (isset($options['size'], $productOptions['size']) && count($productOptions['size']) === 1) {
                    unset($options['size']);
                }
                $product->options = json_encode($options, JSON_THROW_ON_ERROR);
            }
        }

        $seller = User::find($order->seller_id);

        if ($seller && $seller->smart_remarketing) {
            if ($seller->smart_remarketing === SmartRemarketingEnum::ENABLED_WITH_UPSELL_MP) {
                $relatedProducts = (new UpsellController($order->store_id))->getSmartUpsell($order->id, 'order_email');
            } else {
                $relatedProducts = (new UpsellController($order->store_id))->getUpsellProductsForEmail($products->pluck('product_id')->toArray(), UpsellTypeEnum::POST_SALE);
            }
            $relatedProducts = $relatedProducts ? array_slice($relatedProducts, 0, 4) : [];
        } else {
            $productIds = $products->pluck('product_id');
            // Take 4 products from related products
            $upsellController = new UpsellController($order->store_id, 4);
            // Get related products
            $relatedProducts = $upsellController->getUpsellProductsForEmail($productIds->toArray()) ?? [];
        }

        foreach ($relatedProducts as &$product) {
            $product['slug'] = $baseUrl . "/" . $product['slug'];
            $product['campaign_name'] ??= $product['name'];
        }

        $estimatedDeliveryDates = $order->getEstimateDeliveryDates();
        $printDate = $estimatedDeliveryDates['print_date']->format('M d');
        $fromDate = $estimatedDeliveryDates['from_date']->format('M d');
        $toDate = $estimatedDeliveryDates['to_date']->format('M d');
        $estimatedMessage = "Printed before {$printDate}. Estimated delivery from {$fromDate} to {$toDate}.";

        $dataSendMailLog = [
            'sellerId' => $order->seller_id ?? null,
            'storeId'  => $order->store_id ?? null,
            'orderId'  => $order->id ?? null,
        ];
        $firstCampaignSlug = $products->first()->product_url;

        $config = [
            'to'          => $email,
            'template'    => 'buyer.order_confirmation',
            'data'        => [
                'subject'          => 'Order Confirmation #' . $order->order_number,
                'name'             => $name,
                'email'            => $email,
                'order'            => [
                    'id'                    => $order->id,
                    'products'              => $products,
                    'address'               => $order->shippingAddress,
                    'total_product_amount'  => $order->total_product_amount,
                    'tip_amount'            => $order->tip_amount,
                    'total_shipping_amount' => $order->total_shipping_amount,
                    'insurance_fee'         => $order->insurance_fee,
                    'total_discount'        => $order->total_discount,
                    'discount_code'         => $order->discount_code,
                    'payment_discount'      => $order->payment_discount,
                    'total_amount'          => $order->total_amount,
                    'total_tax_amount'      => $order->total_tax_amount,
                    'shipping_method'       => getShippingMethodName($order->shipping_method),
                    'status_url'            => $order->status_url,
                    'order_number'          => $order->order_number,
                    'payment_method'        => getPaymentMethodName($order->payment_method),
                    'payment_gateway_id'    => $order->payment_gateway_id,
                    'estimated_delivery'    => $estimatedMessage,
                    'customer'              => [
                        'name'     => $order->customer_name,
                        'address'  => $order->address,
                        'city'     => $order->city,
                        'postcode' => $order->postcode,
                        'state'    => $order->state,
                        'country'  => $order->country,
                        'phone'    => $order->customer_phone,
                        'house_number' => $order->house_number,
                        'mailbox_number' => $order->mailbox_number,
                    ],
                    'is_notify_china_delivery_late' => $order->isNotifyChinaDeliveryLate(),
                ],
                'related_products' => $relatedProducts,
                'store_info'       => $store,
                'store_url'        => $this->storeUrl($order->store_id),
                'currency'         => $currency,
                'first_campaign'   => $firstCampaignSlug,
            ],
            'sendMailLog' => $dataSendMailLog
        ];

        return sendEmail($config);
    }

    /**
     * @param int $storeId
     * @return string
     */
    private function storeUrl(int $storeId): string
    {
        return 'https://' . self::getDomainByStoreId($storeId);
    }
}
