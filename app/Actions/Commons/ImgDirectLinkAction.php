<?php

namespace App\Actions\Commons;

use App\Services\Link;

class ImgDirectLinkAction
{
    private static ImgDirectLinkAction $instance;

    /**
     * @return self
     */
    public static function singleton(): ImgDirectLinkAction
    {
        return self::$instance ??= new self();
    }

    /**
     * @param string $relativeLink
     *
     * @return string
     */
    public function handle(string $relativeLink): string
    {
        if (str_contains($relativeLink, 'drive.google.com') || str_contains($relativeLink, 'dropbox.com')) {
            return (new Link($relativeLink))->toDirectLink();
        }

        return $relativeLink;
    }
}
