<?php

namespace App\Actions\Commons;

use App\Enums\CacheKeys;
use App\Enums\ColorSpaceEnum;
use App\Enums\DesignTypeEnum;
use App\Enums\FileRenderType;
use App\Enums\FileTypeEnum;
use App\Enums\OrderTypeEnum;
use App\Enums\PersonalizedType;
use App\Enums\PrintSpaceEnum;
use App\Enums\ProductCategoryEnum;
use App\Enums\ProductPrintType;
use App\Enums\ProductType;
use App\Enums\SupplierEnum;
use App\Models\Campaign;
use App\Models\Design;
use App\Models\File;
use App\Models\ProductDesignMapping;
use App\Models\Template;
use App\Services\FulfillmentService;
class GetFilesAndMockupsOfOrderProducts
{
    private $orderProducts;
    private $fullPath;
    private $isSupplier;
    private $isSeller;


    public function __construct($orderProducts, bool $fullPath = false, bool $isSupplier = false, $isSeller = false)
    {
        $this->orderProducts = $orderProducts;
        $this->fullPath = $fullPath;
        $this->isSupplier = $isSupplier;
        $this->isSeller = $isSeller;
    }

    public function handle(): array
    {
        if (empty($this->orderProducts)) {
            return [];
        }
        $listOrderProductIds  = $this->orderProducts->pluck('id')->unique()->toArray();
        $listOrderIds = $this->orderProducts->pluck('order_id')->unique()->toArray();
        $listCampaignIds = $this->orderProducts->pluck('campaign_id')->unique()->toArray();
        $listProductIds = $this->orderProducts->pluck('product_id')->unique()->toArray();
        $designs = Design::query()
            ->select(['id', 'file_url', 'type', 'print_space', 'design_json', 'order_id', 'order_product_id', 'product_id', 'seller_id'])
            ->whereIn('order_product_id', $listOrderProductIds)
            ->whereIn('order_id', $listOrderIds)
            ->where('type', DesignTypeEnum::PRINT)
            ->orderBy('print_space')
            ->get();

        $orders = collect();
        $sellers = collect();
        foreach ($this->orderProducts as $orderProduct) {
            if ($orders->where('id', $orderProduct->order_id)->isEmpty()) {
                $orders->push($orderProduct->order);
            }

            if ($sellers->where('id', $orderProduct->seller_id)->isEmpty()) {
                $sellers->push($orderProduct->seller);
            }
        }
        $files = $this->getFilesOfOrderProducts($listOrderProductIds, $listProductIds, $sellers);
        $campaigns = $this->getCampaignsOfOrderProducts($listCampaignIds, $sellers);
        $expressCampDesigns = $this->mapExpressCampaignDesigns($campaigns, $sellers);
        $files = $this->mapFilesWithExpressCampaignDesigns($files, $designs, $expressCampDesigns);
        $designMappings = collect();
        if ($this->fullPath || $this->isSupplier) {
            $designMappings = $this->mapProductDesignsWithSupplier($this->orderProducts);
        }
        $mugsProductId = Template::getAndCacheProductIdByCategory(ProductCategoryEnum::MUGS);
        $files = $this->mapFilesWithMugs($mugsProductId, $this->orderProducts, $files);
        $designOPMappings = [];
        $orderProductsFullprinted = [];
        foreach ($this->orderProducts as $orderProduct) {
            $designOPMapping = $designMappings
                ->where(function ($q) use ($orderProduct) {
                    $q->orWhere('product_id', $orderProduct->template_id);
                    $q->orWhere('product_id', 0);
                })
                ->where('supplier_id', $orderProduct->supplier_id)
                ->sortByDesc('product_id')
                ->sortByDesc('print_space');

            $designOPMappings[$orderProduct->id] = $designOPMapping;
            $orderProductsFullprinted[$orderProduct->id] = $orderProduct->full_printed;
        }
        $mockups = $this->getMockupsWithSellers($sellers, $this->orderProducts);
        $printSpacesTemplates = collect();
        if (!$this->fullPath) {
            $printSpacesTemplates = $this->getPrintSpacesTemplate($this->orderProducts);
        }
        $orderDesignsAndMockups = [];
        $files = $files->unique('id');
        $mockups = $mockups->unique('id');

        foreach ($this->orderProducts as $id => $orderProduct) {
            $isDesignBySenPrints = $orderProduct->isDesignBySenPrints();
            $sellerTags = $orderProduct->seller?->tags;
            $productId = $orderProduct->product_id;
            $orderProductId = $orderProduct->id;
            $options = json_decode($orderProduct->options);
            $color = correctOptionValue(optional($options)->color);
            $size = correctOptionValue(optional($options)->size);
            $mockupsWithOP = $mockups
                ->where(function ($q) use ($orderProduct, $productId) {
                    return $q->orWhere('order_product_id', $orderProduct->id)->when(!is_null($productId), function ($q) use ($productId) {
                        return $q->orWhere('product_id', $productId);
                    });
                })
                ->where('seller_id', $orderProduct->seller_id)
                ->where(function ($q) use ($color) {
                    return $q->where('option', $color)->orWhereNull('option');
                })
                ->groupBy('print_space')
                ->sortBy('print_space')
                ->sortBy('position')
                ->flatten()
                ->where(function ($file) use ($orderProduct) {
                return $file->product_id == $orderProduct->product_id || $file->order_product_id == $orderProduct->id;
            });
            $printSpacesTemplate = $printSpacesTemplates->filter(
                fn($printSpaceTemplate) =>
                $printSpaceTemplate['template_id'] === $orderProduct->template_id
            );
            $filesWithOP = $files
                ->where('seller_id', $orderProduct->seller_id)->where(function ($file) use ($orderProduct) {
                    return $file->product_id == $orderProduct->product_id || $file->order_product_id == $orderProduct->id;
                })
                ->when($this->isSeller && $isDesignBySenPrints, function ($q) use ($orderProductId, $productId) {
                    if (!is_null($orderProductId)) {
                        $q->orWhere('order_product_id', '!=', $orderProductId);
                    }
                    if (!is_null($productId)) {
                        $q->orWhere('product_id', '!=', $productId);
                    }
                })
                ->sortBy('print_space');
            if (!empty($size) && $orderProduct->isFullPrintedType()) {
                $filesWithOP = $filesWithOP->where(function($q) use ($size, $orderProduct) {
                    return str_contains($size, $q->print_space) || $q->print_space === 'default';
                });
            }
            foreach ($filesWithOP as $index => $file) {
                $fullPrinted = data_get($orderProductsFullprinted, $file->order_product_id);
                $designOPMapping = data_get($designOPMappings, $file->order_product_id ?? 0, collect());
                $file->type = FileTypeEnum::DESIGN;
                $file->table = ($file instanceof Design) ? 'design' : 'file';
                if ($orderProduct && FulfillmentService::shouldFallbackToFileUrl2($file, $orderProduct)) {
                    $file->file_url_2 = $file->file_url;
                }
                $orderProductSeller = $orderProduct->seller_id ?? null;
                try {
                    if (($this->fullPath || $this->isSupplier) && isset($designOPMapping)) {
                        $designMapping = $designOPMapping->first(fn($designMapping) => $designMapping->print_space === $file->print_space);
                        if (!is_null($designMapping) && $fullPrinted === ProductPrintType::PRINT_2D) {
                            if ($orderProductSeller === SupplierEnum::GOOTEN && !empty($mugsProductId) && in_array((int)$orderProduct->template_id, $mugsProductId, true)) {
                                $designMapping->color_space = ColorSpaceEnum::RGB;
                            }
                            if ($orderProductSeller === SupplierEnum::QTCO) {
                                $designMapping->dpi = 200;
                                $designMapping->noxmp = true;
                            }
                            $file->design_url = scaleDesignUrl($designMapping, FulfillmentService::cleanUrlPath($file->design_to_print_url));
                            $file->is_design_mapping = true;
                        } else if ($orderProductSeller === SupplierEnum::MWW) {
                            $file->design_url = isset($file->file_url_2) ? jpgUrl($file->design_to_print_url) : null;
                        } else {
                            $file->design_url = isset($file->file_url_2) ? s3Url($file->design_to_print_url) : null;
                        }
                    } else {
                        $file->design_url = isset($file->file_url_2) ? $file->design_to_print_url : null;
                    }
                } catch (\Throwable $e) {
                    if ($this->fullPath || $this->isSupplier) {
                        throw $e;
                    }
                }

                if (($file->table === 'design' && $file->order_product_id !== $orderProduct->id)) {
                    continue;
                }
                if (isset($sellerTags) && !str_contains($sellerTags, 'sleeve printspace') && in_array($file?->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                    unset($files[$index]);
                    continue;
                }

                $mockup = null;
                foreach ($mockupsWithOP as $idx => $each) {
                    if ($each->print_space === $file->print_space) {
                        $mockup = $each;
                        unset($mockupsWithOP[$idx]);
                        break;
                    }
                }
                if (!empty($orderProduct->custom_print_space)) {
                    $file->print_space = $orderProduct->custom_print_space;
                }
                // get print space info to validate design size
                if (!$this->fullPath) {
                    $printSpace = !$printSpacesTemplate->isEmpty() ? $printSpacesTemplate->first() : null;
                    if (isset($printSpacesTemplate)) {
                        foreach ($printSpacesTemplate as $each) {
                            if ($each['name'] === $file->print_space) {
                                $printSpace = $each;
                                break;
                            }
                        }
                    }
                    $file->printSpace = adjustPrintSpace($printSpace);
                }

                if (is_null($mockup) || ($orderProduct->personalized && $file->table === 'design') || !empty($orderProduct->custom_print_space)) {
                    if (!is_null($mockup) && $orderProduct->personalized && $orderProduct->full_printed === ProductPrintType::EMBROIDERY) {
                        $mockupUrl = $mockup->file_url;
                        if (!$mockupUrl) {
                            $mockupUrl = $mockups->where('seller_id', $orderProduct->seller_id)
                                ->where('print_space', $file->print_space)
                                ->where('type_detail', 'custom')
                                ->where(function ($q) use ($color) {
                                    return $q->where('option', $color)->orWhereNull('option');
                                })
                                ->value('file_url');
                            if (!$mockupUrl) {
                                $mockupUrl = $mockups->where('seller_id', $orderProduct->seller_id)
                                    ->where('type_detail', 'custom')
                                    ->where(function ($q) use ($color) {
                                        return $q->where('option', $color)->orWhereNull('option');
                                    })
                                    ->value('file_url');
                            }
                        }
                    } else {
                        $mockupUrl = $orderProduct->thumb_url;
                        if (empty($mockupUrl)) {
                            $mockupUrl = $file->design_to_print_url;
                        }
                    }
                } else {
                    $mockupUrl = imgColorUrl($mockup->mockup_to_print_url, $color);
                }
                $file->mockup_url = ($this->fullPath || $this->isSupplier) ? imgUrl($mockupUrl) : $mockupUrl;
                $file->has_design_json = false;
                if (!empty($file->design_json)) {
                    $file->has_design_json = true;
                }
                unset($file->design_json);
                if (empty($orderDesignsAndMockups[$orderProduct->id]['files'])) {
                    $orderDesignsAndMockups[$orderProduct->id]['files'] = collect();
                }
                $orderDesignsAndMockups[$orderProduct->id]['files']->push($file);
            }
            foreach ($mockupsWithOP as $index => $mockupWithOP) {
                if (isset($sellerTags) && !str_contains($sellerTags, 'sleeve printspace') && in_array($mockupWithOP?->print_space, PrintSpaceEnum::additionalPrintSpaces(), true)) {
                    unset($mockupsWithOP[$index]);
                    continue;
                }
                $mockupUrl = imgColorUrl($mockupWithOP->mockup_to_print_url, $color);
                $mockupWithOP->mockup_url = ($this->fullPath || $this->isSupplier) ? imgUrl($mockupUrl) : $mockupUrl;

                if (empty($orderDesignsAndMockups[$orderProduct->id]['mockups'])) {
                    $orderDesignsAndMockups[$orderProduct->id]['mockups'] = collect();
                }
                $orderDesignsAndMockups[$orderProduct->id]['mockups']->push($mockupWithOP);
            }
        }

        return $orderDesignsAndMockups;
    }


    public function getFilesOfOrderProducts($orderProductIds, $productIds, $sellers)
    {
        $files = collect();
        foreach ($sellers as $seller) {
            $sellerFiles = File::query()
                ->onSellerConnection($seller)
                ->select(['id', 'file_url', 'file_url_2', 'type', 'print_space', 'design_json', 'seller_id', 'order_product_id', 'product_id', 'order_id'])
                ->where(function ($q) use ($orderProductIds, $productIds) {
                    $q->orWhereIn('order_product_id', $orderProductIds);
                    if (!empty($productIds)) {
                        $q->orWhereIn('product_id', $productIds);
                    }
                })
                ->where('type', FileTypeEnum::DESIGN)
                ->where('option', FileRenderType::PRINT)
                ->orderBy('print_space')
                ->get();
            $files = $files->merge($sellerFiles);
        }
        return $files;
    }

    public function getCampaignsOfOrderProducts($listCampaignIds, $sellers)
    {
        $campaigns = collect();
        foreach ($sellers as $seller) {
            $campaign = Campaign::query()->onSellerConnection($seller)->whereIn('id', $listCampaignIds)->get();
            $campaigns = $campaigns->merge($campaign);
        }

        return $campaigns;
    }

    public function mapExpressCampaignDesigns($campaigns, $sellers)
    {
        $listCampaignExpressIds = $campaigns->where('product_type', ProductType::CAMPAIGN_EXPRESS)
            ->pluck('id')
            ->toArray();
        if (empty($listCampaignExpressIds)) {
            return collect([]);
        }
        $expressCampDesigns = collect();
        foreach ($sellers as $seller) {
            $campaign = File::query()->onSellerConnection($seller)
                ->select(['id', 'file_url', 'file_url_2', 'type', 'print_space'])
                ->whereIn('campaign_id', $listCampaignExpressIds)
                ->where(['option' => FileRenderType::PRINT, 'type' => FileTypeEnum::DESIGN])
                ->get();
            $expressCampDesigns = $expressCampDesigns->merge($campaign);
        }

        return $expressCampDesigns;
    }

    public function mapFilesWithExpressCampaignDesigns($files, $designs, $expressCampDesigns)
    {
        $productIds = $designs->pluck('product_id')->unique()->toArray();
        $newFiles = $files->whereNotIn('product_id', $productIds);
        foreach ($productIds as $productId) {
            $filesWithProduct = $files->where('product_id', $productId);
            if ($filesWithProduct->isNotEmpty()) {
                $filesWithProduct = $filesWithProduct->filter(fn($file) => is_null($designs->where('product_id', $productId)->firstWhere('print_space', $file->print_space)));
            }

            $filesWithProduct = $designs->where('product_id', $productId)->concat($filesWithProduct);
            $newFiles = $newFiles->merge($filesWithProduct);
        }
        if (!empty($expressCampDesigns)) {
            $newFiles = $newFiles->merge($expressCampDesigns);
        }

        return $newFiles;
    }

    public function mapProductDesignsWithSupplier($orderProducts)
    {
        $templateOrderProductIds = $orderProducts->pluck('template_id')->unique()->toArray();
        $supplierIds = $orderProducts->pluck('supplier_id')->unique()->toArray();
        $designMappings = ProductDesignMapping::query()
            ->where(function ($q) use ($templateOrderProductIds) {
                $q->orWhereIn('product_id', $templateOrderProductIds);
                $q->orWhere('product_id', 0);
            })
            ->whereIn('supplier_id', $supplierIds)
            ->orderByDesc('product_id')
            ->orderByDesc('print_space')
            ->get();
        return $designMappings;
    }

    public function mapFilesWithMugs($mugsProductId, $orderProducts, $files)
    {
        $filesMapped = collect();
        foreach ($orderProducts as $orderProduct) {
            if (!empty($mugsProductId) && !empty($orderProduct->options) && $orderProduct->personalized === PersonalizedType::CUSTOM_OPTION && in_array((int)$orderProduct->template_id, $mugsProductId, true)) {
                $productOptions = json_decode($orderProduct->options, true, 512, JSON_THROW_ON_ERROR);
                if (isset($productOptions['size'])) {
                    $filesMapped = $filesMapped->merge($files->filter(fn($file) => $file->print_space === $productOptions['size'])->values());
                }
            }
        }
        if ($filesMapped->isEmpty()) {
            return $files;
        }
        return $filesMapped;
    }

    public function getMockupsWithSellers($sellers, $orderProducts, $color = null)
    {
        $mockupsResponse = collect();
        foreach ($sellers as $seller) {
            $orderProductsOfSeller = $orderProducts->where('seller_id', $seller->id);
            $orderProductIds = $orderProductsOfSeller->pluck('id')->unique()->toArray();
            $productIds = $orderProductsOfSeller->pluck('product_id')->unique()->toArray();
            $mockups = File::query()
                ->onSellerConnection($seller)
                ->select(['id', 'file_url', 'file_url_2', 'print_space', 'seller_id', 'order_product_id', 'product_id', 'order_id', 'type'])
                ->where(function ($q) use ($orderProductIds, $productIds) {
                    return $q->orWhereIn('order_product_id', $orderProductIds)
                        ->when(!empty($productIds), function ($q) use ($productIds) {
                            return $q->orWhereIn('product_id', $productIds);
                        });
                })
                ->where('type', FileTypeEnum::IMAGE)
                ->when($color, fn($q) => $q->where('option', $color))
                ->get();

            $mockupsResponse = $mockupsResponse->merge($mockups);
        }

        return $mockupsResponse;
    }

    public function getPrintSpacesTemplate($orderProducts)
    {
        $templateIds = $orderProducts->pluck('template_id')->unique()->toArray();
        $printSpacesTemplates = collect();
        foreach ($templateIds as $templateId) {
            $printSpace = cacheAlt()->remember(CacheKeys::getTemplateProductByTemplateId($templateId), CacheKeys::CACHE_30D, function () use ($templateId) {
                return Template::query()->whereKey($templateId)->first();
            });
            $printSpacesTemplate = json_decode($printSpace->print_spaces ?? '[]', true, 512, JSON_THROW_ON_ERROR);
            $printSpacesTemplate = array_map(function ($q) use ($templateId) {
                $q['template_id'] = $templateId;
                return $q;
            }, $printSpacesTemplate);
            $printSpacesTemplates = $printSpacesTemplates->merge($printSpacesTemplate);
        }

        return $printSpacesTemplates;
    }
}
