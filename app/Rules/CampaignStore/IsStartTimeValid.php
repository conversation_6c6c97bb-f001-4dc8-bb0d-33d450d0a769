<?php

namespace App\Rules\CampaignStore;

use Illuminate\Contracts\Validation\Rule;

class IsStartTimeValid implements Rule
{
    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param string $attribute
     * @param mixed $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $currentTime = time(); // Get current time.
        $startTime = strtotime($value); // Get start time.

        // If start time < current then return false.
        return $startTime >= $currentTime;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The Start Time field is invalid.';
    }
}
