<?php

namespace App\Rules\Seller;

use App\Models\PromotionRule as Promotion;
use Illuminate\Contracts\Validation\Rule;

class PromotionRule implements Rule
{
    private $storeId;

    public function __construct($storeId)
    {
        $this->storeId = $storeId;
    }

    public function passes($attribute, $value): bool
    {
        return Promotion::query()
            ->where('discount_code', $value)
            ->checkThankYouCode($this->storeId)
            ->exists();
    }

    public function message(): string
    {
        return 'Invalid discount code';
    }
}
