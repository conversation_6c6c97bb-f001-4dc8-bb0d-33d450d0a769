<?php

namespace App\Rules\Analytic;

use Illuminate\Contracts\Validation\Rule;

class DateRangeRule implements Rule
{
    protected $max_days; //config
    protected $start_date;

    /**
     * Create a new rule instance.
     *
     * @return void
     */
    public function __construct($startDate, $maxDays = 365)
    {
        $this->start_date = $startDate;
        $this->max_days   = $maxDays;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $datetimeStartDate = date_create($this->start_date);
        $datetimeEndDate   = date_create($value);
        $interval          = date_diff($datetimeStartDate, $datetimeEndDate);

        return $interval->days <= $this->max_days;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'Date range not above: ' . $this->max_days . ' days';
    }
}
