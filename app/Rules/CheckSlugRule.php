<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;

class CheckSlugRule implements Rule
{
    private string $tableName;
    private array $arrFilter;
    private array $arrExcept;
    private string $connection;


    /**
     * CountSlugRule constructor.
     *
     * @param string $tableName
     * @param array $arrFilter
     * @param array $arrExcept
     * @param string $connection
     */
    public function __construct(string $tableName, array $arrFilter = [], array $arrExcept = [], string $connection = 'mysql')
    {
        $this->tableName = $tableName;
        $this->arrFilter = $arrFilter;
        $this->arrExcept = $arrExcept;
        $this->connection = $connection;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param $attribute
     * @param $value
     *
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        return !DB::connection($this->connection)->table($this->tableName)
            ->where('slug', $value)
            ->when(!empty($this->arrFilter), function ($query) {
                foreach ($this->arrFilter as $col => $val) {
                    $query->where($col, $val);
                }
            })
            ->when(!empty($this->arrExcept), function ($query) {
                foreach ($this->arrExcept as $col => $val) {
                    $query->where($col, '!=', $val);
                }
            })
            ->exists();
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'Slug already taken.';
    }
}
