<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class CampaignCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $campaignId;
    public $sellerInfo;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($campaignId, $sellerInfo)
    {
        $this->campaignId = $campaignId;
        $this->sellerInfo = $sellerInfo;
    }
}
