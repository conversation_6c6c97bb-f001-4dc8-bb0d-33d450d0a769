<?php

namespace App\Events;

use App\Models\PaymentAccount;
use App\Models\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PaymentAccountCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public User $user;
    public PaymentAccount $paymentAccount;

    /**
     * Create a new event instance.
     *
     * @param User $user
     * @param PaymentAccount $paymentAccount
     */
    public function __construct(User $user, PaymentAccount $paymentAccount)
    {
        $this->user = $user;
        $this->paymentAccount = $paymentAccount;
    }
}
