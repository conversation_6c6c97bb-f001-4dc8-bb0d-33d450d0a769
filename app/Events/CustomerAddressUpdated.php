<?php

namespace App\Events;

use App\Models\Order;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\OrderService\Models\RegionOrders;

class CustomerAddressUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;
    public Order|RegionOrders|null $order = null;

    /**
     * Create a new event instance.
     *
     * @param $orderNumber
     * @param bool $isRegion
     */
    public function __construct($orderNumber, $isRegion = false)
    {
        if ($isRegion) {
            $order = RegionOrders::onRegion(config('app.region'))->firstWhere('order_number', $orderNumber);
        } else {
            $order = Order::query()->firstWhere('order_number', $orderNumber);
        }

        if (!is_null($order)) {
            $this->order = $order;
        }
    }
}
