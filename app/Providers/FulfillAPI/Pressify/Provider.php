<?php

namespace App\Providers\FulfillAPI\Pressify;

use App\Providers\FulfillAPI\Beeful_v2\Model\CancelOrderModel;
use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Support\Arr;
use Throwable;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order';
    protected const ENDPOINT_CANCEL_ORDER = '/order/cancel';
    protected const ENDPOINT_CRAWL_ORDER = '/order';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", "Bearer {$provider['token']}");
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    /**
     * @return bool
     */
    public function shouldHaveRequestBody(): bool
    {
        return $this->method !== self::METHOD_CRAWL_ORDER && $this->method !== self::METHOD_CANCEL_ORDER;
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->params['payload']['api_key'] = $this->token;
                break;

            case self::METHOD_CANCEL_ORDER:
                $supOrderId = $params instanceof CancelOrderModel ? $params->id : $params;
                $this->api  .= '/' . $supOrderId . '?api_key=' . $this->token;
                $this->fulfill_order_id = $supOrderId;
                $this->method_url = 'GET';
                break;

            case self::METHOD_CRAWL_ORDER:
                $this->api  .= '/' . $params . '?api_key=' . $this->token;
                $this->fulfill_order_id = $params;
                $this->method_url = 'GET';
                break;
        }
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::crawlProductsAndProductVariantsJob()
     *
     * @throws \Throwable
     */
    public function crawlProductsAndProductVariantsJob(): void
    {
        $this->crawlProductsWithVariants();
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::skipProduct()
     *
     * @param     array     $item
     *
     * @return bool
     */
    protected function skipProduct(array $item): bool
    {
        return false;
    }



    /**
     * @throws Throwable
     */
    protected function crawlVariants(array $product): array
    {
        return [];
    }

    /**
     * @param $params
     *
     * @return bool|string
     * @throws Throwable
     */
    public function withBody($params): bool|string
    {
        return json_encode(Arr::get($params, 'payload'), JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
    }

    private function isOrderCanceled($pattern, $string) {
        if (preg_match($pattern, $string, $matches)) {
            return true;
        }

        return false;
    }

    /**
     * @throws Throwable
     */
    public function cancelOrder($fulfillOrderId, $orderId): bool
    {
        return true;
    }
}
