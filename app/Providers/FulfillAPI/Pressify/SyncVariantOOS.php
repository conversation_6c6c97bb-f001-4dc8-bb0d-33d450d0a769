<?php

namespace App\Providers\FulfillAPI\Pressify;

use App\Providers\FulfillAPI\AbstractSyncVariantOOS;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Throwable;
class SyncVariantOOS extends AbstractSyncVariantOOS
{
    /**
     * @param Collection $variants     *
     *
     * @return Collection
     *
     * @throws Throwable
     */
    public function fetchData(Collection $variants): Collection
    {
        $cfg = $this->getSupplierConfig();
        $token = $cfg['dev']['token'];
        $api =  $cfg['dev']['api'];
        if (app()->environment() === 'production') {
            $token = $cfg['production']['token'];
            $api =  $cfg['production']['api'];
        }
        $responses = Http::get($api . "/products?api-key=$token");
        if ($responses->failed()) {
            return collect();
        }
        $responses = $responses->json();
        return collect(data_get($responses, 'data', []))->keyBy('variant_id')->map(fn($response) => $this->parseStockQty($response));
    }

    /**
     * @param $response
     *
     * @return int|null
     */
    public function parseStockQty($response): ?int
    {
        try {
            $qty = data_get($response, 'stock');
            if (!is_numeric($qty)) {
                return null;
            }
            return (int) $qty;
        } catch (\Throwable $e) {
            return null;
        }
    }
}
