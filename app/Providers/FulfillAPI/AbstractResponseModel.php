<?php

namespace App\Providers\FulfillAPI;

use App\Enums\DiscordChannel;
use App\Enums\OrderProductFulfillStatus;
use App\Models\OrderProduct;
use App\Models\Supplier;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use RuntimeException;

abstract class AbstractResponseModel extends AbstractModel
{
    protected const DEFAULT_ERROR = 'Unknown error';
    protected const SERVER_ERROR = 'Server error, please try again later';
    public string $product_name = '';
    protected array $response_data;
    protected $exception_log = null;
    protected $fulfill_status = null;
    protected array $response_product_variants; // status response

    // for creating order
    protected $product_id;
    protected bool $status = true;
    protected array $errors = [];
    protected array $order_products = [];
    protected bool $need_crawl_order_details = false;

    //    attribute for pagination
    protected $nextPage;
    protected int $page = 0;
    protected int $limit_product_per_page = 50;
    protected int|null $isFulfillOrderIdExisted = null;
    protected string $orderExistedRegex = '';

    public function getResponseData(): array
    {
        unset($this->response_data['response_status_code']);
        return $this->response_data;
    }

    public function getNexPage()
    {
        return $this->nextPage;
    }

    /**
     * @return int
     */
    public function currentPage(): int
    {
        return $this->page;
    }

    public function getStatus(): bool
    {
        return $this->status;
    }

    public function getOrderProducts(): array
    {
        return $this->order_products;
    }

    public function setOrderProducts($array): void
    {
        $this->order_products[] = [
            'id' => Arr::get($array, 'id'),
            'fulfill_sku' => Arr::get($array, 'sku'),
            'fulfilled_quantity' => Arr::get($array, 'quantity'),
            'fulfill_cost' => Arr::get($array, 'cost', 0)
        ];
    }

    public function getExceptionLog()
    {
        return $this->exception_log;
    }

    public function getNeedCrawlOrderDetails(): bool
    {
        return $this->need_crawl_order_details;
    }

    public function getParamsToCrawl(): string
    {
        return $this->fulfill_order_id;
    }

    /**
     * @param $arr
     * @param $fulfillOrderId
     * @return void
     */
    public function checkAndThrowExceptionWhenNoResponse($arr, $fulfillOrderId): void
    {
        $this->handleError('Create Order', $arr, $fulfillOrderId);
        $fulfillOrderIdExisted = $this->handleFulfillOrderExisted(data_get($arr, 'message') ?? data_get($arr, 'data.message'));
        if (!empty($fulfillOrderIdExisted)) {
            $this->isFulfillOrderIdExisted = $fulfillOrderIdExisted;
            return;
        }

        if (!$this->checkCreateOrderHasResponse($arr)) {
            $message = $this->exception_log;
            if (empty($message)) {
                if (data_get($arr, 'success')) {
                    $message = Arr::get($arr, 'message');
                } else if (data_get($arr, 'data.success')) {
                    $message = Arr::get($arr, 'data.message');
                } else {
                    $message = 'No error message';
                }
            }
            throw new RuntimeException($message);
        }
    }

    public function handleError($method, $arr, ?string $fulfillOrderId = null): bool
    {
        if ($this->checkHasError($arr)) {
            $this->status = false;
            if (!empty($fulfillOrderId) && $method !== 'Create Order') {
                $logMessage = $method . ' failed.| Fulfill order ID:' . $fulfillOrderId;
                logToDiscord($logMessage, DiscordChannel::FULFILL_ORDER);
            }
            $this->exception_log = implode(';', $this->errors);
        } else {
            $this->status = true;
        }

        return $this->status;
    }

    protected function checkHasError($arr): bool
    {
        $this->setErrors($arr);
        $this->errors = array_filter($this->errors);
        if (empty($this->errors) && in_array((int)Arr::get($arr, 'response_status_code'), [502, 500,], true)) {
            $this->errors[] = 'Service Unavailable';
        } else if ((int)Arr::get($arr, 'response_status_code') === 429) {
            $this->handleOutOfLimitRate($arr);
        }
        return !empty($this->errors);
    }

    abstract protected function setErrors($arr): void;

    public function handleOutOfLimitRate($arr): void
    {
        $nextAvailable = 5 * 60; //sec
        try {
            $error = Arr::get($arr, 'error');
            $errorMessage = Arr::get($error, 'message');
            if (is_string($errorMessage) && Str::contains($errorMessage, 'Request was throttled')) {
                preg_match('/(\d+)\s+seconds/', $errorMessage, $matches);
                if (isset($matches[1])) {
                    $seconds = $matches[1];
                    $nextAvailable = $seconds;
                } else {
                    preg_match('/(\d+)\s+second/', $errorMessage, $matches);
                    $nextAvailable = $matches[1] ?: $nextAvailable;
                }
            }

        } catch (\Exception $e) {
        }
        Supplier::query()->where('id', $this->supplier_id)->update([
            'api_holds_until' => now()->addSeconds($nextAvailable)
        ]);
    }

    public function handleFulfillOrderExisted($string = null)
    {
        $pattern = $this->orderExistedRegex;
        if (!empty($pattern) && preg_match($pattern, $string, $matches)) {
            return data_get($matches, 1);
        }
        return null;
    }

    abstract protected function checkCreateOrderHasResponse($arr): bool;

    // for supplier which don't response anything after create order
    abstract public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void;

    public function updateOrderAfterCreateWithoutProducts(
        array   $orderProductIds,
        ?string $fulfillOrderId = null,
        ?string $fulfillStatus = null,
        ?string $fulfillExceptionLog = null
    ): void
    {
        $fulfillStatus ??= $this->getFulfillStatus();
        $fulfillExceptionLog ??= $this->exception_log;
        OrderProduct::query()
            ->whereIn('id', $orderProductIds)
            ->where('supplier_id', $this->supplier_id)
            ->update(
                [
                    'fulfill_order_id' => $fulfillOrderId ?? $this->fulfill_order_id,
                    'fulfill_status' => $fulfillStatus,
                    'fulfilled_quantity' => DB::raw('quantity'),
                    'fulfill_exception_log' => $fulfillExceptionLog,
                    'fulfilled_at' => DB::raw('CURRENT_TIMESTAMP'),
                ]
            );
    }

    public function getFulfillStatus(): string
    {
        return ($this->status && empty($this->errors)) ? OrderProductFulfillStatus::PROCESSING : OrderProductFulfillStatus::REJECTED;
    }

    protected function logException($method, $message = '', $fulfillOrderId = null): void
    {
        $errorMessage = $method . ' failed.';
        if (!empty($fulfillOrderId)) {
            $errorMessage .= '| Fulfill order ID:' . $fulfillOrderId;
        }
        if (!empty($message)) {
            $errorMessage .= '| Error:' . $message;
        }
        logToDiscord($errorMessage, DiscordChannel::FULFILL_ORDER);
        throw new RuntimeException($errorMessage);
    }
}
