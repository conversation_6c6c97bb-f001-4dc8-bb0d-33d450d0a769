<?php

namespace App\Providers\FulfillAPI\ShineOn;

use App\Providers\FulfillAPI\ObjectFulfill;
use Throwable;

class Provider extends ObjectFulfill
{
    private array $config = [];

    /**
     * @param array $provider
     *
     * @return void
     */
    public function setProvider(array $provider): void
    {
        parent::setProvider(
            $this->config = $provider
        );
    }

    public array $product_allow_refactor_option = [
        'Cuban Link Chain w/ Lux Box (Partner)',
        'Love Knot Necklace (Yellow & White Gold Variants) Partner'
    ];

    /**
     * @param $method
     *
     * @return void
     */
    protected function setMethod($method): void
    {
        parent::setMethod($method);
    }

    /**
     * @throws Throwable
     */
    public function setParams($params): void
    {
        $this->setHeader('Authorization', 'Bearer ' . $this->token);
        $this->refreshApi();

        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->api = $this->api_origin . $this->config['endpoints']['order_create'];
                $this->params = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'POST';
                break;

            case self::METHOD_CANCEL_ORDER:
                $this->api = $this->api_origin . sprintf(
                    $this->config['endpoints']['order_cancel'], $params->getFulfillOrderId()
                );
                $this->method_url = 'POST';
                break;

            case self::METHOD_CRAWL_ORDER:
                // params is fulfill_order_id
                $this->api = $this->api_origin . sprintf(
                    $this->config['endpoints']['order_detail'], $params
                );
                $this->method_url = 'GET';
                $this->fulfill_order_id = $params;
                break;

            case self::METHOD_CRAWL_PRODUCT:
                $this->api = $this->api_origin . $this->config['endpoints']['crawl_product'];
                $this->api .= '?buyer_upload=1&per_page=10000'; // Lấy tất cả sản phẩm
                $this->method_url = 'GET';
                break;

            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->api = $this->api_origin . $this->config['endpoints']['crawl_product'];
                $this->api .= '/' . $params['parent_id']; // Lấy tất cả sản phẩm
                $this->method_url = 'GET';
                break;
        }
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::crawlProductsAndProductVariantsJob()
     *
     * @throws \Throwable
     */
    public function crawlProductsAndProductVariantsJob(): void
    {
        $this->crawlProductsWithVariants();
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::afterCrawlAllProducts()
     *
     * @param     array     $products
     *
     * @return array
     */
    protected function afterCrawlAllProducts(array $products): array
    {
        return array_reduce($products, static function ($carry, $item) {
            $key = $item['parent_id'];
            if (empty($carry[$key])) {
                $carry[$key] = $item;
            }

            return $carry;
        }, []);
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::attachProductFieldIfNecessary()
     *
     * @param     array     $product
     * @param     array     $response
     *
     * @return array
     */
    protected function attachProductFieldIfNecessary(array $product, array $response): array
    {
        $product['thumb_url'] = $response['data']['image'];
        $product['options'] = $response['data']['options'];

        return $product;
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::skipProduct()
     *
     * @param array $item
     *
     * @return bool
     */
    protected function skipProduct(array $item): bool
    {
        return false;
    }

    /**
     * @param $sendData
     *
     * @return bool
     *
     * @throws Throwable
     */
    protected function handleCancelOrderResponse($sendData): bool
    {
        return data_get($sendData, 'order.status') === 'cancelled';
    }
}
