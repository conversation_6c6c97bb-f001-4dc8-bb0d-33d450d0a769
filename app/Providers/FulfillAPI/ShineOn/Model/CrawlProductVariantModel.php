<?php

namespace App\Providers\FulfillAPI\ShineOn\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\ShineOn\ParseVariantOptionHandler;
use Throwable;

class CrawlProductVariantModel extends \App\Providers\FulfillAPI\Common\CrawlProductVariantModel
{
    protected array $arrKeySeparates = [
        ProductOptionEnum::ENGRAVING
    ];

    const CUSTOM_PRODUCT_OPTIONS = [
        'Cuban Link Chain w/ Lux Box (Partner)',
        'Cuban Link Chain w/ Lux Box  (Gold)',
        'Cuban Link Chain w/ Lux Box (Stainless Steel)',
        'Love Knot Necklace (Gold)',
        'Love Knot Necklace'
    ];


    /**
     * @param     array     $raw
     * @param     array     $options
     * @param     array     $optionsSeparate
     *
     * @return array
     **/
    public function prepareProductRows(array $raw, array $options, array $optionsSeparate, array $product = []): array
    {
        $this->overrideProductOptions($raw, $options);

        $variant = getVariantKey($options);

        return [[
            // <PERSON><PERSON><PERSON> tùy chọn của sản phẩm: màu, size
            'options' => $options,

            // Các tùy chọn đặc biệt của sản phẩm, ví dụ: giới tính, chất liệu, ...
            'separate_options' => $optionsSeparate,

            'location_code' => '*',
            'variant_key' => $variant,
            'out_of_stock' => false,
            'base_cost' => $raw['base_cost'],
            'sku' => $raw['number'],
        ]];
    }

    /**
     * @override \App\Providers\FulfillAPI\Common\CrawlProductVariantModel::beforeMapping()
     *
     * @param array $rawVariant
     * @param array $product
     *
     * product: [
     *  "parent_id" => 1025
     *  "name" => "Heart Buyer Upload (with POD MC)"
     *  "sku" => 1026
     *  "thumb_url" => "https://s3.amazonaws.com/shineon-cdn-public/fulfillment/product_templates/1025/img_src/9.png"
     *  "description" => "..."
     *  "product_type" => "fulfill_product"
     *  "supplier_id" => 38
     *  "options" => array:3 [
     *      "option1" => "Title"
     *      "option2" => "Engraving"
     *      "option3" => null
     *  ]
     * ]
     * rawVariant: [
     *  "id" => 1026
     *  "number" => "PT-1026"
     *  "image" => "https://s3.amazonaws.com/shineon-cdn-public/fulfillment/product_templates/1026/img_src/5.jpg"
     *  "title" => "BU Heart Necklace (Silver) +POD MC"
     *  "base_cost" => 14.77
     *  "price" => "54.95"
     *  "option1_value" => "Luxury Necklace (.316 Surgical Steel)"
     *  "option2_value" => "No"
     *  "option3_value" => null
     *  ...
     * ]
     *
     * @return array
     */
    public function beforeMapping(array $rawVariant, array $product): array
    {
        $rawVariant['options'] = collect($product['options'])
            ->reduce(static function($res, $optName, $suffix) use($rawVariant) {
                $res[$optName] = $rawVariant[$suffix . '_value'] ?? null;

                return $res;
            }, collect())
            ->filter(fn($value) => ! is_null($value))
            ->toArray();
        return $rawVariant;
    }

    /**
     * @param array $data
     * @param string $productName
     * @return array
     * @throws Throwable
     */
    public function getOptions(array $data, string $productName): array
    {
        try {
            $options = $this->extractOptionHandler()->handle($data);
        } catch (Throwable $exception) {
            $this->handleParseVariantOptionException($exception, $data, $productName);
        }

        return array_map(
            'parent::mappingOptions',
            $options ?? []
        );
    }

    /**
     * @return \App\Providers\FulfillAPI\ShineOn\ParseVariantOptionHandler
     */
    public function extractOptionHandler(): ParseVariantOptionHandler
    {
        return app(ParseVariantOptionHandler::class);
    }

    private function overrideProductOptions ($product, &$options) {
        if (in_array($product['title'], self::CUSTOM_PRODUCT_OPTIONS)) {
            if (in_array($product['title'], [
                'Cuban Link Chain w/ Lux Box  (Gold)',
                'Cuban Link Chain w/ Lux Box (Stainless Steel)'
            ])) {
                foreach($options as $key => &$option) {
                    if ($key == ProductOptionEnum::TYPE) {
                        switch(strtolower($option)) {
                            case '14k yellow gold finish':
                                $option = 'gold';
                                break;
                            case 'polished stainless steel':
                                $option = 'stainless steel';
                                break;
                        }
                    }
                }
            } else if (
                in_array($product['title'], [
                    'Love Knot Necklace (Gold)',
                    'Love Knot Necklace'
                ])
            ) {
                foreach($options as $key => &$option) {
                    if ($key == ProductOptionEnum::TYPE) {
                        switch(strtolower($option)) {
                            case '18k yellow gold finish':
                                $option = '18k yellow gold';
                                break;
                            case '14k white gold finish':
                                $option = '14k white gold';
                                break;
                        }
                    }
                }
                sortArrayOptions($options);
            }
        }
    }
}
