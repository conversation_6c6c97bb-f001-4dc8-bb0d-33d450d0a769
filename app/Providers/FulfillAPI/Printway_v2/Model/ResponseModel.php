<?php

namespace App\Providers\FulfillAPI\Printway_v2\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\PrintwayV2WebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    /**
     * @var mixed
     */
    protected int $totalPage;

    public function mappingCrawlProducts(array $arr): void
    {
        $this->response_data = $arr['data'];
        $this->totalPage = $arr['totalPage'];
    }

    /**
     * @return bool
     */
    public function getNexPage(): bool
    {
        return $this->page <= $this->totalPage;
    }

    /**
     * @param array $arr
     *
     * @return void
     */
    public function mappingCrawlProductVariants(array $arr): void
    {
        $this->response_data = $arr;
    }

    protected function setErrors($arr): void
    {
        if (! $arr['success']) {
            $this->errors[] = $arr['message'];
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['data']['pwOrderId']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIdsHandle, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIdsHandle,
            $arr['data']['pwOrderId'],
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['data'])) {
            return (new PrintwayV2WebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }

    /**
     * @return array
     */
    public function getParamsPagination(): array
    {
        return [
            'page'  => ++$this->page,
            'limit' => $this->limit_product_per_page,
        ];
    }
}
