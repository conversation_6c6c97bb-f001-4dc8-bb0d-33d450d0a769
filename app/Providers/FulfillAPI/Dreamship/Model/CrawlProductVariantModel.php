<?php

namespace App\Providers\FulfillAPI\Dreamship\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductVariantModel extends AbstractModel
{

    public function mapping(array $arr, $productName): array
    {
        return [
            [
                'variant_key'  => $this->getVariantKey($arr, $productName),
                'out_of_stock' => self::getOutOfStock($arr['availability']),
                'base_cost'    => $arr['cost'],
                'sku'          => $arr['id'],
            ]
        ];
    }

    private function getVariantKey(array $arr, $productName): string
    {
        return getVariantKey(
            [
                self::mappingOptions($arr['color']['name'], 'color', $productName),
                self::mappingOptions($arr['size']['name'], 'size', $productName),
            ]
        );
    }

    private const COLOR_MAPPING = [
        'forest' => 'forest green',
    ];

    private const ARRAY_MAPPING = [
        'Candle' => [
            'pink'               => 'light pink',
            'light pink vanilla' => 'light pink',
        ],
    ];

    /** @noinspection PhpParameterNameChangedDuringInheritanceInspection */
    public static function mappingOptions($option, $type = 'all', $productName = '', $hadParsed = false): string
    {
        $option = parent::parseOption($option);
        $arr    = self::ARRAY_MAPPING;
        foreach ($arr as $key => $each) {
            if (strpos($productName, $key) !== false) {
                $arrMapping = $each;
                break;
            }
        }
        if (empty($arrMapping)) {
            /** @noinspection PhpSwitchStatementWitSingleBranchInspection */
            /** @noinspection DegradedSwitchInspection */
            switch ($type) {
                case 'color':
                    $arrMapping = self::COLOR_MAPPING;
                    break;
            }
        }
        $option = $arrMapping[$option] ?? $option;

        return parent::mappingOptions($option, $type, $productName, true);
    }
}
