<?php

namespace App\Providers\FulfillAPI\CustomCat\Model;

use App\Enums\CountryEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Exception;

class CreateOrderModel extends AbstractModel
{
    public string $shipping_first_name;
    public string $shipping_last_name;
    public string $shipping_address1;
    public string $shipping_address2;
    public string $shipping_city;
    public string $shipping_state;
    public string $shipping_zip;
    public string $shipping_country;
    public string $shipping_email;
    public string $shipping_phone;
    public string $shipping_method;
    public array $items;
    public string $sandbox;
    public string $api_key;

    public function setMode($mode): void
    {
        $this->sandbox = ($mode === 'production') ? '0' : '1';
    }

    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->getReferenceId($order);
        $this->setShipping($order);
    }

    private function setShipping($order): void
    {
        $this->shipping_first_name = $this->customer_name['first_name'];
        $this->shipping_last_name  = $this->customer_name['last_name'];
        $this->shipping_address1   = $this->customer_address['primary'];
        $this->shipping_address2   = $this->customer_address['addition'];
        $this->shipping_city       = $order->city;
        $this->shipping_state      = $order->state ?? '';
        $this->shipping_zip        = $order->postcode;
        $this->shipping_country    = $order->country;
        $this->shipping_email      = !empty($order->customer_email) ? $order->customer_email : self::SUPPORT_EMAIL;
        $this->shipping_phone      = $order->customer_phone ?? '';
        $this->setShippingMethod();

        if (CountryEnum::isEu($order->country)) {
            $this->shipping_address2 .= ', ioss: ' . ($order->ioss_number ?? 'IN2330015967'); // ioss number for EU
        }
    }

    /**
     * @throws Exception
     */
    public function setItems($product): void
    {
        $arr = [
            'catalog_sku' => (string)$product->fulfill_sku,
            'quantity'    => (int)$product->quantity,
        ];
        if (!$product->full_printed && strpos($product->product_name, 'Mug') === false) {
            $arr['preset_id'] = 2;
        }

        // default
        $design_url_back = '';

        foreach ($product->files as $file) {
            if ($file->print_space === 'back') {
                $design_url_back = $file->design_url;
            } else {
                $design_url = $file->design_url;
            }
            if (!empty($file->is_design_mapping)) {
                unset($arr['preset_id']);
            }
        }

        if (!empty($design_url)) {
            $arr['design_url'] = $design_url;
        }

        if (!empty($design_url_back)) {
            $arr['design_url_back'] = $design_url_back;
        }

        $this->items[] = $arr;
    }

    private function setShippingMethod(): void
    {
        $this->shipping_method = $this->mapped_shipping_methods[0];
    }
}
