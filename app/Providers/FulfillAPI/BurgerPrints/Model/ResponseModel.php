<?php

namespace App\Providers\FulfillAPI\BurgerPrints\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\BurgerPrintsWebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($arr): void
    {
        $this->errors[] = (isset($arr['is_success']) && $arr['is_success'] === false) ? self::DEFAULT_ERROR : null;
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['log_id']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['log_id'],
            OrderProductFulfillStatus::PENDING,
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId, $justCreated): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['id']) || !empty($arr['order_id'])) {
            // @see docs/suppliers/BurgerPrints/readme.txt
            return (new BurgerPrintsWebhookRepository($arr, $this->supplier_id, $justCreated))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
