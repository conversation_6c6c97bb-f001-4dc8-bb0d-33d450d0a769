<?php

namespace App\Providers\FulfillAPI\PrintLogistic;

use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Support\Facades\Http;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/job';
    protected const ENDPOINT_CANCEL_ORDER = '/job';
    protected const ENDPOINT_CRAWL_ORDER = '/get_job_status/'; // get_job_status/{id}
    protected const ENDPOINT_CRAWL_ORDER_DETAILS = '/get_job_status/'; // get_job_status/{id}

    public function setParams($params): void
    {
        $this->setApiWithEndpoint();
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CANCEL_ORDER:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->params           = $params->getPublicAttrs();
                $this->method_url       = 'PUT';
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
            case self::METHOD_CRAWL_ORDER_DETAILS:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->api              .= $params;
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
        }
    }

    public function sendData($response = null, ?string $userId = null, $saveLog = true): string
    {
        /** @noinspection SuspiciousAssignmentsInspection */
        $response = Http::withHeaders($this->headers)
            ->withBody(json_encode($this->params), 'application/json')
            ->send($this->method_url, $this->api)
            ->body();

        return parent::sendData($response, $userId);
    }
}
