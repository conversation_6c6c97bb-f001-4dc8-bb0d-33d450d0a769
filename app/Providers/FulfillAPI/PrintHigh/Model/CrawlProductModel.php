<?php

namespace App\Providers\FulfillAPI\PrintHigh\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductModel extends AbstractModel
{
    public function mapping(array $product): array
    {
        return [
            'sku'          => $product['value'],
            'product_type' => $this->product_type,
            'supplier_id'  => $this->supplier_id,
            'name'         => $product['text'],
            'options'      => $this->getOptions($product),
            'description'  => $product['type'] . '-' . $product['category'] . '-' . $product['print_type'],
        ];
    }

    private function getOptions(array $arrOptions): string
    {
        $arr = [];
        foreach ($arrOptions['colors'] as $color) {
            $colorName = self::mappingOptions($color, 'color');
            $arr['color'][] = self::mappingOptions($color, 'color');
            $this->addNewColor(
                [
                    'name' => $colorName,
                ]
            );
        }
        foreach ($arrOptions['sizes'] as $size) {
            $arr['size'][] = self::mappingOptions($size, 'size');
        }

        return json_encode($arr);
    }
}
