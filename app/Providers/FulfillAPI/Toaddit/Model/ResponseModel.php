<?php

namespace App\Providers\FulfillAPI\Toaddit\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\ToadditWebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    public function mappingCrawlProducts(array $arr): void
    {
        $this->response_data = $arr['products'];
    }

    protected function setErrors($arr): void
    {
        if ($arr['status'] !== 200) {
            $this->errors[] = $arr['message'];
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['result']['pw_order_id']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIdsHandle, $fulfillOrderId = null): void
    {
        $arr = $arr['result'];

        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIdsHandle,
            $arr['pw_order_id'],
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['order'])) {
            return (new ToadditWebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
