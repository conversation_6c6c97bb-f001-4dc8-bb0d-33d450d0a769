<?php

namespace App\Providers\FulfillAPI\Pentifine\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CrawlProductVariantModel extends AbstractModel
{
    public static function mapping(array $variant, array $options): array
    {
        $color = data_get($options, 'color');
        $size = data_get($options, 'size');
        $status = data_get($variant, 'status');
        if (empty($color)) {
            return [];
        }
        $options = [
            'color' => $color,
            'size' => strtolower($size),
        ];
        $options['color'] = static::mappingOptions($options['color'], ProductOptionEnum::COLOR);
        return [
            'sku' => data_get($variant, 'variant_id'),
            'sku_bak' => data_get($variant, 'sku'),
            'base_cost' => data_get($variant, 'price'),
            'options' => $options,
            'variant_key' => getVariantKey($options),
            'out_of_stock' => $status && $status !== 'active',
        ];
    }
}
