<?php

namespace App\Providers\FulfillAPI\Pentifine\Model;

use App\Providers\FulfillAPI\AbstractModel;

class CrawlProductModel extends AbstractModel
{

    /**
     * @param     array     $product
     *
     * @return array
     */
    public function mapping(array $product): array
    {
        return [
            'sku' => data_get($product, 'code'),
            'product_type' => $this->product_type,
            'supplier_id' => $this->supplier_id,
            'name' => data_get($product, 'name'),
            'description' => data_get($product, 'description'),
        ];
    }
}
