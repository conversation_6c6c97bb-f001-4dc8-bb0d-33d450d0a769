<?php

namespace App\Providers\FulfillAPI\PrintArrow;

use App\Providers\FulfillAPI\Common\TokenRetriever;
use App\Providers\FulfillAPI\ObjectFulfill;
use Throwable;

class Provider extends ObjectFulfill
{
    //Docs : https://documenter.getpostman.com/view/********/2sAYJ9AJTi
    //DEV dashboard : https://staging.monkeykingprint.com/vendors/account/login/
    //DEV username : <EMAIL>
    //DEV pass : Admin123

    private array $config = [];

    /**
     * @param array $provider
     *
     * @return void
     */
    public function setProvider(array $provider): void
    {
        parent::setProvider(
            $this->config = $provider
        );
    }

    /**
     * @param $method
     *
     * @return void
     */
    protected function setMethod($method): void
    {
        parent::setMethod($method);
    }

    /**
     * @throws Throwable
     */
    public function setParams($params): void
    {
        $this->setHeader('Authorization', 'Bearer ' . $this->tokenRetriever()->retrieve());
        $this->refreshApi();
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $this->api = $this->api_origin . $this->config['endpoints']['order_create'];
                $this->params = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->method_url = 'POST';
                break;

            case self::METHOD_CANCEL_ORDER:
                $this->api = $this->api_origin . $this->config['endpoints']['order_cancel'];
                $this->params = ["order_code_list" => [$params->getFulfillOrderId()]];
                $this->method_url = 'POST';
                break;

            case self::METHOD_CRAWL_ORDER:
                // params is fulfill_order_id
                $this->api = $this->api_origin . $this->config['endpoints']['order_detail'] . '?searchCriteria[filterGroups][0][filters][0][field]=increment_id&searchCriteria[filterGroups][0][filters][0][value]=' . $params;
                $this->method_url = 'GET';
                $this->fulfill_order_id = $params;
                break;
        }
    }

    /**
     * @return TokenRetriever
     */
    public function tokenRetriever(): TokenRetriever
    {
        return app(\App\Providers\FulfillAPI\PrintArrow\TokenRetriever::class)->withConfigArray($this->config);
    }

    /**
     * @return int
     */
    public function requestTimeout(): int
    {
        return 30;
    }
}
