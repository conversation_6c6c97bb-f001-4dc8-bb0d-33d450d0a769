<?php

namespace App\Providers\FulfillAPI\Lenful\Model;

use App\Enums\FileTypeEnum;
use App\Enums\FulfillMappingEnum;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\AbstractModel;
use Exception;

// curl --location 'https://s-lencam.lenful.com/api/order/64ef1991074769dcaa13585b/create' \
// -H 'accept: */*' \
// -H 'Content-Type: application/json' \
// -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjY0ZWYxOTNkMWEzZWUzOWI3YWFjOWI3ZiIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL2VtYWlsYWRkcmVzcyI6InZ1b25nbnFAc2VucHJpbnRzLmNvbSIsIkZpcnROYW1lIjoiVnVvbmciLCJMYXN0TmFtZSI6Ik5xIiwiRnVsbE5hbWUiOiJWdW9uZyBOcSIsIlVzZXJOYW1lIjoiIiwiVG9rZW5UeXBlIjoiQ3VzdG9tZXIiLCJTaG9wcyI6IltcIjYzMzE3NGVhYTE0M2Y4MTZkNTE4YjdmMVwiXSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6Im93bmVycyIsIk1hbmFnZXJzIjoiW3tcIm1hbmFnZXJfaWRcIjpcIjY0NzgxODVjZWIyNzM1MTg4YjRmNjM3M1wiLFwibWFuYWdlcl9uYW1lXCI6XCJKaW5ueSBWdVwifSx7XCJtYW5hZ2VyX2lkXCI6XCI2NDc4MThhMGViMjczNTE4OGI0ZjYzNzRcIixcIm1hbmFnZXJfbmFtZVwiOlwiTGluaCBQaOG6oW1cIn1dIiwibmJmIjoxNzAxMDcxNDczLCJleHAiOjE3MDM2NjM0NzMsIndhbGxldF9pZCI6IjY0ZWYxOWFmNzcyYzc1MTc5NjYyMzQ1MSJ9.ecrYfSW8xb8uGYiCkBD4Bc2IZzmEL2Co6-XyhT0nSVo' \
// --data-raw '{
//     "order_number": "21789382-1701073719",
//     "first_name": "<PERSON>",
//     "last_name": "Iking",
//     "email": "<EMAIL>",
//     "phone": "+4917647345402",
//     "country_code": "DE",
//     "province": "Vrede",
//     "city": "Vrede",
//     "zip": "48691",
//     "address_1": "Hooge Feld 14",
//     "address_2": "",
//     "note": null,
//     "items": [
//         {
//             "design_sku": "LFCA74L",
//             "product_sku": "LFCA74L",
//             "quantity": 1,
//             "mockups": [
//                 "https:\/\/img.cloudimgs.net\/rx\/1000\/s4\/l_p:6370062:c70a0a_cr\/fl_cutter,fl_layer_apply\/u_p:6370062:dc9d83_co\/co_rgb:FFFFFF,e_colorize:100\/fl_layer_apply\/l_p:6370062:7a94af_sh\/fl_layer_apply\/u_p:6370062:9b38c0a4eea2d5d8\/fl_layer_apply\/c_thumb,w_1280\/f_jpg\/v1\/p\/144559619\/92aeba1f959e5662464892247e65f697\/t\/509796a2386c5016.jpg"
//             ],
//             "designs": [
//                 {
//                     "position": 1,
//                     "link": "https:\/\/senasia.s3-ap-southeast-1.amazonaws.com\/p\/144559619\/b3dc01496746aaaee9fd048369710820.jpg"
//                 }
//             ],
//             "shippings": [
//                 0,
//                 1,
//                 2
//             ]
//         },
//         {
//             "design_sku": "LFCA74L",
//             "product_sku": "LFCA74L",
//             "quantity": 1,
//             "mockups": [
//                 "https:\/\/img.cloudimgs.net\/rx\/1000\/s4\/l_p:6370062:c70a0a_cr\/fl_cutter,fl_layer_apply\/u_p:6370062:dc9d83_co\/co_rgb:FFFFFF,e_colorize:100\/fl_layer_apply\/l_p:6370062:7a94af_sh\/fl_layer_apply\/u_p:6370062:9b38c0a4eea2d5d8\/fl_layer_apply\/c_thumb,w_1280\/f_jpg\/v1\/p\/144560101\/9844094c2a5cb2c300442185f662b607\/t\/f8abcc25230d0dde.jpg"
//             ],
//             "designs": [
//                 {
//                     "position": 1,
//                     "link": "https:\/\/senasia.s3-ap-southeast-1.amazonaws.com\/p\/144560101\/d8082ca816e2a995f9175695042ca1eb.jpg"
//                 }
//             ],
//             "shippings": [
//                 0,
//                 1,
//                 2
//             ]
//         }
//     ]
// }'

class CreateOrderModel extends AbstractModel
{
    private string $order_number; // ": "#String",

    private string $first_name; // ": "Nguyễn Thị",

    private string $last_name; // ": "Test",

    private ?string $email; // ": "<EMAIL>",

    private string $phone; // ": "string",

    private string $country_code; // ": "VN",

    private string $province; // ": "Hà Nội",

    private string $city; // ": "Hà Nội",

    private string $zip; // ": "100000",

    private string $address_1; // ": "Số 3, ngõ 3, Duy Tân, Cầu Giấy",

    private ?string $address_2; // ": "string",

    private ?string $note; // ": "string",

    private array $items;

    public ?string $shippingType;

    /**
     * @throws Exception
     */
    public function setOrder($order): void
    {
        $this->setShippingMethod();

        $this->setOrderNumber(
            $this->fulfill_order_id = $this->getReferenceId($order)
        );

        $this->setFirstName($this->customer_name['first_name']);
        $this->setLastName($this->customer_name['last_name']);
        $this->setEmail($order->customer_email);
        $this->setPhone($order->customer_phone);
        $this->setCountryCode($order->country);
        $this->setProvince($order->state ?: $order->city);
        $this->setCity($order->city);
        $this->setZip($order->postcode);
        $this->setAddress1($this->customer_address['primary']);
        $this->setAddress2($this->getShippingAddress2());
        $this->setNote($this->getOrderNote($order));
    }

    /**
     * @return string|null
     */
    public function getShippingAddress2()
    {
        $value = $this->customer_address['addition'] ?? '';
        if (str_contains($value, self::ORDER_NOTE_TEXT . ':')) {
            $addition = explode('(' .self::ORDER_NOTE_TEXT, $value);
            $value = trim(rtrim($addition[0], ','));
        }
        return trim($value);
    }

    private function setShippingMethod(): void
    {
        $this->shippingType = $this->mapped_shipping_methods[0];
    }

    private function getImage($product): string
    {
        foreach ($product->files as $file) {
            return $file->design_url;
        }

        throw new \RuntimeException('This order product ' . $product->id . ' don\'t have any design file');
    }

    public function getOrderNumber(): string
    {
        return $this->order_number;
    }

    public function setOrderNumber(string $value)
    {
        $this->order_number = $value;

        return $this;
    }

    public function getFirstName(): string
    {
        return $this->first_name;
    }

    public function setFirstName(string $value)
    {
        $this->first_name = $value;

        return $this;
    }

    public function getLastName(): string
    {
        return $this->last_name;
    }

    public function setLastName(string $value)
    {
        $this->last_name = $value;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(?string $value)
    {
        $this->email = $value;

        return $this;
    }

    public function getPhone(): string
    {
        return $this->phone;
    }

    public function setPhone(?string $value)
    {
        $this->phone = $value ?: self::SUPPORT_PHONE;

        return $this;
    }

    public function getCountryCode(): string
    {
        return $this->country_code;
    }

    public function setCountryCode(string $value)
    {
        $this->country_code = $value;

        return $this;
    }

    public function getProvince(): string
    {
        return $this->province;
    }

    public function setProvince(string $value)
    {
        $this->province = $value;

        return $this;
    }

    public function getCity(): string
    {
        return $this->city;
    }

    public function setCity(string $value)
    {
        $this->city = $value;

        return $this;
    }

    public function getZip(): string
    {
        return $this->zip;
    }

    public function setZip(string $value)
    {
        $this->zip = $value;

        return $this;
    }

    public function getAddress1(): string
    {
        return $this->address_1;
    }

    public function setAddress1(string $value)
    {
        $this->address_1 = $value;

        return $this;
    }

    public function getAddress2(): ?string
    {
        return $this->address_2;
    }

    public function setAddress2(?string $value)
    {
        $this->address_2 = $value;

        return $this;
    }

    public function getNote(): ?string
    {
        return $this->note;
    }

    public function setNote(?string $value)
    {
        $this->note = $value;

        return $this;
    }

    public function getItems(): array
    {
        return $this->items;
    }

    public function setListItem(array $value)
    {
        $this->items = $value;

        return $this;
    }

    public function setItems(OrderProduct $value)
    {
        $this->items[] = $this->getOrderItemMapper($value)->toArray();

        return $this;
    }

    /**
     * @param $value
     * @param $type
     * @param $location
     * @param bool $strict
     * @return string
     */
    public function mappingPrintSpace($value, $type, $location = null, bool $strict = false): string
    {
        if ($type === FulfillMappingEnum::PRINT_SPACES && ! in_array($value, ['front', 'back'])) {
            return 'front';
        }

        return parent::getByMapping($value, $type, $location, $strict);
    }

    /**
     * @param OrderProduct $value
     * @return Object
     */
    private function getOrderItemMapper(OrderProduct $value): object
    {
        return new class($this, $value) {
            private string $product_sku;

            private int $quantity;

            private array $mockups;

            private array $designs;

            private array $shippings;

            public function __construct(CreateOrderModel $parent, OrderProduct $p)
            {
                $this->setProductSku($p->fulfill_sku);
                $this->setQuantity($p->quantity);
                $designs = $p->files->filter(fn($v) => $v->type === FileTypeEnum::DESIGN && in_array($v->print_space, ['front', 'back', 'default']));
                $designsSetting = [];
                $mockupsSetting = [];
                foreach ($designs as $design) {
                    if (empty($design->design_url) || empty($design->mockup_url)) {
                        continue;
                    }
                    $printSpace = $parent->mappingPrintSpace(
                        $design->print_space,
                        FulfillMappingEnum::PRINT_SPACES
                    );
                    $position = ['front' => 1, 'back' => 2][$printSpace];
                    $designsSetting[] = ['position' => $position, 'link' => $design->design_url];
                    $mockupsSetting[] = $design->mockup_url;
                }

                $this->setDesigns($designsSetting);
                $this->setMockups($mockupsSetting);
                if ($parent->shippingType == 0) {
                    $this->setShippings([(int) $parent->shippingType, 1]);
                } else if ($parent->shippingType == 2) {
                    $this->setShippings([(int) $parent->shippingType, 0]);
                } else {
                    $this->setShippings([0, 1, 2]);
                }
            }

            public function getProductSku(): string
            {
                return $this->product_sku;
            }

            public function setProductSku(string $productSku)
            {
                $this->product_sku = $productSku;

                return $this;
            }

            public function getQuantity(): int
            {
                return $this->quantity;
            }

            public function setQuantity(int $quantity)
            {
                $this->quantity = $quantity;

                return $this;
            }

            public function getMockups(): array
            {
                return $this->mockups;
            }

            public function setMockups(array $mockups)
            {
                $this->mockups = $mockups;

                return $this;
            }

            public function getDesigns(): array
            {
                return $this->designs;
            }

            public function setDesigns(array $designs)
            {
                $this->designs = $designs;

                return $this;
            }

            public function getShippings(): array
            {
                return $this->shippings;
            }

            public function setShippings(array $shippings)
            {
                $this->shippings = $shippings;

                return $this;
            }

            public function toArray(): array
            {
                return [
                    'product_sku' => $this->getProductSku(),
                    'quantity' => $this->getQuantity(),
                    'mockups' => $this->getMockups(),
                    'designs' => $this->getDesigns(),
                    'shippings' => $this->getShippings(),
                ];
            }
        };
    }

    public function toArray(): array
    {
        return [
            'order_number' => $this->getOrderNumber(),
            'first_name' => $this->getFirstName(),
            'last_name' => $this->getLastName(),
            'email' => $this->getEmail(),
            'phone' => $this->getPhone(),
            'country_code' => $this->getCountryCode(),
            'province' => $this->getProvince(),
            'city' => $this->getCity(),
            'zip' => $this->getZip(),
            'address_1' => $this->getAddress1(),
            'address_2' => $this->getAddress2(),
            'note' => $this->getNote(),
            'items' => $this->getItems(),
        ];
    }

    public function getPublicAttrs(): array
    {
        return $this->toArray();
    }
}
