<?php

namespace App\Providers\FulfillAPI\Lenful\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\Lenful\ParseVariantOptionHandler;
use Throwable;

class CrawlProductVariantModel extends \App\Providers\FulfillAPI\Common\CrawlProductVariantModel
{
    /*
     * product:
     *      name: ao thun
     *      sku: ao-thun
     * variants
     *      Vat lieu: Vai canvas/Gioi tinh: Nam/Size: S/Mau: Trang
     *      Vat lieu: Vai canvas/Gioi tinh: Nam/Size: M/Mau: Trang
     *      Vat lieu: Vai canvas/Gioi tinh: NU/Size: S/Mau: Trang
     *      Vat lieu: Vai canvas/Gioi tinh: NU/Size: S/Mau: Den
     *      Vat lieu: Vai Coton/Gioi tinh: Unisex/Size: S/Mau: Trang
     *      Vat lieu: Vai Coton/Gioi tinh: Unisex/Size: M/Mau: Trang
     * => product
     * p1:
     *      name: ao thun - nam - vai canvas
     *      sku: ao-thun-nam-vai-canvas
     *  variants:
     *      Vat lieu: Vai canvas/Gioi tinh: Nam/Size: S/Mau: Trang
     *      Vat lieu: Vai canvas/Gioi tinh: Nam/Size: M/Mau: Trang
     * p2:
     *      name: ao thun - nu - vai canvas
     *      sku: ao-thun-nu-vai-canvas
     *  variants:
     *      Vat lieu: Vai canvas/Gioi tinh: Nu/Size: S/Mau: Trang
     *      Vat lieu: Vai canvas/Gioi tinh: Nu/Size: S/Mau: Den
     *
     * p3:
     *      name: ao thun - unisex - vai coton
     *      sku: ao-thun-unisex-vai-coton
     *  variants:
     *      Vat lieu: Vai Coton/Gioi tinh: Unisex/Size: S/Mau: Trang
     *      Vat lieu: Vai Coton/Gioi tinh: Unisex/Size: M/Mau: Trang
     *
     * @param array $arr
     * @param array $product
     * @return array
     */

    protected array $arrKeySeparates = [
        ProductOptionEnum::TYPE,
        ProductOptionEnum::SHAPE,
        ProductOptionEnum::PANEL,
        ProductOptionEnum::PACK,
        ProductOptionEnum::GENDER,
        ProductOptionEnum::MODEL,
        ProductOptionEnum::MATERIAL,
        ProductOptionEnum::PRINT,
        ProductOptionEnum::OPTION,
        ProductOptionEnum::ADDITIONAL,
        ProductOptionEnum::LONG_PANTS_SIZE,
        ProductOptionEnum::SPORT_BRAS_SIZE
    ];

    /**
     * @param     array     $arr
     * @param     array     $product
     *
     * @return array
     * @throws \Throwable
     */
    public function mapping(array $arr, array $product = []): array
    {
        $options = $this->getOptions($arr, $product['name']);
        $optionsSeparate = $this->getOptionSeparateProduct($options, $product['name']);

        ksort($options);
        ksort($optionsSeparate);

        return [
            [
                // Các tùy chọn của sản phẩm: màu, size
                'options' => $options,

                // Các tùy chọn đặc biệt của sản phẩm, ví dụ: giới tính, chất liệu, ...
                'separate_options' => $optionsSeparate,

                'variant_key' => getVariantKey($options),
                'out_of_stock' => self::getOutOfStock($arr['status']),
                'base_cost' => $arr['base_cost'],
                'sku' => $arr['sku'],
            ]
        ];
    }

    /**
     * @param array $data
     * @param string $productName
     * @return array
     * @throws Throwable
     */
    public function getOptions(array $data, string $productName): array
    {
        try {
            $options = $this->parser()->handle($data);

            if (! array_key_exists(ProductOptionEnum::COLOR, $options)) {
                $options['color'] = 'white';
            }
        } catch (Throwable $exception) {
            $this->handleParseVariantOptionException($exception, $data, $productName);
        }

        foreach ($options as $key => $option) {
            $options[$key] = parent::mappingOptions($option, $key, $productName, true);
        }

        return $options;
    }

    /**
     * @return ParseVariantOptionHandler
     */
    public function parser(): ParseVariantOptionHandler
    {
        return app(ParseVariantOptionHandler::class);
    }

    /**
     * @param $option
     * @param $type
     * @param $addition
     * @param $hadParsed
     *
     * @return string|null
     */
    public static function mappingOptions($option, $type = 'all', $addition = '', $hadParsed = false): ?string
    {
        $option = parent::mappingOptions($option, $type, $addition, $hadParsed);

        return $type === ProductOptionEnum::SIZE ? self::parseSizeOption($option) : $option;
    }

    /**
     * @param $option
     *
     * @return string
     */
    public static function parseSizeOption($option): string
    {
        // size giày sẽ chỉ lấy size US
        return preg_match('#eu[\d.]+/us([\d.]+)#i', $option, $matches)
            ? str_replace('.', '_', $matches[1])
            : $option;
    }
}
