<?php

namespace App\Providers\FulfillAPI\MonsterDigital;

use App\Providers\FulfillAPI\ObjectFulfill;
use Illuminate\Support\Str;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order/create';
    protected const ENDPOINT_CANCEL_ORDER = '/order/cancelorder';
    protected const ENDPOINT_CRAWL_ORDER = '/order/GetOrderStatus';

    private string $secret;

    private function setSecret($secret): void
    {
        $this->secret = $secret;
    }

    public function setProvider(array $provider): void
    {
        parent::setProvider($provider);
        $this->setSecret($provider['secret']);
    }

    public function setParams($params): void
    {
        $this->setApiWithEndpoint();
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
            case self::METHOD_CANCEL_ORDER:
                $data           = json_encode($params->getPublicAttrs(), JSON_UNESCAPED_SLASHES);
                $this->params   = [
                    'key'       => $this->token,
                    'data'      => $data,
                    'signature' => sha1($this->secret . $this->token . $data),
                ];
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $timestamp    = Str::random(32);
                $this->params = [
                    'xid'       => $params,
                    'timestamp' => $timestamp,
                ];
                $this->api    .= '?' . http_build_query($this->params);
                $this->setHeader('apikey', $this->token);
                $this->setHeader('signature', sha1($this->token . $timestamp . $this->secret));
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
        }
    }
}
