<?php

namespace App\Providers\FulfillAPI\MonsterDigital\Model;

use App\Enums\DiscordChannel;
use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Exception;
use Illuminate\Support\Str;

class CreateOrderModel extends AbstractModel
{
    protected const ITEM_TYPE = 'DigitalPrint';
    protected const DEFAULT_EXTENSION_DESIGN = 'png';

    public string $type = 'ORDER'; // or 'REPRINT'
    public string $time;           // '3/30/2021 3:04:14 PM'
    public string $method = 'create';
    public string $mode;
    public string $status = 'In Production';
    public string $status_code = '6';
    public string $xid;
    public string $notes;
    public array $items = [];
    public string $shipping_carrier;
    public string $shipping_priority;
    public string $shipping_name;
    public string $shipping_address1;
    public string $shipping_address2;
    public string $shipping_city;
    public ?string $shipping_state;
    public string $shipping_country;
    public string $shipping_zipcode;
    public string $shipping_phone;
    public string $shipping_email;
    public int $items_quantity = 0; // the total number of line items on this order

    public function setMode($mode): void
    {
        $this->mode = ($mode !== 'production') ? 'debug' : 'auto';
    }

    /**
     * @throws Exception
     */
    public function setOrder($order): void
    {
        $this->time              = date('m/d/Y h:i:s A');
        $this->fulfill_order_id  = $this->xid = $this->getReferenceId($order);
        $this->notes             = $this->getOrderNote($order);
        $this->shipping_name     = $order->customer_name;
        $this->shipping_address1 = $this->customer_address['primary'];
        $this->shipping_address2 = $this->getShippingAddress2();
        $this->shipping_city     = $order->city;
        $this->shipping_state    = $order->state;
        $this->shipping_country  = $order->country;
        $this->shipping_zipcode  = $order->postcode;
        $this->shipping_phone    = !empty($order->customer_phone) ? $order->customer_phone : self::SUPPORT_PHONE;
        $this->shipping_email    = !empty($order->customer_email) ? $order->customer_email : self::SUPPORT_EMAIL;
        $this->setShippingMethod();
    }

    private function setShippingMethod(): void
    {
        $this->shipping_carrier  = $this->mapped_shipping_methods[0];
        $this->shipping_priority = $this->mapped_shipping_methods[1];
    }

    public function setItems($product): void
    {
        $this->items[] = [
            'name'       => $product->product_name,
            'sku'        => $product->fulfill_sku,
            'quantity'   => $product->quantity,
            'attributes' => $this->getAttributes($product->files)
        ];
        $this->items_quantity++;
    }

    /**
     * @return string|null
     */
    public function getShippingAddress2()
    {
        $value = $this->customer_address['addition'] ?? '';
        if (str_contains($value, self::ORDER_NOTE_TEXT . ':')) {
            $addition = explode('(' .self::ORDER_NOTE_TEXT, $value);
            $value = trim(rtrim($addition[0], ','));
        }
        return trim($value);
    }

    private function getAttributes($files): array
    {
        $arr       = [];
        $arrSearch = [];
        $index     = 0;
        foreach ($files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );
            if (!in_array($printSpace, $arrSearch)) {
                $arr[$index]['type']           = self::ITEM_TYPE;
                $arr[$index]['location']       = $printSpace;
                $arr[$index]['file_url']       = $this->getDesignUrl($file);
                $arr[$index]['file_extension'] = $this->getExtension($file->design_url);
                $arr[$index]['preview']        = $this->getMockupUrl($file);

                $arrSearch[] = $printSpace;
                $index++;
            }
        }

        return $arr;
    }

    private function getExtension($path): string
    {
        $path = Str::before($path, '?');
        $extension = strtolower(pathinfo($path, PATHINFO_EXTENSION));
        if ($extension !== self::DEFAULT_EXTENSION_DESIGN) {
            logToDiscord(
                'Create Order to Supplier '
                . $this->supplier_id
                . ' have different path '
                . $path
                . ' . Order info: '
                . json_encode($this),
                DiscordChannel::FULFILL_ORDER
            );
        }

        return self::DEFAULT_EXTENSION_DESIGN;
    }
}
