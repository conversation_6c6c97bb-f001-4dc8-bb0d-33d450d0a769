<?php

namespace App\Providers\FulfillAPI\PrintLogistic_v2\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\PrintLogisticV2WebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    protected function setErrors($arr): void
    {
        if (Arr::get($arr, 'response_status_code') !== 200) {
            $this->errors[] = Arr::get($arr, 'status');
            $this->errors[] = Arr::get($arr, 'title');
            $this->errors[] = Arr::get($arr, 'detail');
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['response_status_code']);
    }

    public function mappingCreateOrder($arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $fulfillOrderId,
            OrderProductFulfillStatus::PENDING
        );
    }

    public function mappingCrawlOrder($arr, $fulfillOrderId): array
    {
        return (new PrintLogisticV2WebhookRepository($arr, $this->supplier_id))
            ->setSupplierOrderId($fulfillOrderId)
            ->withCrawlMode()
            ->get();
    }
}
