<?php

namespace App\Providers\FulfillAPI\Prima\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public bool $stagingOrder;
    public string $successEndpoint;
    public string $statusEndpoint;
    public string $errorEndpoint;
    public string $orderId;
    public string $orderDate;
    public array $orderIdConstructors = ["platformId", "vendorId", "orderId"];
    public string $platformId = 'SP';
    public string $vendorId = '';
    public string $affiliateId = '';
    public string $subOrganisation = '';
    public string $subOrganisationClient = '';
    public array $customer;
    public array $shippingAddress;
    public array $billingAddress;
    public array $payment;
    public array $items = [];
    public array $components;

    public function setMode($mode): void
    {
        $this->stagingOrder = $mode !== 'production';

        $url                   = getCallBackUrlForFulfill($this->supplier_id);
        $this->successEndpoint = $url;
        $this->statusEndpoint  = $url;
        $this->errorEndpoint   = $url;
    }

    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->orderId = $this->getReferenceId($order);
        $this->setOrderDate();
        $this->setCustomer($order);
        $this->setAddress($order);
    }

    private function setOrderDate(): void
    {
        // 2015-03-19T07:22:00.000Z
        $this->orderDate = date('Y-m-d\TH:i:s') . '.000Z';
    }

    private function setCustomer($order): void
    {
        $this->customer = [
            'emailAddress'   => $order->customer_email,
            'firstName'      => $this->customer_name['first_name'],
            'lastName'       => $this->customer_name['last_name'],
            'phoneNumber'    => $order->customer_phone,
            'businessNumber' => '',
        ];
    }

    private function setAddress($order): void
    {
        $this->shippingAddress = $this->billingAddress = [
            'name'         => $order->customer_name,
            'businessName' => '',
            'phone'        => $order->customer_phone,
            'street1'      => $this->customer_address['full'],
            'street2'      => $this->customer_address['house_number_text'] ?? '',
            'street3'      => $this->customer_address['mailbox_number_text'] ?? '',
            'street4'      => '',
            'city'         => $order->city,
            'postcode'     => $order->postcode,
            'state'        => $order->state,
            'country'      => $order->country,
        ];
    }

    private function setShippingMethod(): void
    {
        $this->shipping_method = $this->mapped_shipping_methods[0];
    }

    public function setItems($product): void
    {
        $this->items[] = [
            'productCode'        => $product->fulfill_sku,
            'unitQuantity'       => $product->quantity,
            'fullPriceIncTax'    => 0,
            'amountPaidIncTax'   => 0,
            'giftCardUsedIncTax' => 0,
            'pricePerUnitIncTax' => 0,
            'pagesPerUnit'       => 0,
            'files'              => $this->getFiles($product),
        ];
    }

    private function getFiles($product): array
    {
        $arr           = [];
        $arrPrintSpace = [];

        foreach ($product->files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );
            if (!in_array($printSpace, $arrPrintSpace)) {
                $arr[]           = [
                    // 'block' => 'cover',
                    'url' => $file->design_url,
                ];
                $arrPrintSpace[] = $printSpace;
            }
        }

        return $arr;
    }
}
