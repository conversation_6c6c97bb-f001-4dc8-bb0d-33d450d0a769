<?php

namespace App\Providers\FulfillAPI\Common;

use Throwable;

class ParseVariantOptionHandler
{
    /**
     * @throws Throwable
     */
    public function handle(array $variant): array
    {
        return [];
    }

    /**
     * @param array $options
     * @return array
     */
    public function parseOptions(array $options): array
    {
        return [];
    }

    /**
     * @param string $key
     * @return bool|string|null
     */
    public function standardized(string $key)
    {
        return null;
    }
}
