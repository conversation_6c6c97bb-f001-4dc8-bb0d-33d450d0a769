<?php

namespace App\Providers\FulfillAPI\LuxuryPro\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CreateOrderModel extends AbstractModel
{
    public string $uniqueRequestId;
    public array $orderItems;
    private string $skuPrefix = 'SEN-';
    public function setOrder($order): void
    {
        $this->uniqueRequestId = generateUUID();
        $this->fulfill_order_id = $this->getReferenceId($order);
    }

    public function setItems($product): void
    {
        $options = Str::isJson($product->options) ? json_decode($product->options, false) : $product->options;
        $size = correctOptionValue(data_get($options, 'size'));
        $this->orderItems[] = [
            'orderNo'   => $this->fulfill_order_id,
            'recipient' => $this->getRecipient($product->order),
            'imageUrls' => $this->getImageUrls($product),
            'product'   => [
                'spu' => $this->skuPrefix . $product->fulfill_sku,
                'size' => $this->getCorrectSize($size),
                'color' => 'white',
                'quantity' => $product->quantity,
            ],
        ];
    }

    /**
     * @param $order
     * @return array
     */
    public function getRecipient($order): array
    {
        return [
            'receiverName' => $order->customer_name,
            'phone' => $order->customer_phone ?? self::SUPPORT_PHONE,
            'email' => $order->customer_email ?? '',
            'address1' => $this->customer_address['primary'],
            'remark' => !empty($this->customer_address['addition']) ? $this->customer_address['addition'] : '.', // address 2 if have
            'state' => $this->customer_address['state'],
            'city' => $order->city,
            'postcode' => $order->postcode ?? '',
            'country' => $order->country,
        ];
    }

    /**
     * @param $product
     * @return array
     */
    private function getImageUrls($product): array
    {
        $arr = [];
        $arrPrintSpace = [];
        foreach ($product->files as $file) {
            $printSpace = $this->getByMapping($file->print_space, FulfillMappingEnum::PRINT_SPACES);
            if (!in_array($printSpace, $arrPrintSpace, true)) {
                $arr[] = $file->design_url;
                $arrPrintSpace[] = $printSpace;
            }
        }
        return $arr;
    }

    /**
     * @param $size
     * @return string
     */
    private function getCorrectSize($size)
    {
        $sizeMapping = [
            'onesize' => 'one size',
        ];
        return $sizeMapping[$size] ?? $size;
    }
}
