<?php

namespace App\Providers\FulfillAPI\Invition\Model;

use App\Providers\FulfillAPI\AbstractModel;
use App\Providers\FulfillAPI\ObjectFulfill;

class CreateOrderModel extends AbstractModel
{
    public string $partner_reference;
    public string $shipping_kind = "dropship"; // we will ship the order directly to the customer given in the address field
    public string $shipping_minimal_level;
    public object $address;
    public array $lines;

    public function setOrder($order): void
    {
        $this->fulfill_order_id = $this->partner_reference = $this->getReferenceId($order);
        $this->setAddress($order);
        $this->setShippingMethod();
    }

    public function setItems($product): void
    {
        $this->lines[] = [
            'item_sku'        => $product->fulfill_sku,
            'image_reference' => $this->getImageReference($product),
            'quantity'        => $product->quantity,
        ];
    }

    private function getImageReference($product): string
    {
        foreach ($product->files as $file) {
            $object         = new ObjectFulfill();
            $objectProvider = $object->getProviderBySupplierId($this->supplier_id);

            return $objectProvider->uploadImage($file->design_url);
        }

        throw new \RuntimeException('This order product ' . $product->id . ' don\'t have any design file');
    }

    private function setAddress($order): void
    {
        $this->address                       = (object)[];
        $this->address->firstname            = $this->customer_name['first_name'];
        $this->address->lastname             = $this->customer_name['last_name'];
        $this->address->streetname           = $this->customer_address['street'];
        $this->address->housenumber          = $this->customer_address['house_number'];
        $this->address->housenumber_addition = $this->customer_address['house_number_addition'];
        $this->address->additional_info      = $this->customer_address['addition'];
        $this->address->state                = $order->state;
        $this->address->zip                  = $order->postcode;
        $this->address->city                 = $order->city;
        $this->address->country_code         = $order->country;
        $this->address->phonenumber          = $order->customer_phone ?? '';
        $this->address->email                = $order->customer_email ?? '';
    }

    private function setShippingMethod(): void
    {
        $this->shipping_minimal_level = $this->mapped_shipping_methods[0];
    }
}
