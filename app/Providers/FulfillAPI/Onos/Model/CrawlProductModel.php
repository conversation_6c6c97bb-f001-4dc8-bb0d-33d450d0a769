<?php

namespace App\Providers\FulfillAPI\Onos\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Arr;

class CrawlProductModel extends AbstractModel
{
    protected string $productSku;
    protected array $arrKeySeparates = [
        'Age',
    ];
    public array $productOptionSeparate = [];

    public function mapping(array $product, &$arr): void
    {
        $this->productSku = $product['sku'];

        $arrOptions = [];
        foreach ($product['attributes'] as $attribute) {
            $options = $attribute['options'];
            $type = $attribute['name'];
            $this->settingOption($options, $type, $arrOptions);
        }
        $optionSeparateProduct = $this->getOptionSeparateProduct($arrOptions);

        do {
            $key = $this->productSku;
            $string = '';
            $this->productOptionSeparate = [];
            foreach ($optionSeparateProduct as $indexOption => &$optionSeparate) {
                foreach ($optionSeparate as $index => $value) {
                    if (!empty($value)) {
                        $string .= '-' . $value;
                        $this->productOptionSeparate[$indexOption] = $value;
                        unset($optionSeparate[$index]);
                        break;
                    }
                }
                if (empty($optionSeparate)) {
                    unset($optionSeparateProduct[$indexOption]);
                }
            }
            $key .= $string;
            $productName = $product['name'] . $string;

            $arr[$key]['product'] = [
                'sku'          => $key,
                'product_type' => $this->product_type,
                'supplier_id'  => $this->supplier_id,
                'name'         => $productName,
                'options'      => json_encode($arrOptions),
                'print_spaces' => $this->getPrintSpaces($product['print_areas']),
                'thumb_url'    => $product['image'],
            ];
            $arr[$key]['variants'] = $this->getVariants($product);
        } while (!empty($optionSeparateProduct));
    }

    private function getPrintSpaces($printAreas): string
    {
        $arr = [];
        foreach ($printAreas as $printArea) {
            $arr[] = [
                'name'   => $printArea['position'],
                'height' => $printArea['height'],
                'width'  => $printArea['width'],
            ];
        }

        return json_encode($arr);
    }

    private function getVariants($product): array
    {
        $model = new CrawlProductVariantModel();
        $arr = [];
        foreach ($product['attribute_specifics'] as $variant) {
            if (
                empty($variant['sku'])
                &&
                empty($variant['price'])
            ) {
                continue;
            }

            $each = $model::mapping($variant, $this);
            if (!empty($each)) {
                $arr[] = $each;
            }
        }

        return $arr;
    }

    /** @noinspection PhpMissingBreakStatementInspection */
    public function settingOption($options, $key, &$arrOptions): void
    {
        switch ($key) {
            default:
                $message = 'New option parse: ' . $key;
                $message .= "\nProduct SKU: " . $this->productSku;
                $message .= "\nSupplier: " . $this->supplier_id;
                logToDiscord(
                    $message
                    , 'fulfill_product'
                    , true
                );
                break;
            case 'Age':
                $type = $key;
                goto mapping;
            case 'Colour':
                $type = ProductOptionEnum::COLOR;
                goto mapping;
            case 'Size':
            case 'Item Size':
                $type = ProductOptionEnum::SIZE;
                goto mapping;
            case 'Pack':
                $type = ProductOptionEnum::PACK;
                goto mapping;
                mapping:

                // if product
                if (is_array($options)) {
                    foreach ($options as $option) {
                        $value = Arr::get($option, 'Value', Arr::get($option, 'value'));
                        $value = self::mappingOptions($value, $type);
                        if ($value) {
                            $arrOptions[$type][] = $value;
                        }
                    }
                    // if variant
                } else {
                    $value = self::mappingOptions($options, $type, '', true);
                    if ($value) {
                        $arrOptions[$type] = $value;
                    }
                }
        }
    }
}
