<?php

namespace App\Providers\FulfillAPI\Beeful;

use App\Providers\FulfillAPI\Beeful\Model\CrawlProductVariantModel;
use App\Providers\FulfillAPI\ObjectFulfill;
use App\Providers\FulfillAPI\Beeful\Model\CancelOrderModel;
use Illuminate\Support\Arr;
use Throwable;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/order';
    protected const ENDPOINT_CANCEL_ORDER = '/order/cancel';
    protected const ENDPOINT_CRAWL_ORDER = '/order';

    public function setProvider($provider): void
    {
        $provider = parent::setProvider($provider);
        $this->setHeader("Authorization", "Bearer {$provider['token']}");
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    /**
     * @return bool
     */
    public function shouldHaveRequestBody(): bool
    {
        return $this->method !== self::METHOD_CRAWL_PRODUCT;
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                // https://sandbox.beeful.net/doc/#!api/order.v1/create_dtf_order.md
                $this->params           = $params->getPublicAttrs();
                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->params['payload']['api_key'] = $this->token;
                if ($this->mode == 'production') {
                    $this->params['payload']['order_status'] = 'new_order';
                } else {
                    $this->params['payload']['order_status'] = 'test_order';
                }
                break;

                // https://sandbox.beeful.net/doc/#!api/order.v1/cancel_order.md
            case self::METHOD_CANCEL_ORDER:
                $supOrderId = $params instanceof CancelOrderModel ? $params->id : $params;
                $this->api  .= '/' . $supOrderId . '?api_key=' . $this->token;
                $this->fulfill_order_id = $supOrderId;
                $this->method_url = 'GET';
                break;

            case self::METHOD_CRAWL_ORDER:
                // https://sandbox.beeful.net/doc/#!api/order.v1/status_order.md
                $this->api  .= '/' . $params . '?api_key=' . $this->token;
                $this->fulfill_order_id = $params;
                $this->method_url = 'GET';
                break;
        }
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::crawlProductsAndProductVariantsJob()
     *
     * @throws \Throwable
     */
    public function crawlProductsAndProductVariantsJob(): void
    {
        $this->crawlProductsWithVariants();
    }

    /**
     * @override \App\Providers\FulfillAPI\ObjectFulfill::skipProduct()
     *
     * @param     array     $item
     *
     * @return bool
     */
    protected function skipProduct(array $item): bool
    {
        return false;
    }



    /**
     * @throws Throwable
     */
    protected function crawlVariants(array $product): array
    {
        static $variantModel;

        // Extract dữ liệu và mapping dữ liệu cho từng biến thể
        $variantModel ??= app(CrawlProductVariantModel::class);
        $result = [];
        foreach ($product['variants'] as $rawVariant) {
            if (! $this->isValidRawVariant($rawVariant, $product)) {
                $this->variantNotFoundSku($rawVariant, $product);
            } else {
                $result[] = $variantModel->mapping($rawVariant, $product);
            }
        }
        $products = [
            [...Arr::only($product, ['sku', 'product_type', 'supplier_id', 'name', 'description']), 'variants' => $result]
        ];

        return array_map($this->prepareProduct(...), $products);
    }

    /**
     * @param $params
     *
     * @return bool|string
     * @throws Throwable
     */
    public function withBody($params): bool|string
    {
        return json_encode(Arr::get($params, 'payload'), JSON_THROW_ON_ERROR | JSON_UNESCAPED_SLASHES);
    }

    /**
     * @param $sendData
     *
     * @return bool
     *
     * @throws Throwable
     */
    protected function handleCancelOrderResponse($sendData): bool
    {
        return data_get($sendData, 'status') === 'success' || (str_starts_with(data_get($sendData, 'msg'), 'Order was only canceled') && data_get($sendData, 'status') === 'error');
    }
}
