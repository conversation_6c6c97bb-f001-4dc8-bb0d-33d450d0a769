<?php

namespace App\Providers\FulfillAPI\Beeful\Model;

use App\Enums\ProductOptionEnum;
use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CrawlProductVariantModel extends AbstractModel
{
    public static function mapping(array $sku, array $options): array
    {
        $colorDetail = collect($options['colors_details'])->firstWhere('color', $sku['color']);
        $options = [
            'color' => str_replace('-', ' ', Str::lower($colorDetail['color_name'])),
            'size' => Str::lower($sku['size']),
        ];
        $options['color'] = static::mappingOptions($options['color'], ProductOptionEnum::COLOR);

        return [
            'sku' => $sku['sku'],
            'base_cost' => 0,
            'options' => $options,
            'variant_key' => getVariantKey($options),
            'out_of_stock' => $sku['status'] !== 'IN_STOCK',
        ];
    }
}
