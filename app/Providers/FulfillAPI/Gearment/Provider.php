<?php

namespace App\Providers\FulfillAPI\Gearment;

use App\Enums\ProductStatus;
use App\Models\FulfillProduct;
use App\Models\OrderProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Modules\OrderService\Models\RegionOrderProducts;

use Throwable;

class Provider extends ObjectFulfill
{
    protected const ENDPOINT_CREATE_ORDER = '/?act=order_create';
    protected const ENDPOINT_CRAWL_ORDER = '/?act=order_info';
    protected const ENDPOINT_CRAWL_PRODUCT = '/?act=products';

    protected string $api_key;
    protected string $api_signature;

    public function setProvider($provider): void
    {
        $provider            = parent::setProvider($provider);
        $this->api_key       = $provider['api_key'];
        $this->api_signature = $provider['api_signature'];
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params = ''): void
    {
        switch ($this->method) {
            case static::METHOD_CREATE_ORDER:
                $data                  = $params->getPublicAttrs();
                $data['api_key']       = $this->api_key;
                $data['api_signature'] = $this->api_signature;

                $this->params = $data;
                $this->setHeader('Content-Type', 'application/json');
                $this->fulfill_order_id = $params->getFulfillOrderId();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->params           = [
                    'api_key'       => $this->api_key,
                    'api_signature' => $this->api_signature,
                    'order_id'      => $params,
                ];
                $this->method_url       = 'GET';
                $this->fulfill_order_id = $params;
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->params = [
                    'api_key'       => $this->api_key,
                    'api_signature' => $this->api_signature,
                ];
                $this->setHeader('Content-Type', 'application/x-www-form-urlencoded');
                $this->method_url = 'GET';
                break;
        }
    }

    public function sendData($body = null, ?string $userId = null, $saveLog = true)
    {
        $client  = new Client();
        $headers = $this->headers;
        $params  = json_encode($this->params);
        $request = new Request(
            $this->method_url,
            $this->api,
            $headers,
            $params
        );
        try {
            $res  = $client->send($request);
            $body = $res->getBody()->getContents();
            $body = json_decode($body, true);
        } catch (Throwable $e) {
            $body = $e->getMessage();
        }

        return parent::sendData($body, $userId, $saveLog);
    }

    public function crawlProductsAndProductVariantsJob(): void
    {
        $objectCrawlProduct = $this;
        $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);
        $objectCrawlProduct->setParams();

        $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);

        $this->arr_new_color = [];
        $this->arr_product_id_insert = [];

        $objectProductModel = $objectCrawlProduct->getModel(self::MODEL_CRAWL_PRODUCT);
        $response           = $objectCrawlProduct->sendData(null, null, false);
        // $response = cache()->rememberForever(
        //     'crawl_variant' . $this->supplier_id,
        //     function () use ($objectCrawlProduct) {
        //         return $objectCrawlProduct->sendData(null, null, false);
        //     }
        // );

        $responseModel->mappingCrawlProducts($response);
        $data = $responseModel->getResponseData();

        $arr = [];

        foreach ($data as $each) {
            try {
                if (!$this->checkAllowCrawlProduct($each['product_name'])) {
                    continue;
                }
                $object = clone $objectProductModel;
                $product = $object->mapping($each);
                if (empty($product)) {
                    continue;
                }
                $arr[] = $product;
            } catch (Throwable $e) {
                logToDiscord(
                    'Mapping products failed.'
                    . "\nSupplier:" . $this->supplier_id
                    . "\nException:" . $e->getMessage()
                    , 'fulfill_product'
                    , true
                );
                continue;
            }
        }

        foreach ($arr as $product) {
            $productSku = $product['product']['sku'];
            if (empty($product['variants'])) {
                $product['product']['status'] = ProductStatus::INACTIVE;
            }
            $parentProduct = FulfillProduct::updateOrCreate(
                [
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ],
                $product['product']
            );
            foreach (Arr::get($product, 'variants', []) as $variant) {
                $variant['product_id'] = $parentProduct->id;
                $this->arr_variant[] = $variant;
            }

            $this->arr_product_id_insert[] = $parentProduct->id;
        }

        $this->handleAfterCrawlProduct();
        $this->handleAfterCrawlVariant();
    }

    /**
     * @override
     *
     * @param    OrderProduct|RegionOrderProducts    $op
     *
     * @return OrderProduct|RegionOrderProducts
     */
    public function correctOrderProduct(OrderProduct|RegionOrderProducts $op): OrderProduct|RegionOrderProducts
    {
        $options = json_decode($op->options, true);

        $op->options = $options;

        return $op;
    }

    /**
     * @override
     *
     * @param    array           $orderOptions
     * @param    OrderProduct|RegionOrderProducts    $op
     *
     * @return string
     */
    public function getVariantKey(array $orderOptions, OrderProduct|RegionOrderProducts $op): string
    {
        if (Str::contains($op->product_name, 'Ornament')) {
            $orderOptions = array_filter(
                $orderOptions,
                static fn($val, $key) => $key !== 'print_side', ARRAY_FILTER_USE_BOTH
            );
        }

        return getVariantKey($orderOptions);
    }
}
