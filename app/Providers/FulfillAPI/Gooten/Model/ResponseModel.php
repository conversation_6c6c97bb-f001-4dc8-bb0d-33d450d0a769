<?php

namespace App\Providers\FulfillAPI\Gooten\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\GootenWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    public function mappingCrawlProducts(array $arr): void
    {
        $data = [];
        $catalogs = $arr['product-catalog'];

        $categoriesAllowed = [
            'Trending',
            'Apparel',
            'Home &amp; Living',
            'All',
            'Clothing',
        ];
        foreach ($catalogs as $catalog) {
            $category = $catalog['name'];
            if (!in_array($category, $categoriesAllowed)) {
                continue;
            }

            foreach ($catalog['items'] as $item) {
                if (empty($item['items'])) {
                    $data[$category][] = $item;
                } else {
                    foreach ($item['items'] as $each) {
                        $data[$category][] = $each;
                    }
                }
            }
        }
        $this->response_data = $data;
    }

    public function mappingCrawlProductVariants(array $arr): void
    {
        $this->response_data = $arr['ProductVariants'];
    }

    public function getProductVariants(array $arr): array
    {
        return $this->response_product_variants = $arr['item_variants'];
    }

    public function getProductId(array $arr): string
    {
        return $this->product_id = $arr['id'];
    }

    protected function setErrors($arr): void
    {
        if (Arr::has($arr, 'HadError')) {
            $errors = Arr::get($arr, 'Errors');
            if ($errors) {
                foreach (Arr::get($arr, 'Errors') as $error) {
                    $message = Arr::get($error, 'ErrorMessage');
                    $message .= Arr::get($error, 'AttemptedValue');
                    $this->errors[] = $message;
                }
            }

            $errorCode = Arr::get($arr, 'ErrorReferenceCode');
            if ($errorCode) {
                $this->errors[] = 'ErrorReferenceCode: ' . Arr::get($arr, 'ErrorReferenceCode');
            }
        }
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['Id']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['Id'],
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['Id'])) {
            // @see docs/suppliers/Gooten/readme.txt
            return (new GootenWebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
