<?php

namespace App\Providers\FulfillAPI\Duplium\Model;

use App\Enums\FulfillMappingEnum;
use App\Providers\FulfillAPI\AbstractModel;

class CreateOrderModel extends AbstractModel
{
    public string $version = '2.1.0';
    public string $callback_uri;
    public string $order_id;
    public string $sale_datetime;
    public array $address_info = [];
    public array $sender_info = [
        'name'         => '',
        'street1'      => '',
        'street2'      => '',
        'city'         => '',
        'state'        => '',
        'postcode'     => '',
        'country_code' => '',
        'phone'        => '',
        'email'        => '',
    ];
    public array $shipping_info;
    public array $items = [];

    public function setOrder($order): void
    {
        $this->callback_uri  = getCallBackUrlForFulfill($this->supplier_id);
        $this->sale_datetime = date('Y-m-d\TH:i:s.u\Z');

        $this->fulfill_order_id = $this->order_id = $this->getReferenceId($order);
        $this->setAddressInfo($order);
        $this->setShippingInfo();
    }

    private function setAddressInfo($order): void
    {
        $this->address_info = [
            'name'         => $order->customer_name,
            'street1'      => $this->customer_address['primary'],
            'street2'      => $this->customer_address['addition'],
            'city'         => $order->city,
            'state'        => $order->state,
            'postcode'     => $order->postcode,
            'country_code' => $order->country,
            'phone'        => $order->customer_phone,
            'email'        => $order->customer_email,
        ];
    }

    private function setShippingInfo(): void
    {
        $this->shipping_info = [
            'method' => $this->mapped_shipping_methods[0],
        ];
    }

    public function setItems($product): void
    {
        $assets = $this->getAssets($product->files);

        $this->items[] = [
            'item_id'      => $this->getReferenceId($product),
            'quantity'     => $product->quantity,
            'value'        => [
                'price'    => 0,
                'currency' => $this->currencyCode,
            ],
            'assets'       => $assets,
            'product_info' => [
                'product' => $product->fulfill_sku,
            ],
        ];
    }

    private function getAssets($files): array
    {
        $arr           = [];
        $arrPrintSpace = [];

        foreach ($files as $file) {
            $printSpace = $this->getByMapping(
                $file->print_space,
                FulfillMappingEnum::PRINT_SPACES
            );

            if (!in_array($printSpace, $arrPrintSpace)) {
                $arr[]           = [
                    'description' => $printSpace,
                    'url'         => $file->design_url,
                    'preview_url' => $file->mockup_url,
                    'asset_type'  => 'image/png',
                ];
                $arrPrintSpace[] = $printSpace;
            }
        }

        return $arr;
    }
}
