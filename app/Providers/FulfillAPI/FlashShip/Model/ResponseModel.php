<?php

namespace App\Providers\FulfillAPI\FlashShip\Model;

use App\Enums\OrderProductFulfillStatus;
use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\FlashShipWebhookRepository;

class ResponseModel extends AbstractResponseModel
{
    protected int $limit_product_per_page = 250;
    protected int $totalPage;

    public function mappingCrawlProducts(array $array): void
    {
        $this->response_data = $array['data'];
        $this->totalPage = $array['pagination']['total_page'];
    }

    /**
     * @param array $arr
     *
     * @return void
     */
    public function mappingCrawlProductVariants(array $arr): void
    {
        $this->response_data = $arr['variants'] ?: [$arr['variant_default']];
    }

    /**
     * @return array
     */
    public function getParamsPagination(): array
    {
        return [
            'page'  => ++$this->page,
            'limit' => $this->limit_product_per_page,
        ];
    }

    /**
     * @return bool
     */
    public function getNexPage(): bool
    {
        return $this->page < $this->totalPage;
    }

    /**
     * @param $arr
     *
     * @return void
     */
    protected function setErrors($arr): void
    {
        $this->fulfill_order_id = (string) (data_get($arr, 'order_code', '') ?: data_get($arr, 'data', ''));

        if ( ! $this->fulfill_order_id) {
            $this->errors[] = 'Fulfill Order ID not found (' . data_get($arr, 'msg') . ')!';
        }
    }

    /**
     * @param $arr
     *
     * @return bool
     */
    protected function checkCreateOrderHasResponse($arr): bool
    {
        return data_get($arr, 'data') && data_get($arr, 'msg') === 'success';
    }

    /**
     * @param     array     $arr
     * @param               $orderProductIds
     * @param               $fulfillOrderId
     *
     * @return void
     */
    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['data'],
            OrderProductFulfillStatus::PENDING,
        );
    }

    /**
     * @param     array     $arr
     * @param               $fulfillOrderId
     *
     * @return array
     */
    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);

        if (!empty($arr['order_code'])) {
            return (new FlashShipWebhookRepository($arr, $this->supplier_id))
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
