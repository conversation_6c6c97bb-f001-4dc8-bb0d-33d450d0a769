<?php

namespace App\Providers\FulfillAPI\Printforia\Model;

use App\Providers\FulfillAPI\AbstractModel;
use Illuminate\Support\Str;

class CrawlProductVariantModel extends AbstractModel
{
    public function mapping(array $array): array
    {
        $sku = $array['sku'];
        if (Str::contains($sku, '.')) {
            $arr     = explode('.', $sku);
            $product = $arr[1];
            $color   = $arr[2];
            $size    = $arr[3];
        } else {
            $arr     = explode('-', $sku);
            $product = $arr[0] . '-' . $arr[1];
            $color   = $arr[2];
            $size    = $arr[3];
        }

        $color = self::mappingOptions($color, 'color');
        $size  = self::mappingOptions($size, 'size');

        return [
            'name'         => $product,
            'product_type' => $this->product_type,
            'supplier_id'  => $this->supplier_id,
            'sku'          => $product,
            'color'        => $color,
            'size'         => $size,
            'variant'      => [
                'sku'          => $sku,
                'variant_key'  => getVariantKey([$color, $size]),
                'out_of_stock' => self::getOutOfStock($array['stock_availability']),
            ]
        ];
    }

    protected static function getOutOfStock($status): bool
    {
        return $status !== 'available';
    }
}
