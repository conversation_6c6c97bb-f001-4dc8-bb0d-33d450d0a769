<?php

namespace App\Providers\FulfillAPI\Printforia\Model;

use App\Providers\FulfillAPI\AbstractResponseModel;
use App\Repositories\PrintforiaWebhookRepository;
use Illuminate\Support\Arr;

class ResponseModel extends AbstractResponseModel
{
    public function mappingCrawlProductVariants($arr): void
    {
        $this->response_data = $arr;
    }

    protected function setErrors($arr): void
    {
        $this->errors[] = Arr::get($arr, 'message');
    }

    protected function checkCreateOrderHasResponse($arr): bool
    {
        return !empty($arr['id']);
    }

    public function mappingCreateOrder(array $arr, $orderProductIds, $fulfillOrderId = null): void
    {
        $this->updateOrderAfterCreateWithoutProducts(
            $orderProductIds,
            $arr['id'],
        );
    }

    public function mappingCrawlOrder(array $arr, $fulfillOrderId): array
    {
        $this->handleError('Crawl Order', $arr, $fulfillOrderId);
        if (!empty($arr['id'])) {
            // @see docs/suppliers/Printforia/readme.txt
            return (new PrintforiaWebhookRepository($arr, $this->supplier_id))
                ->setSupplierOrderId($fulfillOrderId)
                ->withCrawlMode()
                ->get();
        }

        return [];
    }
}
