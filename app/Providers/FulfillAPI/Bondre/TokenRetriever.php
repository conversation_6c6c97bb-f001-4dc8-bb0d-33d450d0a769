<?php

namespace App\Providers\FulfillAPI\Bondre;

use App\Providers\FulfillAPI\Common\ConfigurableTrait;
use Illuminate\Support\Facades\Http;

class TokenRetriever extends \App\Providers\FulfillAPI\Common\TokenRetriever
{
    use ConfigurableTrait;

    /**
     * @return object
     */
    public function login(): object
    {
        return Http::asJson()->timeout(30)->post($this->tokenEndpoint(), $this->tokenPayload());
    }

    /**
     * @param object $response
     * @return string
     */
    protected function extractToken(object $response): string
    {
        return optional($response->object())->data->access_token ?? '';
    }

    /**
     * @return string
     */
    protected function tokenEndpoint(): string
    {
        return $this->config('api') . $this->config('endpoints.token');
    }

    /**
     * @return array
     */
    protected function tokenPayload(): array
    {
        return [
            'username' => $this->config('username'),
            'password' => $this->config('password')
        ];
    }
}
