<?php

namespace App\Providers\FulfillAPI\Yoycol;

use App\Models\FulfillProduct;
use App\Providers\FulfillAPI\ObjectFulfill;
use DateTime;
use DateTimeZone;
use function DeepCopy\deep_copy;

class Provider extends ObjectFulfill
{
    private string $username;
    private string $access_key;
    private string $secret_key;
    protected const ENDPOINT_CREATE_ORDER = '/v2/order/create';
    protected const ENDPOINT_CRAWL_ORDER = '/v2/order/detail'; // order/detail?orderSn={id}
    protected const ENDPOINT_CRAWL_PRODUCT = '/v2/spu/list';
    protected const ENDPOINT_CRAWL_PRODUCT_VARIANT = '/v2/spu/detail'; // spu/detail?spuCode={id}

    public function setProvider($provider): void
    {
        $provider         = parent::setProvider($provider);
        $this->username   = $provider['username'];
        $this->access_key = $provider['access_key'];
        $this->secret_key = $provider['secret_key'];
    }

    protected function setMethod($method): void
    {
        parent::setMethod($method);
        $this->setApiWithEndpoint();
    }

    public function setParams($params): void
    {
        switch ($this->method) {
            case self::METHOD_CREATE_ORDER:
                $data                 = $params->getPublicAttrs();
                $data['customerCode'] = $this->username;
                $this->params         = $data;

                $this->fulfill_order_id = $params->getFulfillOrderId();
                $this->setCustomHeaders();
                break;
            case self::METHOD_CRAWL_ORDER:
                $this->fulfill_order_id = $params;
                $params                 = [
                    'orderSn' => $params,
                ];

                $strParam         = http_build_query($params);
                $this->api        .= '?' . $strParam;
                $this->method_url = 'GET';
                $this->setCustomHeaders($strParam);
                break;
            case self::METHOD_CRAWL_PRODUCT:
                $this->method_url = 'GET';
                ksort($params);
                $strParam  = http_build_query($params);
                $this->api .= '?' . $strParam;
                $this->setCustomHeaders($strParam);
                break;
            case self::METHOD_CRAWL_PRODUCT_VARIANT:
                $this->refreshApi();
                $this->setApiWithEndpoint();
                $this->method_url = 'GET';
                $params           = [
                    'spuCode' => $params,
                ];
                $strParam         = http_build_query($params);
                $this->api        .= '?' . $strParam;
                $this->setCustomHeaders($strParam);
                break;
        }
    }

    private function setCustomHeaders(string $strParam = ''): void
    {
        $date     = $this->getDate();
        $endpoint = $this->getEndpoint();

        $str = $this->method_url . "\n";
        $str .= $endpoint . "\n";
        $str .= $strParam . "\n";
        $str .= $this->access_key . "\n";
        $str .= $date . "\n";
        $str .= 'User-Agent:curl/7.29.0' . "\n";
        $str .= 'x-custom-a:' . $this->username . "\n";

        $hash_result = hash_hmac('sha256', $str, $this->secret_key, true);
        $signature   = base64_encode($hash_result);

        $this->setHeaders([
            'X-HMAC-SIGNATURE'      => $signature,
            'X-HMAC-ALGORITHM'      => 'hmac-sha256',
            'X-HMAC-ACCESS-KEY'     => $this->access_key,
            'Date'                  => $date,
            'X-HMAC-SIGNED-HEADERS' => 'User-Agent;x-custom-a',
            'x-custom-a'            => $this->username,
            'User-Agent'            => 'curl/7.29.0',
        ]);
    }

    private function getDate(): string
    {
        $date = new DateTime();
        $date->setTimezone(new DateTimeZone('GMT'));
        return $date->format(DATE_RFC7231);
    }

    protected array $allow_crawl_products_name = [
        "All-Over Print Men's Flip Flops",
        "All-Over Print Women's Flip Flops",
        "All-Over Print Men's Classic Clogs",
        "All-Over Print Unisex Peaked Cap",
        "Baseball Cap With Flat Brim",
        "All-Over Print Men's Pullover Hoodie",
        "All-Over Print Zip Up Hoodie With Pocket",
        "All-Over Print Men's Sweatshirt",
        "All-Over Print Men's Polo Shirt",
        "Men's Cotton T-Shirt (DTF)",
        "Unisex O-neck Short Sleeve T-shirt | 180GSM Cotton (DTF)",
        "round neck sweatshirt",
        "HOODIE",
        "All-Over Print Men's O-Neck T-Shirt",
        "3SMOGB06",
        "Door Mat | Rubber",
        "Door Mat",
        'blanket',
        'Leather panel shoes',
        'All-Over Print Men\'s Classic Clogs',
        '3SMSLQ03',
        '3SWSLQ03',
        'All-Over Print Men\'s Coconut Shoes',
        'All-Over Print Women\'s Coconut Shoes'
    ];

    private array $override_gender_product_names = [
        'Unisex Classic Clog',
    ];

    public function crawlProductsAndProductVariantsJob(): void
    {
        $objectCrawlProduct = deep_copy($this);
        $objectCrawlProduct->setMethod($this::METHOD_CRAWL_PRODUCT);

        $responseModel = $objectCrawlProduct->getModel(self::MODEL_RESPONSE);

        if (method_exists($responseModel, 'getParamsPagination')) {
            $params = $responseModel->getParamsPagination();
            $objectCrawlProduct->setParams($params);
        }

        $arr                 = [];
        $index               = 0;
        $this->arr_new_color = [];
        $objectProductModel  = $objectCrawlProduct->getModel(self::MODEL_CRAWL_PRODUCT);
        while (true) {
            $response = $objectCrawlProduct->sendData(null, null, false);
            // $response = cache()->rememberForever(
            //     'crawl_product' . $this->supplier_id . $index++,
            //     function () use ($objectCrawlProduct) {
            //         return $objectCrawlProduct->sendData(null, null, false);
            //     }
            // );
            $responseModel->mappingCrawlProducts($response);
            $data = $responseModel->getResponseData();
            if (empty($data)) {
                break;
            }
            foreach ($data as $each) {
                try {
                    $object  = deep_copy($objectProductModel);
                    $product = $object->mapping($each);
                    if (!$this->checkAllowCrawlProduct($product['product'])) {
                        continue;
                    }
                    $arr[] = $product;
                } catch (\Throwable $e) {
                    logException($e, __FUNCTION__, 'fulfill_product', true);
                    continue;
                }
            }
            $params = $responseModel->getParamsPagination();
            $objectCrawlProduct->refreshApi();
            $objectCrawlProduct->setApiWithEndpoint();
            $objectCrawlProduct->setParams($params);
        }
        $this->arr_product_id_insert = [];
        $this->arr_product_sku       = [];
        $this->arr_variant           = [];
        foreach ($arr as $product) {
            $productSku    = $product['product']['sku'];
            $parentProduct = FulfillProduct::updateOrCreate(
                [
                    'sku'         => $productSku,
                    'supplier_id' => $this->supplier_id,
                ],
                $product['product']
            );
            foreach ($product['variants'] as $variant) {
                $variant['product_id'] = $parentProduct->id;
                $this->arr_variant[]   = $variant;
            }

            $this->arr_product_sku[]       = $productSku;
            $this->arr_product_id_insert[] = $parentProduct->id;
        }
        $this->handleAfterCrawlProduct();
        // $this->handleNewColor();
        $this->handleAfterCrawlVariant();
    }

    /**
     * @param string $variantKey
     * @param $orderProduct
     * @param $fulfillProduct
     * @param $isManual
     * @return string
     */
    public function mappingVariantKey(string $variantKey, $orderProduct, $fulfillProduct, $isManual): string
    {
        if (!empty($orderProduct->product_name) &&
            in_array($orderProduct->product_name, $this->override_gender_product_names)
        ) {
            $pattern = '/^(men|women)-(\d+)$/';
            if (preg_match($pattern, $variantKey, $matches)) {
                return 'white-' . $matches[2];
            }
        }
        return $variantKey;
    }
}
