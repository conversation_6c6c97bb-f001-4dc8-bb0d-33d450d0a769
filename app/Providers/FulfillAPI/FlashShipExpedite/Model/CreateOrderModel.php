<?php

namespace App\Providers\FulfillAPI\FlashShipExpedite\Model;


class CreateOrderModel extends \App\Providers\FulfillAPI\FlashShip\Model\CreateOrderModel
{
    /**
     * @param     string     $shippingMethod
     * @param     string     $countryCode
     *
     * @return int|string
     */
    public function mapShippingMethod(string $shippingMethod, string $countryCode)
    {
        return 6; // Expedite
    }
}
