<?php

namespace App\Providers;

use App\Enums\EnvironmentEnum;
use App\Traits\ApiResponse;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    use ApiResponse;

    /**
     * The path to the "home" route for your application.
     *
     * This is used by Laravel authentication to redirect users after login.
     *
     * @var string
     */
    public const HOME = '/home';

    /**
     * The controller namespace for the application.
     *
     * When present, controller route declarations will automatically be prefixed with this namespace.
     *
     * @var string|null
     */
    // protected $namespace = 'App\\Http\\Controllers';

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRateLimiting();

        $this->routes(function () {
            Route::middleware('api')
                ->namespace($this->namespace)
                ->group(function () {
                    // must use require instead of require_once
                    // because of this bug: https://stackoverflow.com/a/37335025/3872002
                    require base_path('routes/api.php');
                    require base_path('routes/admin.php');
                    require base_path('routes/seller.php');
                    require base_path('routes/public-apis.php');
                    require base_path('routes/external.php');
                    require base_path('routes/test.php');
                    require base_path('routes/sync.php');
                    require base_path('routes/protected.php');
                    require base_path('routes/supplier.php');
                });

            Route::prefix('index')
                ->middleware('web')
                ->namespace($this->namespace)
                ->group(base_path('routes/web.php'));
        });
    }

    /**
     * Configure the rate limiters for the application.
     *
     * @return void
     */
    protected function configureRateLimiting(): void
    {
        $request = app('request');
        $ip = getIp($request);
        $domain = $request->header('X-Domain') ?: 'default';
        $limitKey = sha1($domain . '|' . $ip);
        RateLimiter::for('api', function (Request $request) use ($limitKey) {
            $route = $request->route();
            $maxAttempts = 5000;
            if ($route) {
                $routeName = $route->getName();
                $routePrefix = $route->getPrefix();

                if ($routeName === 'api.activity') {
                    return Limit::none();
                }

                if (str_starts_with($routePrefix, 'public')) {
                    $maxAttempts = 10000;
                }

                if ($routeName === 'email-tracking-system.opened' || $routeName === 'email-tracking-system.clicked') {
                    return Limit::none();
                }
            }

            return Limit::perMinute($maxAttempts)->by($limitKey);
        });

        // Storefront APIS
        RateLimiter::for('email', function () use ($ip, $domain, $limitKey) {
            if (!app()->environment(EnvironmentEnum::PRODUCTION)) {
                return Limit::none();
            }

            return $ip ? Limit::perHour(5)
                ->by($limitKey)
                ->response(function () use ($domain, $ip) {
                    graylogInfo('Email rate exceeded: ' . $domain . ' / IP: ' . $ip);
                    return $this->successResponse('passed');
                })
                : Limit::none();
        });

        RateLimiter::for('report', function () use ($ip, $limitKey) {
            return $ip
                ? Limit::perHour(10)->by($limitKey)
                : Limit::none();
        });

        RateLimiter::for('create_order', function () use ($ip, $domain, $limitKey) {
            return $ip
                ? Limit::perHour(120)->by($limitKey)
                    ->response(function () use ($ip, $domain) {
                        graylogInfo('Create order rate exceeded: ' . $domain . ' / IP: ' . $ip);
                        return $this->errorResponse('Rate limit exceeded');
                    })
                : Limit::none();
        });

        RateLimiter::for('create_review', function () use ($ip, $limitKey) {
            return $ip
                ? Limit::perHour(5)->by($limitKey)
                : Limit::none();
        });

        // Seller Dashboard APIs
        RateLimiter::for('reset_password', function () use ($ip, $limitKey) {
            return $ip
                ? Limit::perHour(5)->by($limitKey)
                : Limit::none();
        });

        RateLimiter::for('request_verify_email', function () use ($ip, $limitKey) {
            $limitBy = currentUser()->getUserId() ?: $limitKey;
            return $ip
                ? Limit::perMinute(1)->by($limitBy)
                : Limit::none();
        });

        RateLimiter::for('store_payment_account', function () use ($limitKey) {
            $limitBy = currentUser()->getUserId() ?: $limitKey;
            return Limit::perHour(10)->by($limitBy);
        });

        RateLimiter::for('request_payout', function () use ($limitKey) {
            $limitBy = currentUser()->getUserId() ?: $limitKey;
            return Limit::perDay(20)->by($limitBy);
        });

        RateLimiter::for('upload', function () use ($ip, $limitKey) {
            return $ip
                ? Limit::perMinute(200)->by($limitKey)
                : Limit::none();
        });

        RateLimiter::for('related_campaigns_daily_limit', function () use ($limitKey) {
            return Limit::perDay(6000)->by($limitKey);
        });

        // AI Mockup Preview API rate limit - 15 requests per day per IP
        RateLimiter::for('ai_mockup_preview', function () use ($ip, $limitKey) {
            return $ip
                ? Limit::perDay(15)->by($limitKey)
                    ->response(function () {
                        return $this->errorResponse('Rate limit exceeded. Maximum 15 preview generations per day.', 429);
                    })
                : Limit::none();
        });
    }
}
