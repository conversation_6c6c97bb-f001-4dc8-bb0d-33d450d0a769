<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ExportOrderToExcel implements FromCollection, WithHeadings
{
    protected $orders;

    public function __construct($orders)
    {
        $this->orders = $orders;
    }

    public function collection(): Collection
    {
        return $this->orders;
    }

    public function headings(): array
    {
        return [
            'ID',
            'STORE DOMAIN',
            'ORDER NUMBER',
            'EMAIL',
            'PAID AT',
            'TOTAL',
            'PRODUCT NAME',
            'QUANTITY',
            'SKU',
            'SUPPLIER NAME',
            'EXTERNAL SKU',
            'NAME',
            'ADDRESS 1',
            'ADDRESS 2',
            'CITY',
            'STATE',
            'ZIPCODE',
            'COUNTRY',
            'PHONE',
            'NOTE',
        ];
    }
}
