<?php

namespace App\Exports\Fulfill;

use App\Enums\ProductOptionEnum;
use App\Exports\Fulfill\Sheets\ProductVariantSheet;
use App\Models\Product;
use App\Models\ProductVariant;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class ProductVariantExport implements WithMultipleSheets
{
    private $supplier_id;
    private array $options;

    public function __construct($supplierId)
    {
        $this->supplier_id = $supplierId;
        $this->options     = ProductOptionEnum::getValues();
    }

    public function sheets(): array
    {
        $sheets     = [];
        $products   = Product::query()
            ->select([
                'product.id',
                'name',
                'sku',
                'options',
            ])
            ->join('product_fulfill_mapping', function ($join) {
                $join->on('product_fulfill_mapping.product_id', '=', 'product.id');
                $join->where('product_fulfill_mapping.supplier_id', '=', $this->supplier_id);
            })
            ->orderBy('id')
            ->get();
        $productIds = $products->pluck('id');
        $seller = currentUser()->getInfoAccess();
        $variants = ProductVariant::query()
            ->onSellerConnection($seller)
            ->select([
                'product_id',
                'sku',
                'base_cost',
                'out_of_stock',
                'weight',
                'variant_key',
            ])
            ->whereIn('product_id', $productIds)
            ->orderBy('product_id')
            ->get();

        foreach ($products as $product) {
            $arr            = [];
            $productOptions = json_decode($product->options, true);
            if (empty($productOptions)) {
                continue;
            }
            sortArrayOptions($productOptions);
            $variantKeys = generateVariantKeysByProductOptions($productOptions, true);

            foreach ($variants as $keyVariant => $variant) {
                if ($variant->product_id > $product->id) {
                    break;
                }
                if ($variant->product_id !== $product->id) {
                    continue;
                }

                foreach ($variantKeys as $keyVariantKey => $variantKey) {
                    if ($variantKey['variant_key'] !== $variant->variant_key) {
                        continue;
                    }

                    $data = [
                        $product->sku,
                        $variant->sku,
                        $variant->base_cost,
                        $variant->out_of_stock,
                        $variant->weight,
                    ];
                    foreach ($this->options as $option) {
                        if (isset($variantKey[$option])) {
                            $data[] = $variantKey[$option];
                        } else {
                            $data[] = '';
                        }
                    }

                    $arr[] = $data;
                    unset($variants[$keyVariant], $variantKeys[$keyVariantKey]);
                    break;
                }
            }

            $sheets[] = new ProductVariantSheet($product->name, $arr);
        }

        return $sheets;
    }
}
