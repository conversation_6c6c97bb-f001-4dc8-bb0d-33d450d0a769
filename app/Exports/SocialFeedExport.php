<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class SocialFeedExport implements FromArray, WithHeadings
{
    protected array $arr;

    public function __construct(array $arr)
    {
        $this->arr = $arr;
    }

    public function array(): array
    {
        return $this->arr;
    }

    public function headings(): array
    {
        return [
            'id',
            'title',
            'description',
            'link',
            'image_link',
            'availability',
            'price',
            'google_product_category',
            'product_type',
            'brand',
            'identifier_exists',
            'condition',
            'age_group',
            'color',
            'gender',
            'size',
            'item_group_id',
            'custom_label_0',
            'custom_label_1',
            'custom_label_2',
            'custom_label_3',
            'custom_label_4',
            'shipping_label',
        ];
    }
}