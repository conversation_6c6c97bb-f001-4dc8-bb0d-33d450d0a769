<?php

namespace App\Traits;

use App\Models\Order;
use Illuminate\Support\Carbon;

trait ChatGptPrompt
{
    private function generateChatGptPrompt(Order $order, string $orderDetails = ''): string
    {
        $customerAddress = $order->address . ', ' . $order->city . ', ' . $order->state . ', ' . $order->country . ', ' . $order->postcode;

        // map variables
        $search = [
            '{StoreName}',
            '{CustomerName}',
            '{CustomerEmail}',
            '{CustomerPhone}',
            '{CurrentDate}',
            '{OrderNumber}',
            '{TrackingUrl}',
            '{OrderDetails}',
            '{ShippingFee}',
            '{ShippingInsurance}',
            '{OrderDate}',
            '{OrderStatus}',
            '{FulfillmentStatus}',
            '{PaidAmount}',
            '{PaymentMethod}',
            '{ShipTo}',
            '{ContactCustomerName}',
            '{ContactCustomerEmail}',
            '{ContactOrderNumber}',
            '{ContactStoreDomain}',
            '{MessageSubject}',
            '{Message}',
        ];

        $paidAmount = $order->total_paid ?? $order->total_amount;
        $currencyCode = $order->currency_code;

        // check currency
        if ($currencyCode !== 'USD') {
            $paidAmount *= $order->currency_rate;
        }

        $replace = [
            $order->store_name,
            $order->customer_name,
            $order->customer_email,
            $order->customer_phone,
            date('Y-m-d'),
            $order->order_number,
            $order->status_url,
            $orderDetails,
            $order->total_shipping_amount . ' ' . $currencyCode,
            $order->insurance_fee . ' ' . $currencyCode,
            $order->created_at->format('Y-m-d'),
            $order->status,
            $order->fulfill_status,
            $paidAmount . ' ' . $currencyCode,
            $order->payment_method,
            $customerAddress,
            !empty($this->contactData['customer_name']) ? $this->contactData['customer_name'] : 'Unknown',
            $this->contactData['customer_email'],
            $this->orderNumber,
            $this->contactData['store_domain'],
            !empty($this->contactData['subject']) ? $this->contactData['subject'] : 'Unknown',
            $this->contactData['message'],
        ];

        return str_replace($search, $replace, $this->template);
    }

    /**
     * @param Carbon|string $deliveredAt
     * @return string
     */
    private static function formatDate($deliveredAt): string
    {
        if ($deliveredAt instanceof Carbon) {
            return $deliveredAt->format('Y-m-d');
        }

        return Carbon::parse($deliveredAt)->format('Y-m-d');
    }
}
