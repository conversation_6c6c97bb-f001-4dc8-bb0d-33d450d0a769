<?php

namespace App\Traits;

use App\Enums\ContestStatusEnum;
use Carbon\Carbon;
use Exception;

trait Contest
{
    /**
     * @throws Exception
     */
    private function contestStartDate()
    {
        if (empty($this->contestStartDate)) {
            $this->contestStartDate = $this->getContestTime('contest_start_date');
        }

        return $this->contestStartDate;
    }

    /**
     * @throws Exception
     */
    private function contestEndDate()
    {
        if (empty($this->contestEndDate)) {
            $this->contestEndDate = $this->getContestTime('contest_end_date');
        }

        return $this->contestEndDate;
    }

    /**
     * @throws Exception
     */
    private function getRegistrationContestTime()
    {
        if (empty($this->registrationContestDate)) {
            $this->registrationContestDate = $this->getContestTime('contest_registration_date');
        }

        return $this->registrationContestDate;
    }

    /**
     * @param $timeKey
     * @return mixed
     */
    private function getContestTime($timeKey): mixed
    {
        $contest = \App\Models\Contest::query()
            ->where('status', ContestStatusEnum::MAIN)
            ->first();

        if (empty($contest)) {
            throw new \RuntimeException('No contest active');
        }
        $contestTime = match ($timeKey) {
            'contest_start_date' => $contest->start_time,
            'contest_end_date' => $contest->end_time,
            'contest_registration_date' => $contest->registration_date,
            default => throw new \RuntimeException('Invalid time key'),
        };
        if (!empty($contestTime)) {
            return Carbon::createFromFormat('Y-m-d H:i:s', $contestTime);
        }

        return $contestTime;
    }

    /**
     * @return bool
     */
    public function hasActiveContest()
    {
        try {
            return \App\Models\Contest::query()->where('status', ContestStatusEnum::MAIN)->exists();
        } catch (\Throwable $e) {
            return false;
        }
    }

    /**
     * @return bool
     */
    public function isActiveContestNeedConfirmToJoin()
    {
        try {
            return (bool) \App\Models\Contest::query()->where('status', ContestStatusEnum::MAIN)->value('need_join_contest');
        } catch (\Throwable $e) {
            return false;
        }
    }
}
