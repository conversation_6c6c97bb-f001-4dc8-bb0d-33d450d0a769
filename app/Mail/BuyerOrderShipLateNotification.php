<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BuyerOrderShipLateNotification extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $data;
    /**
     * Create a new message instance.
     *
     * @param null $data
     */
    public function __construct($data = null)
    {
        if (!is_null($data)) {
            $this->data = $data;
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.buyer.order_ship_late_notification', $this->data)
            ->subject($this->data['subject']);
    }
}
