<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

class StoreContactSeller extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $data;

    /**
     * Create a new message instance.
     *
     * @param null $data
     */
    public function __construct($data = null)
    {
        if (!is_null($data)) {
            $this->data = $data;
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        $replyToAddress = Arr::get($this->data, 'email', '<EMAIL>');
        $replyToName = Arr::get($this->data, 'name', 'Customer');
        return $this->markdown('emails.store.contact_seller', $this->data)
            ->replyTo($replyToAddress, $replyToName)
            ->subject($this->data['subject']);
    }
}
