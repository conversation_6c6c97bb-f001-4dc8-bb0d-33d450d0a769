<?php

namespace App\Mail;

use App\Enums\TelegramUserIdEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Throwable;

class SellerOrderNotificationNew extends Mailable
{
    use Queueable;
    use SerializesModels;

    public $data;

    /**
     * Create a new message instance.
     *
     * @param null $data
     */
    public function __construct($data = null)
    {
        if (!is_null($data)) {
            $this->data = $data;
        }
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->markdown('emails.seller.new_order', $this->data)
            ->subject($this->data['subject']);
    }

    public function failed(Throwable $ex) {
        logToTelegramNow(TelegramUserIdEnum::JAMES, $ex->getMessage()); #TODO: JAMES debug
    }
}
