<?php

namespace App\Observers;

use App\Models\EventLogs;
use App\Models\IndexEventLogs;

class EventLogsObserver
{
    /**
     * Handle the EventLogs "created" event.
     *
     * @param EventLogs $eventLogs
     * @return void
     */
    public function created(EventLogs $eventLogs)
    {
        $exists = IndexEventLogs::query()->whereKey($eventLogs->id)->exists();
        if (!$exists) {
            IndexEventLogs::query()->insert($eventLogs->toArray());
        }
    }

    /**
     * Handle the EventLogs "updated" event.
     *
     * @param EventLogs $eventLogs
     * @return void
     */
    public function updated(EventLogs $eventLogs)
    {
        $exists = IndexEventLogs::query()->whereKey($eventLogs->id)->exists();
        if ($exists) {
            IndexEventLogs::query()->whereKey($eventLogs->id)->update($eventLogs->toArray());
        }
    }

    /**
     * Handle the EventLogs "deleted" event.
     *
     * @param EventLogs $eventLogs
     * @return void
     */
    public function deleted(EventLogs $eventLogs)
    {
        $exists = IndexEventLogs::query()->whereKey($eventLogs->id)->exists();
        if ($exists) {
            IndexEventLogs::query()->whereKey($eventLogs->id)->delete();
        }
    }

    /**
     * Handle the EventLogs "restored" event.
     *
     * @param EventLogs $eventLogs
     * @return void
     */
    public function restored(EventLogs $eventLogs)
    {
        $exists = IndexEventLogs::query()->whereKey($eventLogs->id)->exists();
        if ($exists) {
            IndexEventLogs::query()->whereKey($eventLogs->id)->update($eventLogs->toArray());
        }
    }

    /**
     * Handle the EventLogs "force deleted" event.
     *
     * @param EventLogs $eventLogs
     * @return void
     */
    public function forceDeleted(EventLogs $eventLogs)
    {
        $exists = IndexEventLogs::query()->whereKey($eventLogs->id)->exists();
        if ($exists) {
            IndexEventLogs::query()->whereKey($eventLogs->id)->forceDelete();
        }
    }
}
