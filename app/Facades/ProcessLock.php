<?php

namespace App\Facades;

use App\Models\ProcessLocks;
use App\Services\ProcessLockService;
use Illuminate\Support\Facades\Facade;

/**
 * @method static bool has($key)
 * @method static ProcessLocks|null get($key)
 * @method static bool handle($key, $callback, int $max_process = 1, int $ttl = 300)
 * @method static bool put($key, int $max_process = 1, $ttl = null)
 * @method static ProcessLocks|bool add($key, int $max_process = 1, $ttl = null)
 * @method static bool forever($key, $max_process = 1)
 * @method static bool release($key)
 * @method static bool forgetIfExpired($key)
 * @method static bool flushExpired()
 *
 * @see ProcessLockService
 */
class ProcessLock extends Facade
{
    protected static function getFacadeAccessor()
    {
        return 'process-lock';
    }
}
