<?php
namespace App\Enums;
use BenSampo\Enum\Enum;
final class QueueName extends Enum
{
    public const DEFAULT = 'default';

    public const ORDER_EVENTS = 'order-events';

    public const ORDER_STATISTICS = 'order-statistics';

    public const VALIDATE_FULFILL = 'validate_fulfill';

    public const SYNC_SUPPLIER_OOS = 'sync-supplier-oos';

    public const BULK_CAMPAIGN = 'bulk-campaign';

    public const CRAWL_IMAGE = 'crawl-image';

    public const CRAWL_FULFILL = 'crawl-fulfill';

    public const CRAWL_TRACKING_STATUS = 'crawl-tracking-status';

    public const SOCIAL_FEED = 'social-feed';

    public const LARGE_SOCIAL_FEED = 'large-social-feed';

    public const FULFILL = 'fulfill';

    public const FULFILL_FIX_IP = 'fulfill-fix-ip';

    public const ORDER = 'order';

    public const PROCESS_PAYMENT = 'process-payment';

    public const LOW_PRIORITY = 'low-priority';

    public const LARGE_MEMORY = 'large-memory';

    public const CRISP_BOT = 'crisp_bot';

    public const SELLER_SUPPORT = 'seller_support';
}
