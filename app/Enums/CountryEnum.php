<?php
namespace App\Enums;

use BenSampo\Enum\Enum;
use Illuminate\Support\Str;
use RuntimeException;

final class CountryEnum extends Enum
{
    public const EU_COUNTRIES = [
        'AT', // Austria
        'BE', // Belgium
        'BG', // Bulgaria
        'HR', // Croatia
        'CY', // Republic of Cyprus
        'CZ', // Czech Republic
        'DK', // Denmark
        'EE', // Estonia
        'FI', // Finland
        'FR', // France
        'DE', // Germany
        'GR', // Greece
        'HU', // Hungary
        'IE', // Ireland
        'IT', // Italy
        'LV', // Latvia
        'LT', // Lithuania
        'LU', // Luxembourg
        'MT', // Malta
        'NL', // Netherlands
        'PL', // Poland
        'PT', // Portugal
        'RO', // Romania
        'SK', // Slovakia
        'SI', // Slovenia
        'ES', // Spain
        'SE', // Sweden
        'UK', // United Kingdom
        '150', // Europe
    ];

    /**
     * @param    string    $countryCode
     *
     * @return string
     */
    public static function isUsOrEu(string $countryCode): string
    {
        return in_array($countryCode, ['US', ...self::EU_COUNTRIES], true);
    }

    /**
     * @param    string    $countryCode
     *
     * @return string
     */
    public static function isEu(string $countryCode): string
    {
        return in_array($countryCode, self::EU_COUNTRIES, true);
    }

    /**
     * @param    string    $countryCode
     *
     * @return string
     */
    public static function isUs(string $countryCode): string
    {
        return $countryCode === 'US';
    }

    /**
     * @param     string     $stateName
     *
     * @return string|null
     */
    public static function toStateCode(string $countryCode, string $stateName): ?string
    {
        $stateName = Str::lower($stateName);

        switch (Str::upper($countryCode)) {
            case 'US':
                $code = [
                'alabama'                          => 'AL',
                'alaska'                           => 'AK',
                'arizona'                          => 'AZ',
                'arkansas'                         => 'AR',
                'california'                       => 'CA',
                'colorado'                         => 'CO',
                'connecticut'                      => 'CT',
                'delaware'                         => 'DE',
                'district of columbia'             => 'DC',
                'florida'                          => 'FL',
                'georgia'                          => 'GA',
                'hawaii'                           => 'HI',
                'idaho'                            => 'ID',
                'illinois'                         => 'IL',
                'indiana'                          => 'IN',
                'iowa'                             => 'IA',
                'kansas'                           => 'KS',
                'kentucky'                         => 'KY',
                'louisiana'                        => 'LA',
                'maine'                            => 'ME',
                'maryland'                         => 'MD',
                'massachusetts'                    => 'MA',
                'michigan'                         => 'MI',
                'minnesota'                        => 'MN',
                'mississippi'                      => 'MS',
                'missouri'                         => 'MO',
                'montana'                          => 'MT',
                'nebraska'                         => 'NE',
                'nevada'                           => 'NV',
                'new hampshire'                    => 'NH',
                'new jersey'                       => 'NJ',
                'new mexico'                       => 'NM',
                'new york'                         => 'NY',
                'north carolina'                   => 'NC',
                'north dakota'                     => 'ND',
                'ohio'                             => 'OH',
                'oklahoma'                         => 'OK',
                'oregon'                           => 'OR',
                'pennsylvania'                     => 'PA',
                'rhode island'                     => 'RI',
                'south carolina'                   => 'SC',
                'south dakota'                     => 'SD',
                'tennessee'                        => 'TN',
                'texas'                            => 'TX',
                'utah'                             => 'UT',
                'vermont'                          => 'VT',
                'virginia'                         => 'VA',
                'washington'                       => 'WA',
                'west virginia'                    => 'WV',
                'wisconsin'                        => 'WI',
                'wyoming'                          => 'WY',
                ][$stateName] ?? null;
                break;

            case 'CA':
                $code = [
                'alberta'                          => 'AB',
                'british columbia'                 => 'BC',
                'manitoba'                         => 'MB',
                'new brunswick'                    => 'NB',
                'newfoundland and labrador'        => 'NL',
                'northwest territories'            => 'NT',
                'nova scotia'                      => 'NS',
                'nunavut'                          => 'NU',
                'ontario'                          => 'ON',
                'prince edward island'             => 'PE',
                'quebec'                           => 'QC',
                'saskatchewan'                     => 'SK',
                'yukon'                            => 'YT',
                ][$stateName] ?? null;
                break;

            default:
                throw new RuntimeException('Country code not found');
        }

        return $code;
    }

    /**
     * @param $countryCode
     * @param $stateCode
     * @return mixed
     */
    public static function toStateName($countryCode, $stateCode) {
        $MAP = [
            "US" => [
                [
                    "text" => "Alabama",
                    "value" => "AL",
                ],
                [
                    "text" => "Alaska",
                    "value" => "AK",
                ],
                [
                    "text" => "American Samoa",
                    "value" => "AS",
                ],
                [
                    "text" => "Arizona",
                    "value" => "AZ",
                ],
                [
                    "text" => "Arkansas",
                    "value" => "AR",
                ],
                [
                    "text" => "Baker Island",
                    "value" => "UM-81",
                ],
                [
                    "text" => "California",
                    "value" => "CA",
                ],
                [
                    "text" => "Colorado",
                    "value" => "CO",
                ],
                [
                    "text" => "Connecticut",
                    "value" => "CT",
                ],
                [
                    "text" => "Delaware",
                    "value" => "DE",
                ],
                [
                    "text" => "District of Columbia",
                    "value" => "DC",
                ],
                [
                    "text" => "Florida",
                    "value" => "FL",
                ],
                [
                    "text" => "Georgia",
                    "value" => "GA",
                ],
                [
                    "text" => "Guam",
                    "value" => "GU",
                ],
                [
                    "text" => "Hawaii",
                    "value" => "HI",
                ],
                [
                    "text" => "Howland Island",
                    "value" => "UM-84",
                ],
                [
                    "text" => "Idaho",
                    "value" => "ID",
                ],
                [
                    "text" => "Illinois",
                    "value" => "IL",
                ],
                [
                    "text" => "Indiana",
                    "value" => "IN",
                ],
                [
                    "text" => "Iowa",
                    "value" => "IA",
                ],
                [
                    "text" => "Jarvis Island",
                    "value" => "UM-86",
                ],
                [
                    "text" => "Johnston Atoll",
                    "value" => "UM-67",
                ],
                [
                    "text" => "Kansas",
                    "value" => "KS",
                ],
                [
                    "text" => "Kentucky",
                    "value" => "KY",
                ],
                [
                    "text" => "Kingman Reef",
                    "value" => "UM-89",
                ],
                [
                    "text" => "Louisiana",
                    "value" => "LA",
                ],
                [
                    "text" => "Maine",
                    "value" => "ME",
                ],
                [
                    "text" => "Maryland",
                    "value" => "MD",
                ],
                [
                    "text" => "Massachusetts",
                    "value" => "MA",
                ],
                [
                    "text" => "Michigan",
                    "value" => "MI",
                ],
                [
                    "text" => "Midway Atoll",
                    "value" => "UM-71",
                ],
                [
                    "text" => "Minnesota",
                    "value" => "MN",
                ],
                [
                    "text" => "Mississippi",
                    "value" => "MS",
                ],
                [
                    "text" => "Missouri",
                    "value" => "MO",
                ],
                [
                    "text" => "Montana",
                    "value" => "MT",
                ],
                [
                    "text" => "Navassa Island",
                    "value" => "UM-76",
                ],
                [
                    "text" => "Nebraska",
                    "value" => "NE",
                ],
                [
                    "text" => "Nevada",
                    "value" => "NV",
                ],
                [
                    "text" => "New Hampshire",
                    "value" => "NH",
                ],
                [
                    "text" => "New Jersey",
                    "value" => "NJ",
                ],
                [
                    "text" => "New Mexico",
                    "value" => "NM",
                ],
                [
                    "text" => "New York",
                    "value" => "NY",
                ],
                [
                    "text" => "North Carolina",
                    "value" => "NC",
                ],
                [
                    "text" => "North Dakota",
                    "value" => "ND",
                ],
                [
                    "text" => "Northern Mariana Islands",
                    "value" => "MP",
                ],
                [
                    "text" => "Ohio",
                    "value" => "OH",
                ],
                [
                    "text" => "Oklahoma",
                    "value" => "OK",
                ],
                [
                    "text" => "Oregon",
                    "value" => "OR",
                ],
                [
                    "text" => "Palmyra Atoll",
                    "value" => "UM-95",
                ],
                [
                    "text" => "Pennsylvania",
                    "value" => "PA",
                ],
                [
                    "text" => "Puerto Rico",
                    "value" => "PR",
                ],
                [
                    "text" => "Rhode Island",
                    "value" => "RI",
                ],
                [
                    "text" => "South Carolina",
                    "value" => "SC",
                ],
                [
                    "text" => "South Dakota",
                    "value" => "SD",
                ],
                [
                    "text" => "Tennessee",
                    "value" => "TN",
                ],
                [
                    "text" => "Texas",
                    "value" => "TX",
                ],
                [
                    "text" => "United States Minor Outlying Islands",
                    "value" => "UM",
                ],
                [
                    "text" => "United States Virgin Islands",
                    "value" => "VI",
                ],
                [
                    "text" => "Utah",
                    "value" => "UT",
                ],
                [
                    "text" => "Vermont",
                    "value" => "VT",
                ],
                [
                    "text" => "Virginia",
                    "value" => "VA",
                ],
                [
                    "text" => "Wake Island",
                    "value" => "UM-79",
                ],
                [
                    "text" => "Washington",
                    "value" => "WA",
                ],
                [
                    "text" => "West Virginia",
                    "value" => "WV",
                ],
                [
                    "text" => "Wisconsin",
                    "value" => "WI",
                ],
                [
                    "text" => "Wyoming",
                    "value" => "WY",
                ],
            ],
            "AU" => [
                [
                    "text" => "Australian Capital Territory",
                    "value" => "ACT",
                ],
                [
                    "text" => "New South Wales",
                    "value" => "NSW",
                ],
                [
                    "text" => "Northern Territory",
                    "value" => "NT",
                ],
                [
                    "text" => "Queensland",
                    "value" => "QLD",
                ],
                [
                    "text" => "South Australia",
                    "value" => "SA",
                ],
                [
                    "text" => "Tasmania",
                    "value" => "TAS",
                ],
                [
                    "text" => "Victoria",
                    "value" => "VIC",
                ],
                [
                    "text" => "Western Australia",
                    "value" => "WA",
                ],
            ],
            "NZ" => [
                [
                    "text" => "Auckland Region",
                    "value" => "AUK",
                ],
                [
                    "text" => "Bay of Plenty Region",
                    "value" => "BOP",
                ],
                [
                    "text" => "Canterbury Region",
                    "value" => "CAN",
                ],
                [
                    "text" => "Chatham Islands",
                    "value" => "CIT",
                ],
                [
                    "text" => "Gisborne District",
                    "value" => "GIS",
                ],
                [
                    "text" => "Hawke's Bay Region",
                    "value" => "HKB",
                ],
                [
                    "text" => "Manawatu-Wanganui Region",
                    "value" => "MWT",
                ],
                [
                    "text" => "Marlborough Region",
                    "value" => "MBH",
                ],
                [
                    "text" => "Nelson Region",
                    "value" => "NSN",
                ],
                [
                    "text" => "Northland Region",
                    "value" => "NTL",
                ],
                [
                    "text" => "Otago Region",
                    "value" => "OTA",
                ],
                [
                    "text" => "Southland Region",
                    "value" => "STL",
                ],
                [
                    "text" => "Taranaki Region",
                    "value" => "TKI",
                ],
                [
                    "text" => "Tasman District",
                    "value" => "TAS",
                ],
                [
                    "text" => "Waikato Region",
                    "value" => "WKO",
                ],
                [
                    "text" => "Wellington Region",
                    "value" => "WGN",
                ],
                [
                    "text" => "West Coast Region",
                    "value" => "WTC",
                ],
            ],
            "IT" => [
                [
                    "text" => "Abruzzo",
                    "value" => "65",
                ],
                [
                    "text" => "Aosta Valley",
                    "value" => "23",
                ],
                [
                    "text" => "Apulia",
                    "value" => "75",
                ],
                [
                    "text" => "Basilicata",
                    "value" => "77",
                ],
                [
                    "text" => "Benevento Province",
                    "value" => "BN",
                ],
                [
                    "text" => "Calabria",
                    "value" => "78",
                ],
                [
                    "text" => "Campania",
                    "value" => "72",
                ],
                [
                    "text" => "Emilia-Romagna",
                    "value" => "45",
                ],
                [
                    "text" => "Friuli–Venezia Giulia",
                    "value" => "36",
                ],
                [
                    "text" => "Lazio",
                    "value" => "62",
                ],
                [
                    "text" => "Agrigento",
                    "value" => "AG",
                ],
                [
                    "text" => "Caltanissetta",
                    "value" => "CL",
                ],
                [
                    "text" => "Enna",
                    "value" => "EN",
                ],
                [
                    "text" => "Ragusa",
                    "value" => "RG",
                ],
                [
                    "text" => "Siracusa",
                    "value" => "SR",
                ],
                [
                    "text" => "Trapani",
                    "value" => "TP",
                ],
                [
                    "text" => "Liguria",
                    "value" => "42",
                ],
                [
                    "text" => "Lombardy",
                    "value" => "25",
                ],
                [
                    "text" => "Marche",
                    "value" => "57",
                ],
                [
                    "text" => "Bari",
                    "value" => "BA",
                ],
                [
                    "text" => "Bologna",
                    "value" => "BO",
                ],
                [
                    "text" => "Cagliari",
                    "value" => "CA",
                ],
                [
                    "text" => "Catania",
                    "value" => "CT",
                ],
                [
                    "text" => "Florence",
                    "value" => "FI",
                ],
                [
                    "text" => "Genoa",
                    "value" => "GE",
                ],
                [
                    "text" => "Messina",
                    "value" => "ME",
                ],
                [
                    "text" => "Milan",
                    "value" => "MI",
                ],
                [
                    "text" => "Naples",
                    "value" => "NA",
                ],
                [
                    "text" => "Palermo",
                    "value" => "PA",
                ],
                [
                    "text" => "Reggio Calabria",
                    "value" => "RC",
                ],
                [
                    "text" => "Rome",
                    "value" => "RM",
                ],
                [
                    "text" => "Turin",
                    "value" => "TO",
                ],
                [
                    "text" => "Venice",
                    "value" => "VE",
                ],
                [
                    "text" => "Molise",
                    "value" => "67",
                ],
                [
                    "text" => "Pesaro and Urbino Province",
                    "value" => "PU",
                ],
                [
                    "text" => "Piedmont",
                    "value" => "21",
                ],
                [
                    "text" => "Alessandria",
                    "value" => "AL",
                ],
                [
                    "text" => "Ancona",
                    "value" => "AN",
                ],
                [
                    "text" => "Ascoli Piceno",
                    "value" => "AP",
                ],
                [
                    "text" => "Asti",
                    "value" => "AT",
                ],
                [
                    "text" => "Avellino",
                    "value" => "AV",
                ],
                [
                    "text" => "Barletta-Andria-Trani",
                    "value" => "BT",
                ],
                [
                    "text" => "Belluno",
                    "value" => "BL",
                ],
                [
                    "text" => "Bergamo",
                    "value" => "BG",
                ],
                [
                    "text" => "Biella",
                    "value" => "BI",
                ],
                [
                    "text" => "Brescia",
                    "value" => "BS",
                ],
                [
                    "text" => "Brindisi",
                    "value" => "BR",
                ],
                [
                    "text" => "Campobasso",
                    "value" => "CB",
                ],
                [
                    "text" => "Carbonia-Iglesias",
                    "value" => "CI",
                ],
                [
                    "text" => "Caserta",
                    "value" => "CE",
                ],
                [
                    "text" => "Catanzaro",
                    "value" => "CZ",
                ],
                [
                    "text" => "Chieti",
                    "value" => "CH",
                ],
                [
                    "text" => "Como",
                    "value" => "CO",
                ],
                [
                    "text" => "Cosenza",
                    "value" => "CS",
                ],
                [
                    "text" => "Cremona",
                    "value" => "CR",
                ],
                [
                    "text" => "Crotone",
                    "value" => "KR",
                ],
                [
                    "text" => "Cuneo",
                    "value" => "CN",
                ],
                [
                    "text" => "Fermo",
                    "value" => "FM",
                ],
                [
                    "text" => "Ferrara",
                    "value" => "FE",
                ],
                [
                    "text" => "Foggia",
                    "value" => "FG",
                ],
                [
                    "text" => "Forlì-Cesena",
                    "value" => "FC",
                ],
                [
                    "text" => "Frosinone",
                    "value" => "FR",
                ],
                [
                    "text" => "Gorizia",
                    "value" => "GO",
                ],
                [
                    "text" => "Grosseto",
                    "value" => "GR",
                ],
                [
                    "text" => "Imperia",
                    "value" => "IM",
                ],
                [
                    "text" => "Isernia",
                    "value" => "IS",
                ],
                [
                    "text" => "L'Aquila",
                    "value" => "AQ",
                ],
                [
                    "text" => "La Spezia",
                    "value" => "SP",
                ],
                [
                    "text" => "Latina",
                    "value" => "LT",
                ],
                [
                    "text" => "Lecce",
                    "value" => "LE",
                ],
                [
                    "text" => "Lecco",
                    "value" => "LC",
                ],
                [
                    "text" => "Livorno",
                    "value" => "LI",
                ],
                [
                    "text" => "Lodi",
                    "value" => "LO",
                ],
                [
                    "text" => "Lucca",
                    "value" => "LU",
                ],
                [
                    "text" => "Macerata",
                    "value" => "MC",
                ],
                [
                    "text" => "Mantua",
                    "value" => "MN",
                ],
                [
                    "text" => "Massa and Carrara",
                    "value" => "MS",
                ],
                [
                    "text" => "Matera",
                    "value" => "MT",
                ],
                [
                    "text" => "Medio Campidano",
                    "value" => "VS",
                ],
                [
                    "text" => "Modena",
                    "value" => "MO",
                ],
                [
                    "text" => "Monza and Brianza",
                    "value" => "MB",
                ],
                [
                    "text" => "Novara",
                    "value" => "NO",
                ],
                [
                    "text" => "Nuoro",
                    "value" => "NU",
                ],
                [
                    "text" => "Ogliastra",
                    "value" => "OG",
                ],
                [
                    "text" => "Olbia-Tempio",
                    "value" => "OT",
                ],
                [
                    "text" => "Oristano",
                    "value" => "OR",
                ],
                [
                    "text" => "Padua",
                    "value" => "PD",
                ],
                [
                    "text" => "Parma",
                    "value" => "PR",
                ],
                [
                    "text" => "Pavia",
                    "value" => "PV",
                ],
                [
                    "text" => "Perugia",
                    "value" => "PG",
                ],
                [
                    "text" => "Pescara",
                    "value" => "PE",
                ],
                [
                    "text" => "Piacenza",
                    "value" => "PC",
                ],
                [
                    "text" => "Pisa",
                    "value" => "PI",
                ],
                [
                    "text" => "Pistoia",
                    "value" => "PT",
                ],
                [
                    "text" => "Pordenone",
                    "value" => "PN",
                ],
                [
                    "text" => "Potenza",
                    "value" => "PZ",
                ],
                [
                    "text" => "Prato",
                    "value" => "PO",
                ],
                [
                    "text" => "Ravenna",
                    "value" => "RA",
                ],
                [
                    "text" => "Reggio Emilia",
                    "value" => "RE",
                ],
                [
                    "text" => "Rieti",
                    "value" => "RI",
                ],
                [
                    "text" => "Rimini",
                    "value" => "RN",
                ],
                [
                    "text" => "Rovigo",
                    "value" => "RO",
                ],
                [
                    "text" => "Salerno",
                    "value" => "SA",
                ],
                [
                    "text" => "Sassari",
                    "value" => "SS",
                ],
                [
                    "text" => "Savona",
                    "value" => "SV",
                ],
                [
                    "text" => "Siena",
                    "value" => "SI",
                ],
                [
                    "text" => "Sondrio",
                    "value" => "SO",
                ],
                [
                    "text" => "Taranto",
                    "value" => "TA",
                ],
                [
                    "text" => "Teramo",
                    "value" => "TE",
                ],
                [
                    "text" => "Terni",
                    "value" => "TR",
                ],
                [
                    "text" => "Treviso",
                    "value" => "TV",
                ],
                [
                    "text" => "Trieste",
                    "value" => "TS",
                ],
                [
                    "text" => "Udine",
                    "value" => "UD",
                ],
                [
                    "text" => "Varese",
                    "value" => "VA",
                ],
                [
                    "text" => "Verbano-Cusio-Ossola",
                    "value" => "VB",
                ],
                [
                    "text" => "Vercelli",
                    "value" => "VC",
                ],
                [
                    "text" => "Verona",
                    "value" => "VR",
                ],
                [
                    "text" => "Vibo Valentia",
                    "value" => "VV",
                ],
                [
                    "text" => "Vicenza",
                    "value" => "VI",
                ],
                [
                    "text" => "Viterbo",
                    "value" => "VT",
                ],
                [
                    "text" => "Sardinia",
                    "value" => "88",
                ],
                [
                    "text" => "Sicily",
                    "value" => "82",
                ],
                [
                    "text" => "South Tyrol",
                    "value" => "BZ",
                ],
                [
                    "text" => "Trentino",
                    "value" => "TN",
                ],
                [
                    "text" => "Trentino-South Tyrol",
                    "value" => "32",
                ],
                [
                    "text" => "Tuscany",
                    "value" => "52",
                ],
                [
                    "text" => "Umbria",
                    "value" => "55",
                ],
                [
                    "text" => "Veneto",
                    "value" => "34",
                ],
            ],
            "CA" => [
                [
                    "text" => "Alberta",
                    "value" => "AB",
                ],
                [
                    "text" => "British Columbia",
                    "value" => "BC",
                ],
                [
                    "text" => "Manitoba",
                    "value" => "MB",
                ],
                [
                    "text" => "New Brunswick",
                    "value" => "NB",
                ],
                [
                    "text" => "Newfoundland and Labrador",
                    "value" => "NL",
                ],
                [
                    "text" => "Northwest Territories",
                    "value" => "NT",
                ],
                [
                    "text" => "Nova Scotia",
                    "value" => "NS",
                ],
                [
                    "text" => "Nunavut",
                    "value" => "NU",
                ],
                [
                    "text" => "Ontario",
                    "value" => "ON",
                ],
                [
                    "text" => "Prince Edward Island",
                    "value" => "PE",
                ],
                [
                    "text" => "Quebec",
                    "value" => "QC",
                ],
                [
                    "text" => "Saskatchewan",
                    "value" => "SK",
                ],
                [
                    "text" => "Yukon",
                    "value" => "YT",
                ],
            ],
            "BR" => [
                [
                    "text" => "Acre",
                    "value" => "AC",
                ],
                [
                    "text" => "Alagoas",
                    "value" => "AL",
                ],
                [
                    "text" => "Amapá",
                    "value" => "AP",
                ],
                [
                    "text" => "Amazonas",
                    "value" => "AM",
                ],
                [
                    "text" => "Bahia",
                    "value" => "BA",
                ],
                [
                    "text" => "Ceará",
                    "value" => "CE",
                ],
                [
                    "text" => "Distrito Federal",
                    "value" => "DF",
                ],
                [
                    "text" => "Espírito Santo",
                    "value" => "ES",
                ],
                [
                    "text" => "Goiás",
                    "value" => "GO",
                ],
                [
                    "text" => "Maranhão",
                    "value" => "MA",
                ],
                [
                    "text" => "Mato Grosso",
                    "value" => "MT",
                ],
                [
                    "text" => "Mato Grosso do Sul",
                    "value" => "MS",
                ],
                [
                    "text" => "Minas Gerais",
                    "value" => "MG",
                ],
                [
                    "text" => "Pará",
                    "value" => "PA",
                ],
                [
                    "text" => "Paraíba",
                    "value" => "PB",
                ],
                [
                    "text" => "Paraná",
                    "value" => "PR",
                ],
                [
                    "text" => "Pernambuco",
                    "value" => "PE",
                ],
                [
                    "text" => "Piauí",
                    "value" => "PI",
                ],
                [
                    "text" => "Rio de Janeiro",
                    "value" => "RJ",
                ],
                [
                    "text" => "Rio Grande do Norte",
                    "value" => "RN",
                ],
                [
                    "text" => "Rio Grande do Sul",
                    "value" => "RS",
                ],
                [
                    "text" => "Rondônia",
                    "value" => "RO",
                ],
                [
                    "text" => "Roraima",
                    "value" => "RR",
                ],
                [
                    "text" => "Santa Catarina",
                    "value" => "SC",
                ],
                [
                    "text" => "São Paulo",
                    "value" => "SP",
                ],
                [
                    "text" => "Sergipe",
                    "value" => "SE",
                ],
                [
                    "text" => "Tocantins",
                    "value" => "TO",
                ],
            ],
        ];

        foreach ($MAP as $country => $states) {
            if ($country == $countryCode) {
                foreach ($states as $state) {
                    if ($state['value'] == $stateCode) {
                        return $state['text'];
                    }
                }
            }
        }
        return $stateCode;
    }
}
