<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class FulfillmentStatusEnum extends Enum
{
    // enum('unfulfilled', 'fulfilled', 'partial_fulfilled', 'cancelled', 'processing', 'on_hold', 'rejected', 'exception', 'invalid', 'out_of_stock', 'no_ship', 'pending', 'reviewing', 'designing', 'on_delivery')
    public const PENDING = 'pending';
    public const FULFILLED = 'fulfilled';
    public const PARTIAL_FULFILLED = 'partial_fulfilled';
    public const CANCELLED = 'cancelled';
    public const UNFULFILLED = 'unfulfilled';
    public const PROCESSING = 'processing';
    public const ON_HOLD = 'on_hold';
    public const REJECTED = 'rejected';
    public const EXCEPTION = 'exception';
    public const INVALID = 'invalid';
    public const OUT_OF_STOCK = 'out_of_stock';
    public const NO_SHIP = 'no_ship';
    public const REVIEWING = 'reviewing';
    public const DESIGNING = 'designing';
    public const ON_DELIVERY = 'on_delivery';
}
