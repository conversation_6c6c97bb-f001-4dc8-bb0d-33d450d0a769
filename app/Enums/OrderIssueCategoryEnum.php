<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class OrderIssueCategoryEnum extends Enum
{
   const TRACKING = 'Tracking';
   const QUANTITY = 'Quantity';
   const WRONG_SIZE = 'Wrong size';
   const WRONG_PRODUCT = 'Wrong product';
   const WRONG_DESIGN = 'Wrong design';
   const DEFECTIVE = 'Defective';
   const TRADEMARKS = 'Trademarks';
   const LOST_IN_TRANSIT = 'Lost in transit/mail';
   const ERROR_ADMIN_SYSTEM = 'Error admin system';
   const OOS = 'OOS';
   const CANCEL_12H = 'Cancel 12H';
   const DISPUTE_CASE = 'Dispute case';
   const FAST_SHIP = 'Fast ship';
   const CHANGE_ITEM = 'Change item';
   const DISCOUNT = 'Discount';
   const BUG_SEN = 'Bug Sen';
   const SCAM = 'Scam';
   const INSURANCE = 'Insurance';
   const SELLER_REQUESTS_CANCEL = 'Seller requests cancel';
}
