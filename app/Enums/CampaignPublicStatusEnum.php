<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class CampaignPublicStatusEnum extends Enum
{
    public const NO = 'no';
    public const YES = 'yes';
    public const APPROVED = 'approved';
    public const REJECTED = 'rejected';

    /**
     * @param $status
     * @param $seller
     * @return int
     */
    public static function getPublicStatusElastic($status, $seller): int
    {
        if (!empty($seller)) {
            if (!empty($seller->custom_payment)) {
                return 0;
            }
            if ($status === self::APPROVED) {
                return 1;
            }
            return 0;
        }
        return $status === self::APPROVED ? 1 : 0;
    }

    /**
     * @param $status
     * @param $seller
     * @return string
     */
    public static function getPublicStatus($status, $seller): string
    {
        if (!empty($seller)) {
            if (!empty($seller->custom_payment)) {
                return self::NO;
            }
            if ($status === self::APPROVED) {
                return self::YES;
            }
            return self::NO;
        }
        return $status;
    }
}
