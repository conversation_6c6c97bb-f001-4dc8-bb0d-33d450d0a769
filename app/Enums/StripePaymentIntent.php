<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class StripePaymentIntent extends Enum
{
    public const CREATED = 'payment_intent.created';
    public const SUCCEEDED = 'payment_intent.succeeded';
    public const PROCESSING = 'payment_intent.processing';
    public const CANCELLED = 'payment_intent.canceled';
    public const FAILED = 'payment_intent.payment_failed';
    public const REQUIRES_ACTION = 'payment_intent.requires_action';
    public const CHARGE_SUCCEEDED = 'charge.succeeded';
    public const DISPUTE_CREATED = 'charge.dispute.created';
    public const RADAR_EARLY_FRAUD_WARNING = 'radar.early_fraud_warning.created';
    public const PENDING = 'charge.pending';
}
