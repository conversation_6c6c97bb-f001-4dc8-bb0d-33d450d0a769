<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class FbaFulfillBy extends Enum
{
    const BY_EBAY = 'ebay';
    const BY_ETSY = 'etsy';
    const BY_POSHMARK = 'poshmark';
    const BY_TIKTOK = 'tiktok';
    const BY_AMAZON = 'amazon';

    public static function getStepsQuantityByAmazon(): array
    {
        return [
            30,
            60,
            90,
            120,
            150,
            180,
            210,
            240,
            270,
            300,
        ];
    }
}
