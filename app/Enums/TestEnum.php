<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

final class TestEnum extends Enum
{
    public const ACCESS_TOKEN = 'temp_access_token_for_testing';

    public const THAI = '<EMAIL>';

    public const TUAN = '<EMAIL>';

    public const TEST_BOT = '<EMAIL>';

    public static function arrEmail(): array
    {
        return [
            self::THAI,
            self::TUAN,
            self::TEST_BOT
        ];
    }

}
