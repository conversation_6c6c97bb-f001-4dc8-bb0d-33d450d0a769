<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Enums\OrderProductFulfillStatus;
use App\Models\OrderProduct;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class CustomCatWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'Pending',
            'Verified',
            'Picked / Received',
            'Processing',
            'Failed QA',
            'Ready to Ship',
            'Binned',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'Shipped',
        ],
    ];
    protected array $statusesCancelled = [
        'Cancelled',
        'Canceled',
    ];
    protected array $statusesOnHold = [
        'On Hold - Bad Address',
    ];

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    public function webhookHandle(): self
    {
        // Status
        $topic = Arr::get($this->data, 'topic');

        if ($topic === 'design-rejected') {
            $designUrl = Arr::get($this->data, 'design_url');
            $reason    = Arr::get($this->data, 'reason');
            $orders    = Arr::get($this->data, 'orders');
            foreach ($orders as $order) {
                $fulfillOrderId = $order['order_id'];
                $fulfillSku     = [];
                foreach ($order['items'] as $item) {
                    $fulfillSku[] = $item['catalog_sku'];
                }
                /** @noinspection UnknownColumnInspection */
                OrderProduct::query()
                    ->where('supplier_id', $this->supplierId)
                    ->where('fulfill_order_id', $fulfillOrderId)
                    ->whereIn('fulfill_sku', $fulfillSku)
                    ->where('fulfill_status', OrderProductFulfillStatus::PROCESSING)
                    ->update(
                        [
                            'fulfill_status'        => OrderProductFulfillStatus::EXCEPTION,
                            'fulfill_exception_log' => 'Design url rejected: ' . $designUrl . ' | Reason: ' . $reason,
                        ]
                    );
            }

            return $this;
        }
        $fulfillmentStatus = OrderProductFulfillStatus::PROCESSING; // cause webhook response always is fulfilled -> we set to processing
        $this->setFulfillmentStatus($this->fulfillStatusMapping($this->arrStatusMapping, $fulfillmentStatus));

        $supplierOrderId = Arr::get($this->data, 'order_id');
        $this->setSupplierOrderId($supplierOrderId);

        $senFulfills[] = [
            'no_items' => true,
            'tracking' => [
                'tracking_code'    => Arr::get($this->data, 'tracker_number'),
                'shipping_carrier' => null,
                'tracking_url'     => Arr::get($this->data, 'tracking_url')
            ],
        ];
        $this->setFulfillments($senFulfills);

        return $this;
    }

    public function crawlHandle(): self
    {
        // Status
        $fulfillStatus = Arr::get($this->data, 'ORDER_STATUS');
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $fulfillStatus);
        $this->setFulfillmentStatus($fulfillStatus);

        $supplierOrderId = Arr::get($this->data, 'ORDER_ID');
        $this->setSupplierOrderId($supplierOrderId);

        $lineItems = Arr::get($this->data, 'LINE_ITEMS', []);
        foreach ($lineItems as $lineItem) {
            // change status if one item status is different
            $status = $this->fulfillStatusMapping($this->arrStatusMapping, Arr::get($lineItem, 'STATUS'));
            if ($status !== $fulfillStatus) {
                $this->setExceptionMessage("One product have different status", false);
            }
        }

        $tracking      = Arr::get($this->data, 'SHIPMENTS', []);
        $senFulfills[] = [
            'no_items' => true,
            'tracking' => [
                'tracking_code'    => Arr::get(Arr::last($tracking), 'TRACKING_ID'),
                'shipping_carrier' => Arr::get(Arr::last($tracking), 'VENDOR'),
                'tracking_url'     => null
            ],
        ];
        $this->setFulfillments($senFulfills);

        return $this;
    }
}
