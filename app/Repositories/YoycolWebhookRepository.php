<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class YoycolWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'Created',
            'Awaiting shipment',
            'Awaiting production',
            'In Production',
            'Packaged',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'Shipping',
            'Delivered',
            'Picked up',
        ],
        FulfillmentStatusEnum::REJECTED   => [
            null,
        ],
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response['data'];
    }

    public function webhookHandle(): self
    {
        // don't have
        return $this;
    }

    public function crawlHandle(): self
    {
        $status = Arr::get($this->data, 'status');
        $lineItems = Arr::get($this->data, 'products', []);
        if (empty($lineItems) || empty($status)) {
            return $this;
        }
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $status);
        $this->setFulfillmentStatus($fulfillStatus);
        if ($fulfillStatus === FulfillmentStatusEnum::REJECTED) {
            $fulfillments = [
                [
                    'no_items' => true,
                ]
            ];
            $this->setFulfillments($fulfillments);

            return $this;
        }

        $orderId = Arr::get($this->data, 'orderSn');
        $this->setSupplierOrderId($orderId);

        $items     = [];
        foreach ($lineItems as $lineItem) {
            $items[] = [
                'id' => $lineItem['ecSkuCode'],
            ];
        }

        $trackings = Arr::get($this->data, 'packageItems', []);
        if (count($trackings) > 1) {
            $this->setExceptionMessage('Tracking number is more than 1');
        }
        $tracking     = Arr::last($trackings);
        $fulfillments = [
            [
                'items'    => $items,
                'tracking' => [
                    'tracking_code'    => Arr::get($tracking, 'trackingNo'),
                    'shipping_carrier' => Arr::get($tracking, 'lpName', Arr::get($tracking, 'lpCode')),
                    'tracking_url'     => null,
                ]
            ]
        ];
        $this->setFulfillments($fulfillments);
        return $this;
    }
}
