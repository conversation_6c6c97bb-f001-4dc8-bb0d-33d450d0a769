<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class ToadditWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    // statues must lower key
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PROCESSING => [
            'pending design',
            'pending',
            'cooling off',
            'in production',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'shipped',
        ],
        FulfillmentStatusEnum::REJECTED   => [
            'error artwork',
        ],
    ];
    protected array $statusesCancelled = [
        'request cancel',
        'cancelled',
        'refund shipping',
    ];
    protected array $statusesOnHold = [
        'action required',
        'not verify',
        'unpaid',
        'on hold',
        'waiting pay',
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response['order'];
    }

    public function webhookHandle(): self
    {
        // don't have
        return $this;
    }

    public function crawlHandle(): self
    {
        $supFulfills = $this->data['order_items'];
        $statues     = [];
        foreach ($supFulfills as $item) {
            $status           = strtolower($item['order_status']);
            $statues[$status] = $status;
        }
        if (count($statues) > 1) {
            $this->setExceptionMessage(
                'Items status is not same: ' . implode(',', $statues)
                , false
            );
        } else {
            $status = $this->fulfillStatusMapping($this->arrStatusMapping, array_shift($statues));
            $this->setFulfillmentStatus($status);
        }

        $senOrderId = $this->data['order_name'];
        $this->setSenPrintsOrderId($senOrderId);

        $supplierOrderId = $this->data['pw_order_id'];
        $this->setSupplierOrderId($supplierOrderId);

        $supFulfills = $this->data['order_items'];
        $senFulfills = [];
        foreach ($supFulfills as $supFulfill) {
            $items   = [];
            $sku     = $this->getSku($supFulfill['item_sku']);
            $items[] = [
                'sku' => $sku,
            ];

            $tracking = Arr::first($this->data['trackings'], static function ($tracking) use ($supFulfill) {
                return $tracking['item_sku'] === $supFulfill['item_sku'];
            });

            // each item have one tracking
            $senFulfills[] = [
                'items'    => $items,
                'tracking' => [
                    'tracking_code'    => Arr::get($tracking, 'tracking_number'),
                    'shipping_carrier' => Arr::get($tracking, 'carrier_name'),
                    'tracking_url'     => Arr::get($tracking, 'tracking_url'),
                ],
            ];
        }

        $this->setFulfillments($senFulfills);

        return $this;
    }

    // PW17-HCORMV-DEFAULTSIZE-ONESIDE-W1GEHOCY-3096
    // => PW17-HCORMV-DEFAULTSIZE-ONESIDE
    private function getSku($sku): string
    {
        $arr    = explode('-', $sku);
        $string = array_slice($arr, 0, -2);

        return implode('-', $string);
    }
}
