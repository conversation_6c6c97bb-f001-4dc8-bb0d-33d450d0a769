<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class QTCOWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::REJECTED   => [
        ],
        FulfillmentStatusEnum::PENDING    => [
        ],
        FulfillmentStatusEnum::PROCESSING => [
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'SHIPPED',
        ],
    ];
    protected array $statusesCancelled = [
    ];
    protected array $statusesOnHold = [
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    public function webhookHandle(): self
    {
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['productionState']);
        $this->setFulfillmentStatus($fulfillStatus);

        $supplierOrderId = Arr::get($this->data, 'id');
        $this->setSupplierOrderId($supplierOrderId);

        $shipment = Arr::get($this->data, 'shipment');
        if ($shipment) {
            $trackingCode = Arr::get($shipment, 'trackingNumber');
            $shippingCarrier = Arr::get($shipment, 'shipmentCode');
            if (str_contains($shippingCarrier, 'AUSPOST')) {
                $shippingCarrier = 'Australia Post';
                $trackingUrl = 'https://auspost.com.au/mypost/track/#/details/' . $trackingCode;
            }
        }

        $fulfillments = [
            [
                'no_items' => true,
                'tracking' => [
                    'tracking_code'    => $trackingCode ?? null,
                    'shipping_carrier' => $shippingCarrier ?? null,
                    'tracking_url'     => $trackingUrl ?? null,
                ]
            ]
        ];
        $this->setFulfillments($fulfillments);

        return $this;
    }

    public function crawlHandle(): self
    {
        return $this;
    }
}
