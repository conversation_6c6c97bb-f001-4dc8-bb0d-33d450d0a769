<?php

namespace App\Repositories\Interfaces;

interface WebhookInterface
{
    /**
     * @param int $supplierId
     */
    public function setSupplierId(int $supplierId);

    /**
     * @return int
     */
    public function getSupplierId();

    /**
     * Set exception message.
     * @param mixed $message
     *
     * @return [type]
     */
    public function setExceptionMessage($message);

    /**
     * Is fulfillment is invalid.
     * @return [type]
     */
    public function isException();

    /**
     * Get exception log.
     * @return [type]
     */
    public function getExceptionLog();
}
