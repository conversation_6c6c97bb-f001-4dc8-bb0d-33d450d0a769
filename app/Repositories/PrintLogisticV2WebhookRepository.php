<?php

namespace App\Repositories;

use App\Enums\OrderProductFulfillStatus;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class PrintLogisticV2WebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        OrderProductFulfillStatus::PENDING    => [
            'new',
            'awaiting_accept',
        ],
        OrderProductFulfillStatus::PROCESSING => [
            'In preparation',
            'In production',
            'Ready for shipment',
            'awaiting_preparation',
            'processing',
            'production',
        ],
        OrderProductFulfillStatus::REJECTED   => [
            'rejected',
            'Error',
            'Stock problem',
            'not_found',
            'error',
        ],
        OrderProductFulfillStatus::FULFILLED  => [
            'Sent',
        ],
    ];
    protected array $statusesCancelled = [
        'Canceled',
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $response;
    }

    public function crawlHandle(): self
    {
        $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['status']);
        $this->setFulfillmentStatus($fulfillStatus);

        $supplierOrderId = Arr::get($this->data, 'job_id');
        $this->setSupplierOrderId($supplierOrderId);

        $fulfillments = [
            [
                'no_items' => true,
                'tracking' => [
                    'tracking_code'    => Arr::get($this->data, 'tracking_number'),
                    'shipping_carrier' => Arr::get($this->data, 'carrier'),
                    'tracking_url'     => Arr::get($this->data, 'tracking_url'),
                ]
            ]
        ];
        $this->setFulfillments($fulfillments);

        return $this;
    }
}
