<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Arr;

class PrintHighWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING    => [
            'created',
        ],
        FulfillmentStatusEnum::PROCESSING => [
            'paid',
            'processing',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'shipped',
            'completed',
        ],
    ];

    protected array $statusesCancelled = [
        'archived',
    ];

    public function __construct($data, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = $data;
    }

    public function webhookHandle(): self
    {
        // don't have
        return $this;
    }

    public function crawlHandle(): self
    {
        // Status
        $fulfillStatus = Arr::get($this->data, 'order.status');
        if ($this->handleStatusException($fulfillStatus)) {
            $fulfillStatus = $this->fulfillStatusMapping($this->arrStatusMapping, $fulfillStatus);
            $this->setFulfillmentStatus($fulfillStatus);
        }

        $supplierOrderId = Arr::get($this->data, 'order.id');
        $this->setSupplierOrderId($supplierOrderId);

        $senOrderId = Arr::get($this->data, 'order.sellerOrderId');
        $this->setSenPrintsOrderId($senOrderId);

        // items
        $items     = [];
        $lineItems = Arr::get($this->data, 'orderItems', []);
        foreach ($lineItems as $lineItem) {
            $items[] = [
                'sku' => $lineItem['catalogId'],
            ];
        }

        // shipping
        $tracking = Arr::get($this->data, 'orderPackages', []);
        if (is_array($tracking)) {
            if (count($tracking) > 1) { // if tracking has more than two items then set exception message.
                $this->setExceptionMessage("Orders have multiple fulfillments");
            }
        } else {
            $this->setExceptionMessage("Parse tracking failed");
        }
        $tracking      = last($tracking);
        $senFulfills[] = [
            'items'    => $items,
            'tracking' => [
                'tracking_code'    => Arr::get($tracking, 'trackingNumber'),
                'shipping_carrier' => Arr::get($tracking, 'carrierCode'),
                'tracking_url'     => null
            ],
        ];
        $this->setFulfillments($senFulfills);

        return $this;
    }
}
