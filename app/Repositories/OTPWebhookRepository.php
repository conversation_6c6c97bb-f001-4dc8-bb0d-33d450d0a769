<?php

namespace App\Repositories;

use App\Enums\FulfillmentStatusEnum;
use App\Repositories\Interfaces\FulfillmentInterface;
use Illuminate\Support\Str;

class OTPWebhookRepository extends BaseWebhookRepository implements FulfillmentInterface
{
    public array $data;
    protected array $arrStatusMapping = [
        FulfillmentStatusEnum::PENDING    => [
            'Pending',
        ],
        FulfillmentStatusEnum::PROCESSING => [
            'New',
            'Producible',
            'Picked',
            'InProduction',
            'Produced',
            'Checked',
        ],
        FulfillmentStatusEnum::FULFILLED  => [
            'Shipped',
        ],
        FulfillmentStatusEnum::EXCEPTION    => [
            'OrderNotFound',
        ],
    ];
    protected array $statusesOnHold = [
        'CustomerCare',
    ];
    protected array $statusesCancelled = [
        'Canceled',
    ];

    public function __construct(array $response, $supplierId)
    {
        parent::__construct($supplierId);
        $this->data = array_shift($response['data']);
    }

    public function webhookHandle(): self
    {
        return $this;
    }

    /**
     * @return $this
     */
    public function crawlHandle(): self
    {
        $this->setSupplierOrderId($this->data['OrderID']);

        $this->setFulfillmentStatus(
            $this->fulfillStatusMapping($this->arrStatusMapping, $this->data['State'])
        );

        $this->setSenPrintsOrderId(
            Str::of($this->data['OrderID'])
                ->explode('-')
                ->first()
        );
        $trackingNumber = data_get($this->data, 'TrackingNumber');
        $this->setFulfillments([[
            'no_items' => true,
            'tracking' => [
                'tracking_code'    => $trackingNumber,
                'shipping_carrier' => $trackingNumber ? $this->getShippingCarrier(data_get($this->data, 'TrackingCarrier')) : null,
                'tracking_url'     => data_get($this->data, 'TrackingUrl')
            ],
        ]]);

        return $this;
    }
}
