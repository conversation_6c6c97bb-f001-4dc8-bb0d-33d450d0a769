<?php
namespace App\Listeners;

use App\Enums\OrderCancelRequestStatus;
use App\Events\OrderRefundedEvent;
use App\Models\OrderCancelRequest;
use Illuminate\Contracts\Queue\ShouldQueue;

class OrderRefundedListener implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param OrderRefundedEvent $event
     * @return void
     */
    public function handle(OrderRefundedEvent $event) {
        $order = $event->order;
        try {
            OrderCancelRequest::query()
                ->where('order_id', $order->id)
                ->whereIn('status', [
                    OrderCancelRequestStatus::PENDING,
                    OrderCancelRequestStatus::CONFIRMED,
                    OrderCancelRequestStatus::PROCESSING,
                ])
                ->update([
                    'status' => OrderCancelRequestStatus::CANCELLED,
                    'updated_at' => now()
                ]);
        } catch (\Throwable $e) {
            logToDiscord('Cannot set action when order refunded for order #' . $order->order_number . ' - Error: ' . $e->getMessage());
        }
    }
}
