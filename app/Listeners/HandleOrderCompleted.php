<?php

namespace App\Listeners;

use App\Enums\QueueName;
use App\Events\OrderPaymentCompleted;
use App\Jobs\ProcessOrderCompleted;

class HandleOrderCompleted
{
    /**
     * Handle the event.
     *
     * @param OrderPaymentCompleted $event
     * @throws \Throwable
     */
    public function handle(OrderPaymentCompleted $event): void
    {
        ProcessOrderCompleted::dispatch($event->order->id)->onQueue(QueueName::ORDER)->delay(now()->addSeconds(random_int(5, 10)));
    }
}
