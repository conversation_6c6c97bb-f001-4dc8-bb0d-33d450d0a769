<?php

namespace App\Listeners;

use App\Events\AfterOrderPaymentCompleted;

class VerifyCustomerAddressAndSendMail
{
    /**
     * Handle the event.
     *
     * @param AfterOrderPaymentCompleted $event
     * @return void
     */
    public function handle(AfterOrderPaymentCompleted $event): void
    {
        $order = $event->order;
        if ($order->isCustomServiceOrder() || $order->isServiceOrder() || $order->isFulfillmentOrder()) {
            return;
        }
        \App\Jobs\VerifyCustomerAddress::dispatchSync($order, false, false, true);
    }
}
