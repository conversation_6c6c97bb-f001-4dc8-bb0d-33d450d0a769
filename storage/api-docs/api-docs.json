{"openapi": "3.0.0", "info": {"title": "Seller API", "description": "The API is a service for creating and managing orders, including product listings, order processing, and more.\n\nRequests use standard HTTP methods: GET, POST, PUT, DELETE. Responses are in JSON format.\n\nAuthentication: You can get a token by going to the [Dashboard -> Settings -> General](https://seller.senprints.com/settings/general) -> Get API Key.\n\nYou can select the environment by changing the value of the Servers section below.", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "https://apis.dev.senprints.net", "description": "Development"}, {"url": "https://api.senprints.com", "description": "Production"}], "paths": {"/seller-api/orders": {"post": {"tags": ["Orders"], "summary": "Create a new order", "description": "This API creates a new order based on the provided data.", "operationId": "899717196c7a42ae598152ec960bc0a4", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["order_number", "address", "country", "products"], "properties": {"order_number": {"type": "string"}, "store_name": {"type": "string"}, "store_domain": {"type": "string"}, "customer_name": {"type": "string"}, "customer_email": {"type": "string", "format": "email"}, "customer_phone": {"type": "string"}, "address": {"type": "string"}, "address_2": {"type": "string"}, "house_number": {"type": "string"}, "mailbox_number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "postcode": {"type": "string"}, "country": {"description": "Country code (these options are examples, the actual list of countries may vary)", "type": "string", "enum": ["US", "CA", "GB", "AU", "DE", "FR", "IT", "ES", "JP"]}, "shipping_method": {"type": "string", "enum": ["standard", "express"]}, "order_note": {"type": "string"}, "shipping_label": {"description": "Shipping label URL (required if the order is platform)", "type": "string", "format": "url", "nullable": true}, "products": {"type": "array", "items": {"required": ["quantity", "template_id", "options"], "properties": {"campaign_title": {"type": "string"}, "quantity": {"type": "integer"}, "template_id": {"type": "integer"}, "options": {"description": "Product options as key-value pairs", "type": "object", "example": {"color": "white", "size": "30x20in"}, "additionalProperties": {"type": "string"}}, "custom_options": {"description": "Custom options for the product (e.g., text, image)", "type": "array", "items": {"properties": {"type": {"description": "Type of the option (e.g., text, image)", "type": "string"}, "label": {"description": "Label of the option", "type": "string"}, "value": {"description": "Value of the option", "type": "string"}}, "type": "object"}, "nullable": true}, "barcode": {"description": "Barcode (required only when fulfill_fba_by is amazon)", "type": "string", "nullable": true}, "fulfill_fba_by": {"description": "Fulfillment type (required only when order is platform)", "type": "string", "enum": ["ebay", "etsy", "poshmark", "tiktok", "amazon"], "nullable": true}, "designs": {"type": "array", "items": {"properties": {"file_url": {"type": "string", "format": "url"}, "print_space": {"description": "Reference to template product", "type": "string"}}, "type": "object"}}, "mockups": {"type": "array", "items": {"properties": {"file_url": {"type": "string", "format": "url"}, "print_space": {"description": "Reference to template product", "type": "string"}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}, "examples": {"Example1": {"summary": "Example create order fulfillment", "value": {"order_number": "TEST-123456", "store_name": "ABC Store", "store_domain": "abcstore.com", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "+1234567890", "address": "123 ABC Street", "address_2": "Apt 4B", "house_number": "12", "mailbox_number": "34", "city": "New York", "state": "NY", "postcode": "10001", "country": "US", "shipping_method": "standard", "order_note": "Please deliver before weekend.", "products": [{"campaign_title": "T-Shirt Campaign", "quantity": 2, "template_id": 565, "options": {"color": "red", "size": "m"}, "designs": [{"file_url": "https://example.com/design1.png", "print_space": "front"}], "mockups": [{"file_url": "https://example.com/mockup1.png", "print_space": "front"}]}]}}, "Example2": {"summary": "Example create order platform", "value": {"order_number": "TEST-789012", "store_name": "XYZ Store", "store_domain": "xyzstore.com", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "+1987654321", "address": "456 XYZ Avenue", "address_2": "Suite 21", "house_number": "45", "mailbox_number": "67", "city": "Los Angeles", "state": "CA", "postcode": "90001", "country": "US", "order_note": "Leave at the front door if not home.", "shipping_label": "https://example.com/shipping_label.pdf", "products": [{"campaign_title": "<PERSON>ie Campaign", "quantity": 30, "template_id": 562, "options": {"color": "black", "size": "l"}, "barcode": "123456789012", "fulfill_fba_by": "amazon", "designs": [{"file_url": "https://example.com/design2.png", "print_space": "back"}], "mockups": [{"file_url": "https://example.com/mockup2.png", "print_space": "back"}]}]}}}}}}, "responses": {"201": {"description": "Order created successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Order created successfully"}, "data": {"$ref": "#/components/schemas/Order"}}, "type": "object"}}}}, "400": {"description": "Invalid data", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation error"}, "errors": {"type": "object"}}, "type": "object"}}}}, "500": {"description": "Server error", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error creating order"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/seller-api/catalog": {"get": {"tags": ["Catalog"], "summary": "Get the list of catalogs", "description": "Retrieve the list of catalogs available in the system.", "operationId": "8e623e3100364635de5c0b64cba827ec", "responses": {"200": {"description": "Catalog list retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "name": {"type": "string", "example": "Category Name"}, "slug": {"type": "string", "example": "Category Slug"}, "products": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 101}, "name": {"type": "string", "example": "Product Name"}, "sku": {"type": "string", "example": "PROD123"}, "thumb_url": {"type": "string", "example": "https://example.com/thumb.jpg"}, "base_cost": {"type": "number", "format": "float", "example": 10.5}, "suggested_price": {"type": "number", "format": "float", "example": 15}, "options": {"type": "object", "example": {"color": ["white", "black", "sport grey", "wild lime", "kelly green", "charcoal", "classic orange"], "size": ["s", "m", "l", "xl", "2xl", "3xl", "4xl", "5xl"]}}, "print_spaces": {"type": "array", "items": {"properties": {"name": {"type": "string", "example": "front"}, "width": {"type": "integer", "example": 3000}, "height": {"type": "integer", "example": 3000}, "price": {"type": "number", "format": "float", "example": 1, "nullable": true}}, "type": "object"}, "example": {"front": {"name": "front", "width": 3000, "height": 3000, "price": 1}, "back": {"name": "back", "width": 4050, "height": 4650, "price": 2}}}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/seller-api/me": {"get": {"tags": ["Me"], "summary": "Get information about the authenticated user", "description": "Retrieve the details of the authenticated user.", "operationId": "dad22279181d63daf4851c9f04946186", "responses": {"200": {"description": "User information retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "user": {"properties": {"id": {"type": "integer", "example": 1}, "email": {"type": "string", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/seller-api/catalog/info": {"get": {"tags": ["Catalog"], "summary": "Get catalog info", "description": "Get details of a specific catalog.", "operationId": "bdb3b525abc72e4161ced618f67d69f7", "parameters": [{"name": "id", "in": "query", "description": "The ID of the catalog", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Catalog list retrieved successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "data": {"properties": {"variants": {"type": "array", "items": {"properties": {"variant_key": {"type": "string", "example": "VARIANT123"}, "base_cost": {"type": "number", "format": "float", "example": 10.5}, "location_code": {"type": "string", "example": "LOC123"}, "price": {"type": "number", "format": "float", "example": 15}, "quantity": {"type": "integer", "example": 100}, "out_of_stock": {"type": "boolean", "example": false}}, "type": "object"}}, "shipping_rules": {"type": "array", "items": {"properties": {"shipping_method": {"type": "string", "example": "Standard"}, "location_code": {"type": "string", "example": "LOC123"}, "extra_cost": {"type": "number", "format": "float", "example": 2.5}, "shipping_cost": {"type": "number", "format": "float", "example": 5}}, "type": "object"}}, "description": {"type": "string", "example": "Product description"}, "size_guides": {"type": "array", "items": {"properties": {"size": {"type": "string", "example": "M"}, "length": {"type": "number", "format": "float", "example": 70}, "width": {"type": "number", "format": "float", "example": 50}, "sleeve": {"type": "number", "format": "float", "example": 20}, "height": {"type": "number", "format": "float", "example": 10}, "weight": {"type": "number", "format": "float", "example": 0.5}}, "type": "object"}}, "shipping_times": {"properties": {"standard": {"type": "array", "items": {"properties": {"min": {"type": "integer", "example": 3}, "max": {"type": "integer", "example": 7}}, "type": "object"}}, "express": {"type": "array", "items": {"properties": {"min": {"type": "integer", "example": 1}, "max": {"type": "integer", "example": 3}}, "type": "object"}}}, "type": "object"}, "processing_time": {"type": "integer", "example": 3}}, "type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Unauthorized"}}, "type": "object"}}}}, "404": {"description": "Product template not found", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Product template not found"}, "data": {"type": "null", "example": null}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/seller-api/orders/{id}": {"get": {"tags": ["Orders"], "summary": "Get order details", "description": "Retrieve the details of a specific order by its ID.", "operationId": "c478d669c54cc4b74210ada4690a2926", "parameters": [{"name": "id", "in": "path", "description": "The unique ID of the order", "required": true, "schema": {"type": "integer", "example": 1}}], "responses": {"200": {"description": "Order details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Order"}}}}, "404": {"description": "Order not found", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Order not found"}}, "type": "object"}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden access"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["Orders"], "summary": "Update an order", "description": "Only send fields that need updating.", "operationId": "d8307c52ec67f0ac1b8821b7409b0c74", "parameters": [{"name": "id", "in": "path", "description": "The unique ID of the order", "required": true, "schema": {"type": "integer", "example": 1}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"order_number": {"type": "string"}, "store_name": {"type": "string"}, "store_domain": {"type": "string"}, "customer_name": {"type": "string"}, "customer_email": {"type": "string", "format": "email"}, "customer_phone": {"type": "string"}, "address": {"type": "string"}, "address_2": {"type": "string"}, "house_number": {"type": "string"}, "mailbox_number": {"type": "string"}, "city": {"type": "string"}, "state": {"type": "string"}, "postcode": {"type": "string"}, "country": {"description": "Country code (these options are examples, the actual list of countries may vary)", "type": "string", "enum": ["US", "CA", "GB", "AU", "DE", "FR", "IT", "ES", "JP"]}, "shipping_method": {"type": "string", "enum": ["standard", "express"]}, "order_note": {"type": "string"}, "shipping_label": {"description": "Shipping label URL (required if the order is platform)", "type": "string", "format": "url", "nullable": true}, "products": {"type": "array", "items": {"required": ["quantity", "template_id", "options"], "properties": {"id": {"description": "Order Product ID (nullable, fill only when updating an existing product)", "type": "integer", "nullable": true}, "campaign_title": {"type": "string"}, "quantity": {"type": "integer"}, "template_id": {"type": "integer"}, "options": {"description": "Product options as key-value pairs", "type": "object", "additionalProperties": {"type": "string"}}, "custom_options": {"description": "Custom options for the product", "type": "array", "items": {"properties": {"type": {"description": "Type of the option", "type": "string"}, "label": {"description": "Label of the option", "type": "string"}, "value": {"description": "Value of the option", "type": "string"}}, "type": "object"}, "nullable": true}, "barcode": {"description": "Barcode (required only when fulfill_fba_by is amazon)", "type": "string", "nullable": true}, "fulfill_fba_by": {"description": "Fulfillment type (required only when order is platform)", "type": "string", "enum": ["ebay", "etsy", "poshmark", "tiktok", "amazon"], "nullable": true}, "designs": {"type": "array", "items": {"properties": {"id": {"description": "ID of the design file", "type": "integer"}, "file_url": {"type": "string", "format": "url"}, "print_space": {"type": "string", "enum": ["default", "main", "back", "front", "front_1", "front_2", "front_3", "front_4", "large", "small", "9oz", "10oz", "11oz", "12oz", "15oz", "16oz", "18oz", "20oz", "20oz2", "32oz", "10x10", "10x14", "10x8", "11x14", "12x12", "12x16", "12x18", "12x8", "13x19", "13x21", "14x10", "14x11", "14x14", "14x21", "16x12", "16x16", "16x20", "16x24", "17x17", "18x12", "18x18", "18x24", "20x16", "20x20", "20x30", "21x14", "24x16", "24x17", "24x18", "24x24", "24x32", "24x36", "27x40", "28x28", "30x20", "30x30", "30x40", "30x46", "32x24", "32x32", "32x48", "34x21", "35x60", "36x24", "36x54", "36x60", "40x27", "40x40", "46x30", "48x32", "4x6", "50x60", "54x36", "60x80", "6x4", "6x9", "8x10", "8x12", "8x8", "king", "queen", "twin", "twin_xl", "standard", "120x140", "120x150", "130x180", "150x200", "s", "m", "l", "xl", "right_sleeve", "left_sleeve", "left_chest", "right_chest", "glass", "decanter", "coaster", "box", "heart", "round", "2x2 inches", "3x3 inches", "3-5x3-5 inches", "4x4 inches", "8 inches", "12 inches", "14 inches", "18 inches", "22 inches", "26 inches", "30 inches", "48 inches", "one sided", "double sided"]}}, "type": "object"}}, "mockups": {"type": "array", "items": {"properties": {"id": {"description": "ID of the mockup file", "type": "integer"}, "file_url": {"type": "string", "format": "url"}, "print_space": {"type": "string", "enum": ["default", "main", "back", "front", "front_1", "front_2", "front_3", "front_4", "large", "small", "9oz", "10oz", "11oz", "12oz", "15oz", "16oz", "18oz", "20oz", "20oz2", "32oz", "10x10", "10x14", "10x8", "11x14", "12x12", "12x16", "12x18", "12x8", "13x19", "13x21", "14x10", "14x11", "14x14", "14x21", "16x12", "16x16", "16x20", "16x24", "17x17", "18x12", "18x18", "18x24", "20x16", "20x20", "20x30", "21x14", "24x16", "24x17", "24x18", "24x24", "24x32", "24x36", "27x40", "28x28", "30x20", "30x30", "30x40", "30x46", "32x24", "32x32", "32x48", "34x21", "35x60", "36x24", "36x54", "36x60", "40x27", "40x40", "46x30", "48x32", "4x6", "50x60", "54x36", "60x80", "6x4", "6x9", "8x10", "8x12", "8x8", "king", "queen", "twin", "twin_xl", "standard", "120x140", "120x150", "130x180", "150x200", "s", "m", "l", "xl", "right_sleeve", "left_sleeve", "left_chest", "right_chest", "glass", "decanter", "coaster", "box", "heart", "round", "2x2 inches", "3x3 inches", "3-5x3-5 inches", "4x4 inches", "8 inches", "12 inches", "14 inches", "18 inches", "22 inches", "26 inches", "30 inches", "48 inches", "one sided", "double sided"]}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}, "examples": {"Example1": {"summary": "Example updating order fulfillment", "value": {"order_number": "TEST-123456", "store_name": "ABC Store", "store_domain": "abcstore.com", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "+1234567890", "address": "123 ABC Street", "address_2": "Apt 4B", "house_number": "12", "mailbox_number": "34", "city": "New York", "state": "NY", "postcode": "10001", "country": "US", "shipping_method": "standard", "order_note": "Please deliver before weekend.", "products": [{"id": 1, "campaign_title": "T-Shirt Campaign", "quantity": 2, "template_id": 565, "options": {"color": "red", "size": "m"}, "designs": [{"id": 1, "file_url": "https://example.com/design1.png", "print_space": "front"}], "mockups": [{"id": 10, "file_url": "https://example.com/mockup1.png", "print_space": "front"}]}]}}, "Example2": {"summary": "Example updating order platform", "value": {"order_number": "TEST-789012", "store_name": "XYZ Store", "store_domain": "xyzstore.com", "customer_name": "<PERSON>", "customer_email": "<EMAIL>", "customer_phone": "+1987654321", "address": "456 XYZ Avenue", "address_2": "Suite 21", "house_number": "45", "mailbox_number": "67", "city": "Los Angeles", "state": "CA", "postcode": "90001", "country": "US", "order_note": "Leave at the front door if not home.", "shipping_label": "https://example.com/shipping_label.pdf", "products": [{"id": 2, "campaign_title": "<PERSON>ie Campaign", "quantity": 30, "template_id": 562, "options": {"color": "black", "size": "l"}, "barcode": "123456789012", "fulfill_fba_by": "amazon", "custom_options": [{"type": "Gift Wrap", "label": "Wrap it nicely", "value": "Yes"}], "designs": [{"id": 2, "file_url": "https://example.com/design2.png", "print_space": "back"}], "mockups": [{"id": 3, "file_url": "https://example.com/mockup2.png", "print_space": "back"}]}]}}}}}}, "responses": {"200": {"description": "Order updated successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Order updated successfully"}, "data": {"$ref": "#/components/schemas/Order"}}, "type": "object"}}}}, "400": {"description": "Invalid data provided", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Validation error"}, "errors": {"type": "object"}}, "type": "object"}}}}, "404": {"description": "Order not found", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Order not found"}}, "type": "object"}}}}, "403": {"description": "Unauthorized access", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Forbidden access"}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/seller-api/orders/{id}/{action}": {"put": {"tags": ["Orders"], "summary": "Update order status", "description": "Update the status of an existing order by its ID.", "operationId": "bf9af376c4c2e7257a7b7a9a4309c816", "parameters": [{"name": "id", "in": "path", "description": "The unique ID of the order", "required": true, "schema": {"type": "integer", "example": 1}}, {"name": "action", "in": "path", "description": "Action to update order status", "required": true, "schema": {"type": "string", "enum": ["hold", "resume", "cancel"], "example": "hold"}}], "responses": {"200": {"description": "Order status updated successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Order status updated successfully"}}, "type": "object"}}}}, "400": {"description": "Invalid action", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Invalid action"}, "action": {"type": "string", "example": "unknown_action"}}, "type": "object"}}}}, "404": {"description": "Order not found", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Order not found"}}, "type": "object"}}}}, "500": {"description": "Error updating order status", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "Error updating order status"}, "order_id": {"type": "integer", "example": 1}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"schemas": {"Order": {"title": "Order", "properties": {"id": {"description": "Order ID", "type": "integer", "example": 1}, "order_number": {"description": "Your order number", "type": "string", "example": "ORD-123456"}, "store_name": {"description": "Store name", "type": "string", "example": "ABC Store", "nullable": true}, "store_domain": {"description": "Store domain", "type": "string", "example": "abc.com", "nullable": true}, "customer_name": {"description": "Customer name", "type": "string", "example": "<PERSON><PERSON><PERSON>"}, "customer_email": {"description": "Customer email", "type": "string", "format": "email", "example": "<EMAIL>", "nullable": true}, "customer_phone": {"description": "Customer phone number", "type": "string", "example": "0123456789", "nullable": true}, "address": {"description": "Customer address", "type": "string", "example": "123 ABC Street"}, "address_2": {"description": "Additional address", "type": "string", "example": "Apartment 101", "nullable": true}, "house_number": {"description": "House number", "type": "string", "example": "12B", "nullable": true}, "mailbox_number": {"description": "Mailbox number", "type": "string", "example": "456", "nullable": true}, "city": {"description": "City", "type": "string", "example": "<PERSON><PERSON>", "nullable": true}, "state": {"description": "State/Province", "type": "string", "example": "<PERSON>", "nullable": true}, "postcode": {"description": "Postal code", "type": "string", "example": "100000", "nullable": true}, "country": {"description": "Country code", "type": "string", "example": "VN"}, "order_note": {"description": "Order note", "type": "string", "example": "Deliver during business hours", "nullable": true}, "shipping_label": {"description": "Shipping label URL", "type": "string", "format": "url", "example": "http://example.com/label.pdf", "nullable": true}, "shipping_method": {"description": "Shipping method", "type": "string", "example": "standard"}, "cross_shipping": {"description": "Whether the order uses cross shipping", "type": "boolean", "example": true, "nullable": true}, "personalized": {"description": "Whether the order is personalized", "type": "boolean", "example": false, "nullable": true}, "status": {"description": "Order status", "type": "string", "example": "pending"}, "fulfill_status": {"description": "Fulfillment status", "type": "string", "example": "unfulfilled"}, "address_verified": {"description": "Whether the address has been verified", "type": "boolean", "example": true, "nullable": true}, "fulfill_log": {"description": "Fulfillment log details", "type": "string", "example": "Order sent to production", "nullable": true}, "type": {"description": "Order type", "type": "string", "example": "standard"}, "created_at": {"description": "Order creation timestamp", "type": "string", "format": "date-time", "example": "2025-02-28T17:01:29+07:00"}, "updated_at": {"description": "Order update timestamp", "type": "string", "format": "date-time", "example": "2025-02-28T17:01:29+07:00"}, "paid_at": {"description": "Timestamp when the order was paid", "type": "string", "format": "date-time", "example": "2025-03-01T12:00:00+07:00"}, "delivered_at": {"description": "Timestamp when the order was delivered", "type": "string", "format": "date-time", "example": "2025-03-05T09:00:00+07:00"}, "received_at": {"description": "Timestamp when the order was received", "type": "string", "format": "date-time", "example": "2025-03-06T14:00:00+07:00"}, "total_quantity": {"description": "Total quantity of products in the order", "type": "integer", "example": 3}, "total_product_amount": {"description": "Total amount for products", "type": "number", "format": "float", "example": 59.99}, "total_shipping_amount": {"description": "Total shipping fee", "type": "number", "format": "float", "example": 5.99}, "total_amount": {"description": "Total order amount", "type": "number", "format": "float", "example": 65.98}, "total_paid": {"description": "Total amount paid", "type": "number", "format": "float", "example": 65.98}, "total_product_cost": {"description": "Total product cost", "type": "number", "format": "float", "example": 30}, "total_shipping_cost": {"description": "Total shipping cost", "type": "number", "format": "float", "example": 3}, "total_seller_profit": {"description": "Total profit for the seller", "type": "number", "format": "float", "example": 32.98}, "processing_fee": {"description": "Processing fee for the order", "type": "number", "format": "float", "example": 2}, "total_fulfill_fee": {"description": "Total fulfill fee", "type": "number", "format": "float", "example": 4}, "processing_fee_paid": {"description": "Whether the processing fee has been paid", "type": "boolean", "example": true}, "fulfill_fee_paid": {"description": "Whether the fulfill fee has been paid", "type": "boolean", "example": true}, "products": {"description": "List of products in the order", "type": "array", "items": {"properties": {"id": {"description": "Order Product ID in the order", "type": "integer", "example": 1}, "product_name": {"description": "Product name", "type": "string", "example": "T-shirt"}, "sku": {"description": "Product SKU", "type": "string", "example": "SKU12345"}, "quantity": {"description": "Product quantity", "type": "integer", "example": 2}, "price": {"description": "Price of the product", "type": "number", "format": "float", "example": 19.99}, "total_amount": {"description": "Total amount for this product line", "type": "number", "format": "float", "example": 39.98}, "seller_profit": {"description": "Profit earned by seller from this product", "type": "number", "format": "float", "example": 10}, "fulfill_status": {"description": "Fulfillment status of the product", "type": "string", "example": "fulfilled"}, "full_printed": {"description": "Whether the product is full printed", "type": "boolean", "example": true}, "barcode": {"description": "Barcode of the product", "type": "string", "example": "ABC123456789", "nullable": true}, "fulfill_fba_by": {"description": "Fulfillment service provider", "type": "string", "example": "Amazon", "nullable": true}, "campaign_title": {"description": "Campaign name related to the product", "type": "string", "example": "Valentine Campaign", "nullable": true}, "tracking_code": {"description": "Tracking code for this product", "type": "string", "example": "TRACK123456", "nullable": true}, "tracking_url": {"description": "Tracking URL for this product", "type": "string", "format": "url", "example": "http://tracking.com/TRACK123456", "nullable": true}, "shipping_carrier": {"description": "Shipping carrier used", "type": "string", "example": "DHL", "nullable": true}, "cross_shipping": {"description": "Whether the product uses cross shipping", "type": "boolean", "example": false}, "color": {"description": "Product color", "type": "string", "example": "Red", "nullable": true}, "options": {"description": "Product options as key-value pairs", "type": "object", "example": {"color": "white", "size": "30x20in"}, "additionalProperties": {"type": "string"}}, "custom_options": {"description": "Custom options for the product (e.g., text, image)", "type": "array", "items": {"properties": {"type": {"description": "Type of the option (e.g., text, image)", "type": "string"}, "label": {"description": "Label of the option", "type": "string"}, "value": {"description": "Value of the option", "type": "string"}}, "type": "object"}, "nullable": true}, "thumb_url": {"description": "Product thumbnail URL", "type": "string", "format": "url", "example": "http://example.com/thumb.jpg", "nullable": true}, "mockups": {"description": "List of product mockups", "type": "array", "items": {"required": ["id", "file_url"], "properties": {"id": {"description": "Mockup ID", "type": "integer", "example": 1}, "file_url": {"description": "Mockup file URL", "type": "string", "format": "url", "example": "http://example.com/mockup.jpg"}, "print_space": {"description": "Print space of the mockup", "type": "string", "example": "front", "nullable": true}}, "type": "object"}}, "designs": {"description": "List of product designs", "type": "array", "items": {"required": ["id", "file_url"], "properties": {"id": {"description": "Design ID", "type": "integer", "example": 1}, "file_url": {"description": "Design file URL", "type": "string", "format": "url", "example": "http://example.com/design.jpg"}, "print_space": {"description": "Print space of the design", "type": "string", "example": "front", "nullable": true}}, "type": "object"}}}, "type": "object"}}}, "type": "object"}}, "securitySchemes": {"bearerAuth": {"type": "http", "description": "Enter your token", "bearerFormat": "JWT", "scheme": "bearer"}}}, "tags": [{"name": "Orders", "description": "Orders"}, {"name": "Catalog", "description": "Catalog"}, {"name": "Me", "description": "Me"}]}