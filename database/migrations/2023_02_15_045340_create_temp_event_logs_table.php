<?php

use App\Enums\EventLogsTypeEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateTempEventLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::connection('pgsql')->hasTable('temp_event_logs')) {
            Schema::connection('pgsql')->create('temp_event_logs', function (Blueprint $table) {
                $table->id();
                $table->string('type')
                    ->comment(implode(',', EventLogsTypeEnum::asArray()))
                    ->index()
                    ->default(EventLogsTypeEnum::VISIT);
                $table->unsignedBigInteger('seller_id')
                    ->index()
                    ->nullable();
                $table->unsignedBigInteger('store_id')
                    ->index()
                    ->nullable();
                $table->unsignedBigInteger('campaign_id')
                    ->index()
                    ->nullable();
                $table->unsignedBigInteger('product_id')
                    ->index()
                    ->nullable();
                $table->uuid('session_id')->index();
                $table->string('country', 64)->nullable();
                $table->string('device', 16)->nullable();
                $table->string('device_detail', 128)->nullable();
                $table->string('ad_source', 128)->default('N/A');
                $table->string('ad_campaign', 128)->default('N/A');
                $table->string('ad_medium', 128)->default('N/A');
                $table->timestamp('timestamp')
                    ->index()
                    ->default(DB::raw('CURRENT_TIMESTAMP'));
                $table->timestamp('datestamp')
                    ->index();
                $table->index(['type', 'campaign_id', 'timestamp']);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
