<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateBoughtTogetherLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::connection('pgsql')->hasTable('bought_together_log')) {
            $this->down();
        }

        Schema::connection('pgsql')->create('bought_together_log', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('product_id1')->index('bought_together_log_product_id1')->nullable();
            $table->unsignedBigInteger('product_id2')->index('bought_together_log_product_id2')->nullable();
            $table->unsignedBigInteger('order_id')->index('bought_together_log_order_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('seller_id')->nullable();
            $table->unsignedBigInteger('store_id')->nullable();
            $table->timestamp('timestamp')->default(DB::raw('CURRENT_TIMESTAMP'))->index('bought_together_log_timestamp');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('pgsql')->dropIfExists('bought_together_log');
    }
}
