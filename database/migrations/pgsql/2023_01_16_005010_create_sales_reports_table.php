<?php

use App\Enums\SaleReport\PeriodEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesReportsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::connection('pgsql')->create('sales_reports', function (Blueprint $table) {
            $table->id();
            $table->date('date_timezone')
                ->comment('Date with timezone seller')
                ->index();
            $table->dateTime('timestamp')->index();
            $table->string('period', 8)
                ->comment(implode(',', PeriodEnum::getValues()))
                ->default(PeriodEnum::HOUR)
                ->index();
            $table->string('type', 50)->index();
            $table->string('data_type', 50)->nullable()->index();
            $table->string('data_key')->nullable()->index();
            $table->string('data_id', 50)->nullable()->index();
            $table->integer('visits');
            $table->integer('add_to_carts');
            $table->integer('checkouts');
            $table->integer('orders');
            $table->integer('items');
            $table->double('total_sales');
            $table->double('seller_profit');
            $table->double('insurance_fee');
            $table->double('tip');
            $table->double('total_product_amount');
            $table->double('total_shipping_amount');
            $table->double('total_discount');
            $table->double('total_refund');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('pgsql')->dropIfExists('sales_reports');
    }
}
