<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitCampaignTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::connection('pgsql')->hasTable('visit_campaign')) {
            $this->down();
        }

        Schema::connection('pgsql')->create('visit_campaign', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('seller_id')->index('visit_campaign_seller_id')->nullable();
            $table->unsignedBigInteger('store_id')->index('visit_campaign_store_id')->nullable();
            $table->unsignedBigInteger('campaign_id')->index('visit_campaign_campaign_id')->nullable();
            $table->unsignedBigInteger('product_id')->index('visit_campaign_product_id')->nullable();
            $table->uuid('session_id')->index('visit_campaign_session_id');
            $table->string('country', 64)->nullable();
            $table->string('device', 16)->nullable();
            $table->string('device_detail', 128)->nullable();
            $table->timestamp('timestamp')->default(DB::raw('CURRENT_TIMESTAMP'))->index('visit_campaign_timestamp');
            $table->timestamp('datestamp')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::connection('pgsql')->dropIfExists('visit_campaign');
    }
}
