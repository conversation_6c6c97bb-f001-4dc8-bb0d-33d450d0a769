<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterAdColumnsNotNullableInStatsOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'ad_source')) {
            DB::connection('pgsql')
                ->table('stats_order')
                ->update(
                    [
                        'ad_source' => DB::raw("COALESCE(ad_source,'N/A')"),
                    ]
                );
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->string('ad_source')->nullable(false)->default('N/A')->change();
                }
            );
        }
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'ad_medium')) {
            DB::connection('pgsql')
                ->table('stats_order')
                ->update(
                    [
                        'ad_medium' => DB::raw("COALESCE(ad_medium,'N/A')"),
                    ]
                );
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->string('ad_medium')->nullable(false)->default('N/A')->change();
                }
            );
        }
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'ad_campaign')) {
            DB::connection('pgsql')
                ->table('stats_order')
                ->update(
                    [
                        'ad_campaign' => DB::raw("COALESCE(ad_campaign,'N/A')"),
                    ]
                );
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->string('ad_campaign')->nullable(false)->default('N/A')->change();
                }
            );
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
}
