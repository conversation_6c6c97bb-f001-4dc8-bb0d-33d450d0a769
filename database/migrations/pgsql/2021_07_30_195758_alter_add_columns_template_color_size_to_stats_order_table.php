<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterAddColumnsTemplateColorSizeToStatsOrderTable extends Migration
{
    public function up()
    {
        if (!Schema::connection('pgsql')->hasColumn('stats_order', 'template_id')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->unsignedBigInteger('template_id')->index('template_id_index')->nullable();
                }
            );
        }
        if (!Schema::connection('pgsql')->hasColumn('stats_order', 'color')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->string('color')->index('color_index')->nullable();
                }
            );
        }
        if (!Schema::connection('pgsql')->hasColumn('stats_order', 'size')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->string('size')->index('size_index')->nullable();
                }
            );
        }
    }

    public function down()
    {
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'template_id')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->dropColumn('template_id');
                }
            );
        }
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'color')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->dropColumn('color');
                }
            );
        }
        if (Schema::connection('pgsql')->hasColumn('stats_order', 'size')) {
            Schema::connection('pgsql')->table(
                'stats_order',
                function (Blueprint $table) {
                    $table->dropColumn('size');
                }
            );
        }
    }
}
