<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterAddIndexToEventLogsTable extends Migration
{
    public function up()
    {
        $listIndex = DB::connection('pgsql')->table('pg_indexes')
            ->where('tablename','event_logs')
            ->get('indexname');

        Schema::connection('pgsql')->table('event_logs', function (Blueprint $table) use ($listIndex) {
            if (!$listIndex->firstWhere('indexname','event_logs_device_detail')) {
                $table->index('device_detail', 'event_logs_device_detail');
            }
            if (!$listIndex->firstWhere('indexname','event_logs_ad_source')) {
                $table->index('ad_source', 'event_logs_ad_source');
            }
            if (!$listIndex->firstWhere('indexname','event_logs_ad_campaign')) {
                $table->index('ad_campaign', 'event_logs_ad_campaign');
            }
            if (!$listIndex->firstWhere('indexname','event_logs_ad_medium')) {
                $table->index('ad_medium', 'event_logs_ad_medium');
            }
            if (!$listIndex->firstWhere('indexname','event_logs_country')) {
                $table->index('country', 'event_logs_country');
            }
        });
    }

    public function down()
    {
    }
}
