<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class AlterAddTypeIndexToEventLogsTable extends Migration
{
    public function up()
    {
        $listIndex = DB::connection('pgsql')->table('pg_indexes')
            ->where('tablename','event_logs')
            ->get('indexname');

        Schema::connection('pgsql')->table('event_logs', function (Blueprint $table) use ($listIndex) {
            if (!$listIndex->firstWhere('indexname','type_index')) {
                $table->index('type', 'type_index');
            }
        });
    }

    public function down()
    {
    }
}
