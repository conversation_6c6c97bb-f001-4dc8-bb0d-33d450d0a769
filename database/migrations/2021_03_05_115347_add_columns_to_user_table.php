<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnsToUserTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasColumn('user', 'total_purchases')) {
            Schema::table('user', function (Blueprint $table) {
                $table->unsignedDouble('total_purchases')
                    ->after('balance')
                    ->default(0);
            });
        }
        if (!Schema::hasColumn('user', 'total_orders')) {
            Schema::table('user', function (Blueprint $table) {
                $table->unsignedInteger('total_orders')
                    ->after('balance')
                    ->default(0);
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
