<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStaffLogTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('staff_log', function (Blueprint $table) {
            if (!Schema::hasColumn('staff_log', 'order_id')) {
                $table->foreignId('order_id')
                    ->nullable()
                    ->after('is_login_as')
                    ->constrained('order')
                    ->cascadeOnDelete();
            }

            if (!Schema::hasColumn('staff_log', 'campaign_id')) {
                $table->foreignId('campaign_id')
                    ->nullable()
                    ->after('is_login_as')
                    ->constrained('product')
                    ->cascadeOnDelete();
            }
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
