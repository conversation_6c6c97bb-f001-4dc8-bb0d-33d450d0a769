<?php

use App\Enums\OrderFulfillStatus;
use App\Enums\OrderStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AlterOrderHistoryTable extends Migration
{
    public function up()
    {
        $sm           = Schema::getConnection()->getDoctrineSchemaManager();
        $indexesFound = $sm->listTableIndexes('order_history');

        Schema::table(
            'order_history',
            function (Blueprint $table) use ($indexesFound){
                if (Schema::hasColumn('order_history', 'order_status')) {
                    $table->enum('order_status', OrderStatus::asArray())
                        ->comment("'" . implode("','", OrderStatus::getValues()) . "'")
                        ->change();
                }
                if (Schema::hasColumn('order_history', 'updated_by')) {
                    $table->string('updated_by')->change();
                }
                if (Schema::hasColumn('order_history', 'detail')) {
                    $table->text('detail')->nullable()->change();
                }
                if (!Schema::hasColumn('order_history', 'admin_detail')) {
                    $table->text('admin_detail')->nullable()->after('detail');
                }
                if (Schema::hasColumn('order_history', 'updated_at')) {
                    $table->dropColumn('updated_at');
                }
                if (!Schema::hasColumn('order_history', 'fulfill_status')) {
                    $table->enum('fulfill_status', OrderFulfillStatus::asArray())
                        ->comment("'" . implode("','", OrderFulfillStatus::getValues()) . "'")
                        ->after('order_status')
                        ->nullable();
                }
                if (!Schema::hasColumn('order_history', 'action')) {
                    $table->string('action')
                        ->after('order_id')
                        ->index();
                }
                if (!array_key_exists('created_at_index', $indexesFound)) {
                    $table->index('created_at', 'created_at_index');
                }
            }
        );

    }

    public function down(): void {}
}
