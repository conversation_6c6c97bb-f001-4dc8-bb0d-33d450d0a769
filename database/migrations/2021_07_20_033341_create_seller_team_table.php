<?php

use App\Enums\SellerTeamRoleEnum;
use App\Enums\SellerTeamStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSellerTeamTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('seller_team', function (Blueprint $table) {
//            $table->id();

            // team admin
            $table->unsignedBigInteger('seller_id');
            $table->foreign('seller_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');

            // team member
            $table->unsignedBigInteger('member_id');
            $table->foreign('member_id')
                ->references('id')
                ->on('user')
                ->onDelete('cascade');

            $table->enum('role', SellerTeamRoleEnum::getValues());
            $table->enum('status', SellerTeamStatusEnum::getValues())
                ->default(SellerTeamStatusEnum::INVITED);

            $table->timestamps();

            $table->unique(['seller_id', 'member_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
