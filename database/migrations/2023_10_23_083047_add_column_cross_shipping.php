<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnCrossShipping extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('order', static function (Blueprint $table) {
            $table->tinyInteger('cross_shipping')->comment('0: not cross shipping, 1: cross shipping')->default(0);
        });

        Schema::table('order_product', static function (Blueprint $table) {
            $table->tinyInteger('cross_shipping')->comment('0: not cross shipping, 1: cross shipping')->default(0);
        });

        Schema::connection('singlestore')->table('order', function (Blueprint $table) {
            $table->tinyInteger('cross_shipping')->comment('0: not cross shipping, 1: cross shipping')->default(0);
        });

        Schema::connection('singlestore')->table('order_product', function (Blueprint $table) {
            $table->tinyInteger('cross_shipping')->comment('0: not cross shipping, 1: cross shipping')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
