<?php

use App\Enums\FeaturedInfoStatusEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class CreateFeaturedInfoTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('featured_info', function (Blueprint $table) {
            $table->id();
            $table->string('subject', 255);
            $table->string('short_desc', 255)
                ->comment('Short description')
                ->nullable();
            $table->text('description')->nullable();
            $table->enum('status', FeaturedInfoStatusEnum::getValues())
                ->default(FeaturedInfoStatusEnum::ACTIVE);
            $table->string('banner_url', 255);
            $table->string('banner_url_2', 255)->nullable();
            $table->string('article_url', 255);
            $table->unsignedBigInteger('staff_id')->nullable();
            $table->timestamp('created_at')->default(DB::raw('CURRENT_TIMESTAMP'));
            $table->timestamp('updated_at')->default(DB::raw('CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP'));
            $table->timestamp('expired_at');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void {}
}
