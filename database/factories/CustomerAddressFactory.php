<?php

namespace Database\Factories;

use App\Models\CustomerAddress;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class CustomerAddressFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = CustomerAddress::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        $customerIds = User::role('customer')
            ->limit(10)
            ->get('id')
            ->pluck('id')
            ->toArray();

        $customerId = $this->faker->randomElement($customerIds);

        return [
            'user_id' => $customerId,
            'name' => $this->faker->name,
            'address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'postcode' => $this->faker->postcode,
            'country' => $this->faker->countryCode,
            'status' => 1
        ];
    }
}
