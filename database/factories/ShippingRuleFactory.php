<?php

namespace Database\Factories;

use App\Enums\ShippingRuleType;
use App\Models\Product;
use App\Models\ShippingRule;
use Illuminate\Database\Eloquent\Factories\Factory;

class ShippingRuleFactory extends Factory
{
    protected $model = ShippingRule::class;

    public function definition(): array
    {
        return [
            'shipping_method' => ShippingRuleType::STANDARD,
            'location_code' => '*',
            'shipping_cost' => $this->faker->randomFloat(2, 0, 100),
            'extra_cost' => $this->faker->randomFloat(2, 0, 100),
        ];
    }

    public function forProduct(Product $product): self
    {
        return $this->state(fn(array $attributes) => [
            'product_id' => $product->id,
        ]);
    }
}
