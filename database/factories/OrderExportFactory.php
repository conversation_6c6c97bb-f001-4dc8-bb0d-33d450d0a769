<?php

namespace Database\Factories;

use App\Enums\OrderExportStatus;
use App\Models\OrderExport;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderExportFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OrderExport::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition(): array
    {
        return [
            'export_name' => $this->faker->monthName,
            'total_orders' => $this->faker->numberBetween(100, 200),
            'download_url' => 'https://sendev.s3.ap-southeast-1.amazonaws.com/export/orders-exported.csv',
            'status' => $this->faker->randomElement(OrderExportStatus::asArray()),
            'created_at' => $this->faker->dateTimeThisYear()
        ];
    }
}
