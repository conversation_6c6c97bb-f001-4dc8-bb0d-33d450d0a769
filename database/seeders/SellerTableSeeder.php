<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SellerTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
//        if (app()->environment('production')) {
//            User::create([
//                'id' => 1,
//                'name' => 'SenPrints System',
//                'email' => '<EMAIL>',
//                'password' => Hash::make('vn8MHSjyKfs'),
//                'balance' => 0,
//                'timezone' => 'Asia/Saigon',
//                'utc_offset' => 0
//            ])->assignRole('seller');
//        } else {

        $password = Hash::make('12345678');

        // Create first user
        User::query()
            ->create([
                'id' => 1,
                'name' => 'SenPrints Admin',
                'email' => '<EMAIL>',
                'password' => $password,
                'balance' => 0,
                'timezone' => 'Asia/Saigon',
                'utc_offset' => 0
            ])
            ->assignRole('seller');

        User::query()
            ->create([
                'id' => 2,
                'name' => 'SenPrints Demo',
                'email' => '<EMAIL>',
                'password' => $password,
                'balance' => 9999,
                'timezone' => 'Asia/Saigon',
                'utc_offset' => 0
            ])
            ->assignRole('seller');

        User::query()
            ->create([
                'name' => 'JUNO_OKYO',
                'email' => '<EMAIL>',
                'password' => $password,
                'balance' => 9999,
                'timezone' => 'Asia/Saigon',
                'utc_offset' => 3
            ])
            ->assignRole('seller');

        $guardSellerRoles = [
            'seller',
            'manager',
            'launcher'
        ];

        // Create a random user
        User::factory()
            ->count(10)
            ->create()
            ->each(function ($user) use ($guardSellerRoles) {
                $randRoleKey = array_rand($guardSellerRoles);
                $user->assignRole($guardSellerRoles[$randRoleKey]);
            });
        //}
    }
}
