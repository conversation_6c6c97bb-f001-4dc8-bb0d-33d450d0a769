<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoryTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
//        \DB::table('category')->delete();
        DB::table('category')->insert(array(
            0 =>
                array(
                    'id' => 1,
                    'name' => 'Apparel',
                    'full_name' => 'Apparel',
                    'slug' => 'apparel',
                    'popularity' => 0,
                    'parent_id' => NULL,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            1 =>
                array(
                    'id' => 2,
                    'name' => 'Men',
                    'full_name' => 'Apparel > Men',
                    'slug' => 'apparel-men',
                    'popularity' => 1,
                    'parent_id' => 1,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            2 =>
                array(
                    'id' => 3,
                    'name' => 'T-Shirts',
                    'full_name' => 'Apparel > Men > T-Shirts',
                    'slug' => 'apparel-men-t-shirts',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            3 =>
                array(
                    'id' => 4,
                    'name' => 'Classic Tees',
                    'full_name' => 'Apparel > Men > T-Shirts > Classic Tees',
                    'slug' => 'apparel-men-t-shirts-classic-tees',
                    'popularity' => 1,
                    'parent_id' => 3,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            4 =>
                array(
                    'id' => 5,
                    'name' => 'V-Neck Shirts',
                    'full_name' => 'Apparel > Men > T-Shirts > V-Neck Shirts',
                    'slug' => 'apparel-men-t-shirts-v-neck-shirts',
                    'popularity' => 1,
                    'parent_id' => 3,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            5 =>
                array(
                    'id' => 6,
                    'name' => 'Premium Fitted Tees',
                    'full_name' => 'Apparel > Men > T-Shirts > Premium Fitted Tees',
                    'slug' => 'apparel-men-t-shirts-premium-fitted-tees',
                    'popularity' => 1,
                    'parent_id' => 3,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            6 =>
                array(
                    'id' => 7,
                    'name' => 'All Over Unisex Shirts',
                    'full_name' => 'Apparel > Men > T-Shirts > All Over Unisex Shirts',
                    'slug' => 'apparel-men-t-shirts-all-over-unisex-shirts',
                    'popularity' => 1,
                    'parent_id' => 3,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            7 =>
                array(
                    'id' => 8,
                    'name' => 'Classic Polo',
                    'full_name' => 'Apparel > Men > T-Shirts > Classic Polo',
                    'slug' => 'apparel-men-t-shirts-classic-polo',
                    'popularity' => 1,
                    'parent_id' => 3,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            8 =>
                array(
                    'id' => 9,
                    'name' => 'Long Sleeves',
                    'full_name' => 'Apparel > Men > Long Sleeves',
                    'slug' => 'apparel-men-long-sleeves',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            9 =>
                array(
                    'id' => 10,
                    'name' => 'Lightweight Jacket',
                    'full_name' => 'Apparel > Men > Long Sleeves > Lightweight Jacket',
                    'slug' => 'apparel-men-long-sleeves-lightweight-jacket',
                    'popularity' => 1,
                    'parent_id' => 9,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            10 =>
                array(
                    'id' => 11,
                    'name' => 'Dress shirt',
                    'full_name' => 'Apparel > Men > Long Sleeves > Dress shirt',
                    'slug' => 'apparel-men-long-sleeves-dress-shirt',
                    'popularity' => 1,
                    'parent_id' => 9,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            11 =>
                array(
                    'id' => 12,
                    'name' => 'Tank Tops',
                    'full_name' => 'Apparel > Men > Tank Tops',
                    'slug' => 'apparel-men-tank-tops',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            12 =>
                array(
                    'id' => 13,
                    'name' => 'Unisex Tanks',
                    'full_name' => 'Apparel > Men > Tank Tops > Unisex Tanks',
                    'slug' => 'apparel-men-tank-tops-unisex-tanks',
                    'popularity' => 1,
                    'parent_id' => 12,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            13 =>
                array(
                    'id' => 14,
                    'name' => 'All Over Unisex Tanks',
                    'full_name' => 'Apparel > Men > Tank Tops > All Over Unisex Tanks',
                    'slug' => 'apparel-men-tank-tops-all-over-unisex-tanks',
                    'popularity' => 1,
                    'parent_id' => 12,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            14 =>
                array(
                    'id' => 15,
                    'name' => 'Footwear',
                    'full_name' => 'Apparel > Men > Footwear',
                    'slug' => 'apparel-men-footwear',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            15 =>
                array(
                    'id' => 16,
                    'name' => 'Socks',
                    'full_name' => 'Apparel > Men > Footwear > Socks',
                    'slug' => 'apparel-men-footwear-socks',
                    'popularity' => 1,
                    'parent_id' => 15,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            16 =>
                array(
                    'id' => 17,
                    'name' => 'Men\'s Shoes',
                    'full_name' => 'Apparel > Men > Footwear > Men\'s Shoes',
                    'slug' => 'apparel-men-footwear-mens-shoes',
                    'popularity' => 1,
                    'parent_id' => 15,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            17 =>
                array(
                    'id' => 18,
                    'name' => 'Hoodies',
                    'full_name' => 'Apparel > Men > Hoodies',
                    'slug' => 'apparel-men-hoodies',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            18 =>
                array(
                    'id' => 19,
                    'name' => 'Sweatshirts',
                    'full_name' => 'Apparel > Men > Sweatshirts',
                    'slug' => 'apparel-men-sweatshirts',
                    'popularity' => 1,
                    'parent_id' => 2,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            19 =>
                array(
                    'id' => 20,
                    'name' => 'Women',
                    'full_name' => 'Apparel > Women',
                    'slug' => 'apparel-women',
                    'popularity' => 1,
                    'parent_id' => 1,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            20 =>
                array(
                    'id' => 21,
                    'name' => 'T-Shirts',
                    'full_name' => 'Apparel > Women > T-Shirts',
                    'slug' => 'apparel-women-t-shirts',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            21 =>
                array(
                    'id' => 22,
                    'name' => 'Ladies Tees',
                    'full_name' => 'Apparel > Women > T-Shirts > Ladies Tees',
                    'slug' => 'apparel-women-t-shirts-ladies-tees',
                    'popularity' => 1,
                    'parent_id' => 21,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            22 =>
                array(
                    'id' => 23,
                    'name' => 'Premium Fitted Ladies Tees',
                    'full_name' => 'Apparel > Women > T-Shirts > Premium Fitted Ladies Tees',
                    'slug' => 'apparel-women-t-shirts-premium-fitted-ladies-tees',
                    'popularity' => 1,
                    'parent_id' => 21,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            23 =>
                array(
                    'id' => 24,
                    'name' => 'All Over Unisex Shirts',
                    'full_name' => 'Apparel > Women > T-Shirts > All Over Unisex Shirts',
                    'slug' => 'apparel-women-t-shirts-all-over-unisex-shirts',
                    'popularity' => 1,
                    'parent_id' => 21,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            24 =>
                array(
                    'id' => 25,
                    'name' => 'Footwear',
                    'full_name' => 'Apparel > Women > Footwear',
                    'slug' => 'apparel-women-footwear',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            25 =>
                array(
                    'id' => 26,
                    'name' => 'Socks',
                    'full_name' => 'Apparel > Women > Footwear > Socks',
                    'slug' => 'apparel-women-footwear-socks',
                    'popularity' => 1,
                    'parent_id' => 25,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            26 =>
                array(
                    'id' => 27,
                    'name' => 'Women\'s Shoes',
                    'full_name' => 'Apparel > Women > Footwear > Women\'s Shoes',
                    'slug' => 'apparel-women-footwear-womens-shoes',
                    'popularity' => 1,
                    'parent_id' => 25,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            27 =>
                array(
                    'id' => 28,
                    'name' => 'Tank Tops',
                    'full_name' => 'Apparel > Women > Tank Tops',
                    'slug' => 'apparel-women-tank-tops',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            28 =>
                array(
                    'id' => 29,
                    'name' => 'Ladies Flowy Tanks',
                    'full_name' => 'Apparel > Women > Tank Tops > Ladies Flowy Tanks',
                    'slug' => 'apparel-women-tank-tops-ladies-flowy-tanks',
                    'popularity' => 1,
                    'parent_id' => 28,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            29 =>
                array(
                    'id' => 30,
                    'name' => 'Unisex Tanks',
                    'full_name' => 'Apparel > Women > Tank Tops > Unisex Tanks',
                    'slug' => 'apparel-women-tank-tops-unisex-tanks',
                    'popularity' => 1,
                    'parent_id' => 28,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            30 =>
                array(
                    'id' => 31,
                    'name' => 'All Over Unisex Tanks',
                    'full_name' => 'Apparel > Women > Tank Tops > All Over Unisex Tanks',
                    'slug' => 'apparel-women-tank-tops-all-over-unisex-tanks',
                    'popularity' => 1,
                    'parent_id' => 28,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            31 =>
                array(
                    'id' => 32,
                    'name' => 'Long Sleeves',
                    'full_name' => 'Apparel > Women > Long Sleeves',
                    'slug' => 'apparel-women-long-sleeves',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            32 =>
                array(
                    'id' => 33,
                    'name' => 'Hoodies',
                    'full_name' => 'Apparel > Women > Hoodies',
                    'slug' => 'apparel-women-hoodies',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            33 =>
                array(
                    'id' => 34,
                    'name' => 'Sweatshirts',
                    'full_name' => 'Apparel > Women > Sweatshirts',
                    'slug' => 'apparel-women-sweatshirts',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            34 =>
                array(
                    'id' => 35,
                    'name' => 'Dresses',
                    'full_name' => 'Apparel > Women > Dresses',
                    'slug' => 'apparel-women-dresses',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            35 =>
                array(
                    'id' => 36,
                    'name' => 'Leggings',
                    'full_name' => 'Apparel > Women > Leggings',
                    'slug' => 'apparel-women-leggings',
                    'popularity' => 1,
                    'parent_id' => 20,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            36 =>
                array(
                    'id' => 37,
                    'name' => 'Youth & Baby',
                    'full_name' => 'Apparel > Youth & Baby',
                    'slug' => 'apparel-youth-baby',
                    'popularity' => 1,
                    'parent_id' => 1,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            37 =>
                array(
                    'id' => 38,
                    'name' => 'Youth T-Shirts',
                    'full_name' => 'Apparel > Youth & Baby > Youth T-Shirts',
                    'slug' => 'apparel-youth-baby-youth-t-shirts',
                    'popularity' => 1,
                    'parent_id' => 37,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            38 =>
                array(
                    'id' => 39,
                    'name' => 'Onesies',
                    'full_name' => 'Apparel > Youth & Baby > Onesies',
                    'slug' => 'apparel-youth-baby-onesies',
                    'popularity' => 1,
                    'parent_id' => 37,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            39 =>
                array(
                    'id' => 40,
                    'name' => 'Home & Living',
                    'full_name' => 'Home & Living',
                    'slug' => 'home-living',
                    'popularity' => 1,
                    'parent_id' => NULL,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            40 =>
                array(
                    'id' => 41,
                    'name' => 'Bath and Towels',
                    'full_name' => 'Home & Living > Bath and Towels',
                    'slug' => 'home-living-bath-and-towels',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            41 =>
                array(
                    'id' => 42,
                    'name' => 'Shower Curtains',
                    'full_name' => 'Home & Living > Bath and Towels > Shower Curtains',
                    'slug' => 'home-living-bath-and-towels-shower-curtains',
                    'popularity' => 1,
                    'parent_id' => 41,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            42 =>
                array(
                    'id' => 43,
                    'name' => 'Bath Mats',
                    'full_name' => 'Home & Living > Bath and Towels > Bath Mats',
                    'slug' => 'home-living-bath-and-towels-bath-mats',
                    'popularity' => 1,
                    'parent_id' => 41,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            43 =>
                array(
                    'id' => 44,
                    'name' => 'Bath Towels',
                    'full_name' => 'Home & Living > Bath and Towels > Bath Towels',
                    'slug' => 'home-living-bath-and-towels-bath-towels',
                    'popularity' => 1,
                    'parent_id' => 41,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            44 =>
                array(
                    'id' => 45,
                    'name' => 'Beach Towels',
                    'full_name' => 'Home & Living > Bath and Towels > Beach Towels',
                    'slug' => 'home-living-bath-and-towels-beach-towels',
                    'popularity' => 1,
                    'parent_id' => 41,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            45 =>
                array(
                    'id' => 46,
                    'name' => 'Tea Towels',
                    'full_name' => 'Home & Living > Bath and Towels > Tea Towels',
                    'slug' => 'home-living-bath-and-towels-tea-towels',
                    'popularity' => 1,
                    'parent_id' => 41,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            46 =>
                array(
                    'id' => 47,
                    'name' => 'Pillows and Bedding',
                    'full_name' => 'Home & Living > Pillows and Bedding',
                    'slug' => 'home-living-pillows-and-bedding',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            47 =>
                array(
                    'id' => 48,
                    'name' => 'Decorative Pillows',
                    'full_name' => 'Home & Living > Pillows and Bedding > Decorative Pillows',
                    'slug' => 'home-living-pillows-and-bedding-decorative-pillows',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            48 =>
                array(
                    'id' => 49,
                    'name' => 'Duvet Covers',
                    'full_name' => 'Home & Living > Pillows and Bedding > Duvet Covers',
                    'slug' => 'home-living-pillows-and-bedding-duvet-covers',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            49 =>
                array(
                    'id' => 50,
                    'name' => 'Comforters',
                    'full_name' => 'Home & Living > Pillows and Bedding > Comforters',
                    'slug' => 'home-living-pillows-and-bedding-comforters',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            50 =>
                array(
                    'id' => 51,
                    'name' => 'Fleece Blankets',
                    'full_name' => 'Home & Living > Pillows and Bedding > Fleece Blankets',
                    'slug' => 'home-living-pillows-and-bedding-fleece-blankets',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            51 =>
                array(
                    'id' => 52,
                    'name' => 'Pillow Shams',
                    'full_name' => 'Home & Living > Pillows and Bedding > Pillow Shams',
                    'slug' => 'home-living-pillows-and-bedding-pillow-shams',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            52 =>
                array(
                    'id' => 53,
                    'name' => 'Pillowcases',
                    'full_name' => 'Home & Living > Pillows and Bedding > Pillowcases',
                    'slug' => 'home-living-pillows-and-bedding-pillowcases',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            53 =>
                array(
                    'id' => 54,
                    'name' => 'Quilts',
                    'full_name' => 'Home & Living > Pillows and Bedding > Quilts',
                    'slug' => 'home-living-pillows-and-bedding-quilts',
                    'popularity' => 1,
                    'parent_id' => 47,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            54 =>
                array(
                    'id' => 55,
                    'name' => 'Wall Decoration',
                    'full_name' => 'Home & Living > Wall Decoration',
                    'slug' => 'home-living-wall-decoration',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            55 =>
                array(
                    'id' => 56,
                    'name' => 'Posters',
                    'full_name' => 'Home & Living > Wall Decoration > Posters',
                    'slug' => 'home-living-wall-decoration-posters',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            56 =>
                array(
                    'id' => 57,
                    'name' => 'Wall Tapestries',
                    'full_name' => 'Home & Living > Wall Decoration > Wall Tapestries',
                    'slug' => 'home-living-wall-decoration-wall-tapestries',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            57 =>
                array(
                    'id' => 58,
                    'name' => 'Window Curtains',
                    'full_name' => 'Home & Living > Wall Decoration > Window Curtains',
                    'slug' => 'home-living-wall-decoration-window-curtains',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            58 =>
                array(
                    'id' => 59,
                    'name' => 'Gallery Wrapped Canvas Prints',
                    'full_name' => 'Home & Living > Wall Decoration > Gallery Wrapped Canvas Prints',
                    'slug' => 'home-living-wall-decoration-gallery-wrapped-canvas-prints',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            59 =>
                array(
                    'id' => 60,
                    'name' => 'Floating Framed Canvas Prints White',
                    'full_name' => 'Home & Living > Wall Decoration > Floating Framed Canvas Prints White',
                    'slug' => 'home-living-wall-decoration-floating-framed-canvas-prints-white',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            60 =>
                array(
                    'id' => 61,
                    'name' => 'Floating Framed Canvas Prints Black',
                    'full_name' => 'Home & Living > Wall Decoration > Floating Framed Canvas Prints Black',
                    'slug' => 'home-living-wall-decoration-floating-framed-canvas-prints-black',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            61 =>
                array(
                    'id' => 62,
                    'name' => 'Hanging Canvas',
                    'full_name' => 'Home & Living > Wall Decoration > Hanging Canvas',
                    'slug' => 'home-living-wall-decoration-hanging-canvas',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            62 =>
                array(
                    'id' => 63,
                    'name' => 'Easel-Back Gallery Wrapped Canvas',
                    'full_name' => 'Home & Living > Wall Decoration > Easel-Back Gallery Wrapped Canvas',
                    'slug' => 'home-living-wall-decoration-easel-back-gallery-wrapped-canvas',
                    'popularity' => 1,
                    'parent_id' => 55,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            63 =>
                array(
                    'id' => 64,
                    'name' => 'Kitchen and Dining',
                    'full_name' => 'Home & Living > Kitchen and Dining',
                    'slug' => 'home-living-kitchen-and-dining',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            64 =>
                array(
                    'id' => 65,
                    'name' => 'Placemats',
                    'full_name' => 'Home & Living > Kitchen and Dining > Placemats',
                    'slug' => 'home-living-kitchen-and-dining-placemats',
                    'popularity' => 1,
                    'parent_id' => 64,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            65 =>
                array(
                    'id' => 66,
                    'name' => 'Aprons',
                    'full_name' => 'Home & Living > Kitchen and Dining > Aprons',
                    'slug' => 'home-living-kitchen-and-dining-aprons',
                    'popularity' => 1,
                    'parent_id' => 64,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            66 =>
                array(
                    'id' => 67,
                    'name' => 'Woven Rugs',
                    'full_name' => 'Home & Living > Kitchen and Dining > Woven Rugs',
                    'slug' => 'home-living-kitchen-and-dining-woven-rugs',
                    'popularity' => 1,
                    'parent_id' => 64,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            67 =>
                array(
                    'id' => 68,
                    'name' => 'Runners',
                    'full_name' => 'Home & Living > Kitchen and Dining > Runners',
                    'slug' => 'home-living-kitchen-and-dining-runners',
                    'popularity' => 1,
                    'parent_id' => 64,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            68 =>
                array(
                    'id' => 69,
                    'name' => 'Puzzles',
                    'full_name' => 'Home & Living > Puzzles',
                    'slug' => 'home-living-puzzles',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            69 =>
                array(
                    'id' => 70,
                    'name' => 'Magnets',
                    'full_name' => 'Home & Living > Magnets',
                    'slug' => 'home-living-magnets',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            70 =>
                array(
                    'id' => 71,
                    'name' => 'Coasters',
                    'full_name' => 'Home & Living > Coasters',
                    'slug' => 'home-living-coasters',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            71 =>
                array(
                    'id' => 72,
                    'name' => 'Cutting Boards',
                    'full_name' => 'Home & Living > Cutting Boards',
                    'slug' => 'home-living-cutting-boards',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            72 =>
                array(
                    'id' => 73,
                    'name' => 'Pet Beds',
                    'full_name' => 'Home & Living > Pet Beds',
                    'slug' => 'home-living-pet-beds',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            73 =>
                array(
                    'id' => 74,
                    'name' => 'Yard Signs',
                    'full_name' => 'Home & Living > Yard Signs',
                    'slug' => 'home-living-yard-signs',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            74 =>
                array(
                    'id' => 75,
                    'name' => 'Drinkware',
                    'full_name' => 'Home & Living > Drinkware',
                    'slug' => 'home-living-drinkware',
                    'popularity' => 1,
                    'parent_id' => 40,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            75 =>
                array(
                    'id' => 76,
                    'name' => 'Mugs',
                    'full_name' => 'Home & Living > Drinkware > Mugs',
                    'slug' => 'home-living-drinkware-mugs',
                    'popularity' => 1,
                    'parent_id' => 75,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            76 =>
                array(
                    'id' => 77,
                    'name' => 'Color Changing Mugs',
                    'full_name' => 'Home & Living > Drinkware > Color Changing Mugs',
                    'slug' => 'home-living-drinkware-color-changing-mugs',
                    'popularity' => 1,
                    'parent_id' => 75,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            77 =>
                array(
                    'id' => 78,
                    'name' => 'Accessories',
                    'full_name' => 'Accessories',
                    'slug' => 'accessories',
                    'popularity' => 1,
                    'parent_id' => NULL,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            78 =>
                array(
                    'id' => 79,
                    'name' => 'Bags',
                    'full_name' => 'Accessories > Bags',
                    'slug' => 'accessories-bags',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            79 =>
                array(
                    'id' => 80,
                    'name' => 'Drawstring Bags',
                    'full_name' => 'Accessories > Bags > Drawstring Bags',
                    'slug' => 'accessories-bags-drawstring-bags',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            80 =>
                array(
                    'id' => 81,
                    'name' => 'Tote Bags',
                    'full_name' => 'Accessories > Bags > Tote Bags',
                    'slug' => 'accessories-bags-tote-bags',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            81 =>
                array(
                    'id' => 82,
                    'name' => 'Backpack',
                    'full_name' => 'Accessories > Bags > Backpack',
                    'slug' => 'accessories-bags-backpack',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            82 =>
                array(
                    'id' => 83,
                    'name' => 'Sling Pack',
                    'full_name' => 'Accessories > Bags > Sling Pack',
                    'slug' => 'accessories-bags-sling-pack',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            83 =>
                array(
                    'id' => 84,
                    'name' => 'Accessory Pouches',
                    'full_name' => 'Accessories > Bags > Accessory Pouches',
                    'slug' => 'accessories-bags-accessory-pouches',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            84 =>
                array(
                    'id' => 85,
                    'name' => 'Weekender Tote',
                    'full_name' => 'Accessories > Bags > Weekender Tote',
                    'slug' => 'accessories-bags-weekender-tote',
                    'popularity' => 1,
                    'parent_id' => 79,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            85 =>
                array(
                    'id' => 86,
                    'name' => 'Hats',
                    'full_name' => 'Accessories > Hats',
                    'slug' => 'accessories-hats',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            86 =>
                array(
                    'id' => 87,
                    'name' => 'Embroidered Hat',
                    'full_name' => 'Accessories > Hats > Embroidered Hat',
                    'slug' => 'accessories-hats-embroidered-hat',
                    'popularity' => 1,
                    'parent_id' => 86,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            87 =>
                array(
                    'id' => 88,
                    'name' => 'Knit Beanie',
                    'full_name' => 'Accessories > Hats > Knit Beanie',
                    'slug' => 'accessories-hats-knit-beanie',
                    'popularity' => 1,
                    'parent_id' => 86,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            88 =>
                array(
                    'id' => 89,
                    'name' => 'Phone Cases',
                    'full_name' => 'Accessories > Phone Cases',
                    'slug' => 'accessories-phone-cases',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            89 =>
                array(
                    'id' => 90,
                    'name' => 'Mousepads',
                    'full_name' => 'Accessories > Mousepads',
                    'slug' => 'accessories-mousepads',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            90 =>
                array(
                    'id' => 91,
                    'name' => 'Ties',
                    'full_name' => 'Accessories > Ties',
                    'slug' => 'accessories-ties',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            91 =>
                array(
                    'id' => 92,
                    'name' => 'Masks',
                    'full_name' => 'Accessories > Masks',
                    'slug' => 'accessories-masks',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            92 =>
                array(
                    'id' => 93,
                    'name' => 'Neck Gaiter',
                    'full_name' => 'Accessories > Neck Gaiter',
                    'slug' => 'accessories-neck-gaiter',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            93 =>
                array(
                    'id' => 94,
                    'name' => 'Yoga Mat',
                    'full_name' => 'Accessories > Yoga Mat',
                    'slug' => 'accessories-yoga-mat',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            94 =>
                array(
                    'id' => 95,
                    'name' => 'Stickers',
                    'full_name' => 'Accessories > Stickers',
                    'slug' => 'accessories-stickers',
                    'popularity' => 1,
                    'parent_id' => 78,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            95 =>
                array(
                    'id' => 96,
                    'name' => 'Jewelry',
                    'full_name' => 'Jewelry',
                    'slug' => 'jewelry',
                    'popularity' => 1,
                    'parent_id' => NULL,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            96 =>
                array(
                    'id' => 97,
                    'name' => 'Necklaces',
                    'full_name' => 'Jewelry > Necklaces',
                    'slug' => 'jewelry-necklaces',
                    'popularity' => 1,
                    'parent_id' => 96,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            97 =>
                array(
                    'id' => 98,
                    'name' => 'Cord Necklaces',
                    'full_name' => 'Jewelry > Necklaces > Cord Necklaces',
                    'slug' => 'jewelry-necklaces-cord-necklaces',
                    'popularity' => 1,
                    'parent_id' => 97,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            98 =>
                array(
                    'id' => 99,
                    'name' => 'Metallic Necklaces',
                    'full_name' => 'Jewelry > Necklaces > Metallic Necklaces',
                    'slug' => 'jewelry-necklaces-metallic-necklaces',
                    'popularity' => 1,
                    'parent_id' => 97,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            99 =>
                array(
                    'id' => 100,
                    'name' => 'Earrings',
                    'full_name' => 'Jewelry > Earrings',
                    'slug' => 'jewelry-earrings',
                    'popularity' => 1,
                    'parent_id' => 96,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
            100 =>
                array(
                    'id' => 101,
                    'name' => 'Bracelets',
                    'full_name' => 'Jewelry > Bracelets',
                    'slug' => 'jewelry-bracelets',
                    'popularity' => 1,
                    'parent_id' => 96,
                    'created_at' => '2020-09-12 15:42:47',
                    'updated_at' => '2020-09-12 15:42:47',
                ),
        ));
    }
}
