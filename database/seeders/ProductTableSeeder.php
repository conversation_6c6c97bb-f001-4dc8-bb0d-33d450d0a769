<?php

namespace Database\Seeders;

use App\Models\Product;
use Illuminate\Database\Seeder;

class ProductTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Product::query()->insert([
            'name' => 'test template',
            'name_ts' => null,
            'options' => null,
            'print_spaces' => null,
            'base_cost' => 5,
            'price' => 10,
            'old_price' => 8,
            'shipping_cost' => 5,
            'product_type' => 'template',
            'campaign_id' => null,
            'template_id' => null,
        ]);
        Product::factory()->count(200)->create();
    }
}
