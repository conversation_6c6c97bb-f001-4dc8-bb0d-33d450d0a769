<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\SellerCustomer;
use Illuminate\Database\Seeder;

class SellerCustomerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $sellerId = 1;
        $customers = Order::query()
            ->select(['customer_id'])
            ->groupBy('customer_id')
            ->where('seller_id', $sellerId)
            ->get();

        if ($customers->count() > 0) {
            $data = [];

            foreach ($customers as $customer) {
                $data[] = [
                    'seller_id' => $sellerId,
                    'customer_id' => $customer->customer_id,
                    'total_orders' => 1,
                    'total_purchases' => 1
                ];
            }

            SellerCustomer::query()->insert($data);
        }
    }
}
