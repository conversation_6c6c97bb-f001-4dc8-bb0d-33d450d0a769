<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SystemConfigTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('system_config')->insert(array(
            0 =>
                array(
                    'id' => 1,
                    'key' => 'payout_minimum',
                    'value' => 100,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
            1 =>
                array(
                    'id' => 2,
                    'key' => 'payout_maximum',
                    'value' => 10000,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
            2 =>
                array(
                    'id' => 3,
                    'key' => 'options_limit',
                    'value' => 3,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
            3 =>
                array(
                    'id' => 4,
                    'key' => 'print_spaces_limit',
                    'value' => 5,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
            4 =>
                array(
                    'id' => 5,
                    'key' => 'base_rate',
                    'value' => 30,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
            5 =>
                array(
                    'id' => 6,
                    'key' => 'processing_fee',
                    'value' => 5,
                    'status' => 1,
                    'created_at' => NULL,
                    'updated_at' => NULL,
                ),
        ));
    }
}
