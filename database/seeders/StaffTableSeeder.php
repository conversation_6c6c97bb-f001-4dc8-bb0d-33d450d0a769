<?php

namespace Database\Seeders;

use App\Models\Staff;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class StaffTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        try {
            DB::beginTransaction();

//            if (app()->environment('production')) {
//                Staff::query()
//                    ->create([
//                        'name' => 'System Account',
//                        'email' => '<EMAIL>',
//                        'password' => Hash::make('kBpX8JgB2b3')
//                    ])
//                    ->assignRole('admin');
//            } else {

            $password = Hash::make('********');

            //  create first admin
            $admins = [
                ['name' => 'Hải', 'email' => '<EMAIL>', 'password' => $password],
                ['name' => 'Đôn', 'email' => '<EMAIL>', 'password' => $password],
                ['name' => 'Huân', 'email' => '<EMAIL>', 'password' => $password],
                ['name' => 'Mạnh Tuấn', 'email' => '<EMAIL>', 'password' => $password],
                ['name' => 'Chánh', 'email' => '<EMAIL>', 'password' => $password]
            ];

            foreach ($admins as $user) {
                Staff::query()
                    ->create($user)
                    ->assignRole('admin');
            }
            // add factory account admin
            $guardAdminRoles = [
                'accounter',
                'supporter'
            ];

            Staff::factory()
                ->count(10)
                ->create()
                ->each(function ($user) use ($guardAdminRoles) {
                    $randRoleKey = array_rand($guardAdminRoles);
                    $user->assignRole($guardAdminRoles[$randRoleKey]);
                });
            //}

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
        }
    }
}
