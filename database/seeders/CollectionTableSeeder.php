<?php

namespace Database\Seeders;

use App\Models\Collection;
use App\Models\Product;
use App\Models\ProductCollection;
use App\Models\SellerCollection;
use App\Models\Store;
use App\Models\StoreCollection;
use App\Models\User;
use Faker;
use Illuminate\Database\Seeder;

class CollectionTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker\Factory::create();


        $products = [];
        $campaigns = [];
        $stores = [];
        $userId = User::role('seller')->value('id');
        $productId = Product::value('id');
        $storeId = Store::value('id');

        for ($i = 1; $i <= 30; $i++) {
            $collection = new Collection();
            $collection->slug = $faker->slug();
            $collection->name = $faker->words(2, true);

            if ($collection->save()) {
                $collectionId = $collection->id;

                // Add to product_collection
                $products[] = [
                    'product_id' => $productId,
                    'seller_id' => $userId,
                    'collection_id' => $collectionId
                ];

                // Add to seller_collection
                $campaigns[] = [
                    'seller_id' => $userId,
                    'collection_id' => $collectionId
                ];

                // Add to store_collection
                $stores[] = [
                    'store_id' => $storeId,
                    'collection_id' => $collectionId
                ];
            }
        }

        if (count($products) > 0) {
            ProductCollection::query()->insert($products);
        }

        if (count($campaigns) > 0) {
            SellerCollection::query()->insert($campaigns);
        }

        if (count($stores) > 0) {
            StoreCollection::query()->insert($stores);
        }
    }
}
