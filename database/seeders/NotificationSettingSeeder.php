<?php

namespace Database\Seeders;

use App\Http\Controllers\NotificationSettingController;
use App\Models\Store;
use Illuminate\Database\Seeder;

class NotificationSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $stores = Store::query()
            ->select(['id', 'seller_id'])
            ->inRandomOrder()
            ->get();

        if ($stores->count() === 0) {
            return;
        }

        foreach ($stores as $store) {
            NotificationSettingController::initAbandonedConfigs($store->id, $store->seller_id, NotificationChannelEnum::EMAIL);
            NotificationSettingController::initAbandonedConfigs($store->id, $store->seller_id, NotificationChannelEnum::SMS);
        }
    }
}
