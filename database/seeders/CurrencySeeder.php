<?php

namespace Database\Seeders;

use App\Models\Currency;
use Illuminate\Database\Seeder;

class CurrencySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Currency::query()->insert([
            [
                'code' => 'USD',
                'locale' => 'en-US',
                'name' => 'US Dollar',
                'rate' => 1,
                'settable' => 1,
            ],
            [
                'code' => 'EUR',
                'locale' => 'en-gb',
                'name' => 'Euro',
                'rate' => 0.83,
                'settable' => 1,
            ],
            [
                'code' => 'VND',
                'locale' => 'vi-VN',
                'name' => 'Vietnam Dong',
                'rate' => 23000,
                'settable' => 0,
            ]
        ]);
    }
}
