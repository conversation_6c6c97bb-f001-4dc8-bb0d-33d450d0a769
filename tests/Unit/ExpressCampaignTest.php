<?php

namespace Tests\Unit;

use App\Enums\CampaignRenderModeEnum;
use Tests\TestCase;
use App\Models\File;
use Illuminate\Support\Collection;
use App\Services\ExpressMockupRenderService;
use Mo<PERSON>y\MockInterface;

class ExpressCampaignTest extends TestCase
{
    protected ExpressMockupRenderService $service;
    protected Collection $mockups;
    protected $templateId;

    /** @return void  */
    protected function setUp(): void {
        parent::setUp();
        $this->service = new ExpressMockupRenderService();
    }

    public function test_generate_mockups_express_campaign()
    {
        $mockup = $this->partialMock(File::class, function (MockInterface $mock) {
            $mock->shouldReceive('getAttribute')->with('file_url')->andReturn('path/to/mockup.png');
            $mock->shouldReceive('getAttribute')->with('file_url_2')->andReturn('path/to/mockup.png');
            $mock->shouldReceive('getAttribute')->with('design_json')->andReturn(json_encode([
                "color" => "path/to/color.png",
                "crop" => "path/to/crop.png",
                "shadow" => "path/to/shadow.png",
                "expressOptions" => [
                    "width" => 100,
                    "height" => 100,
                    "x" => 0,
                    "y" => 0
                ]
            ]));
        });

        $design = $this->partialMock(File::class);
        $design->file_url = 'path/to/design.png';
        $design->file_url_2 = 'path/to/design.png';

        # Pick random CampaignRenderModeEnum
        $renderModes = CampaignRenderModeEnum::getValues();
        $renderMode = $renderModes[array_rand($renderModes)];

        $this->assertNotNull($design, 'Design not found');
        $mockupPath = $this->service->prepareOptionsForGenerateImageUrl($mockup, $design, '000000', $renderMode);
        $this->assertNotNull($mockupPath, 'Mockup URL not found');
        $this->assertNotEmpty($mockupPath, 'Mockup URL is empty');
        $expectedString = "u_path:to:mockup";
        $this->assertStringContainsString($expectedString, $mockupPath, 'Mockup URL is invalid');
        return imgUrl($mockupPath);
    }
}
