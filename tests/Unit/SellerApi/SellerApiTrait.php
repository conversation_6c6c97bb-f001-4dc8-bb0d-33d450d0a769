<?php

namespace Tests\Unit\SellerApi;

use App\Models\User;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class SellerApiTrait extends TestCase
{
    use DatabaseTransactions;

    protected User $seller;

    public function setUp(): void
    {
        parent::setUp();
        $this->seller = User::query()
            ->create([
                'name'     => 'Test Seller',
                'password' => bcrypt('password'),
                'email'    => time() . '<EMAIL>',
            ]);


        $this->actingAsSellerExternal($this->seller);
    }
}
