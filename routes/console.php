<?php

use App\Enums\CacheKeys;
use App\Models\SystemConfig;
use Illuminate\Support\Facades\Artisan;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('schedule-health', function () {
    $result = SystemConfig::setConfig(CacheKeys::LAST_DATETIME_SCHEDULE_RUN, array(
        'value' => now()->toDateTimeString(),
        'status' => 1
    ));
    if ($result) {
        $this->info('OK');
        return;
    }
    $this->error('NOT OK');
})->purpose('Check the schedule health');
